/* eslint-disable no-underscore-dangle */
/* eslint-disable no-param-reassign */
/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * Configures the default icons for Leaflet maps.
 * This function dynamically imports Leaflet only on the client-side to avoid SSR issues,
 * as Leaflet is a browser-only library. It also applies a workaround for a common
 * issue with Webpack where default icon paths are not resolved correctly.
 */
export const configureLeafletDefaultIcons = () => {
  // Ensure this code runs only in the browser
  if (typeof window !== 'undefined') {
    // Dynamically import Leaflet
    import('leaflet').then((L) => {
      // Workaround for default icon path issue with Webpack
      delete (L.Icon.Default.prototype as any)._getIconUrl;

      // Set the correct paths for the default icons
      L.Icon.Default.mergeOptions({
        iconRetinaUrl: '/images/leaflet/marker-icon-2x.png',
        iconUrl: '/images/leaflet/marker-icon.png',
        shadowUrl: '/images/leaflet/marker-shadow.png',
      });
    });
  }
};
