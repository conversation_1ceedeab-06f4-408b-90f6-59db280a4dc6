import { z } from 'zod';
import { myShipmentsApiResponseSchema } from './response-transformer';
import { Pagination } from '../../types';

export type MyShipmentsApiResponse = z.infer<typeof myShipmentsApiResponseSchema>;

export type Filter = {
  status?: string | null;
  createdAtGte?: string | null;
  createdAtLte?: string | null;
  updatedAtGte?: string | null;
  updatedAtLte?: string | null;
};

export interface getMyShipmentsQueryProps {
  pagination?: Pagination;
  filters?: Filter;
  search?: string;
  sort?: {
    sortBy: string | undefined;
    sortOrder: 'asc' | 'desc';
  };
}
