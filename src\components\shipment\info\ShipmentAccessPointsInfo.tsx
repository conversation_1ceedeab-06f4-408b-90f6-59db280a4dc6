/* eslint-disable react/require-default-props */
import React from 'react';
import {
  Grid,
  Text,
  Group,
  Loader,
  Stack,
  Badge,
  Alert,
} from '@mantine/core';
import {
  IconTruck,
  IconFlag,
  IconMapPin,
  IconPhone,
  IconMail,
  IconClock,
  IconBuilding,
} from '@tabler/icons-react';
import useTranslation from 'next-translate/useTranslation';
import { useQuery } from '@tanstack/react-query';
import { AccessPoint, getAccessPointQuery } from '../../../requests';
import { Shipment } from '../../../requests/shipment';

interface ShipmentAccessPointsInfoProps {
  shipment: Shipment;
}

interface AccessPointDisplayProps {
  accessPoint: AccessPoint | null;
  isLoading: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  error: any;
  type: 'origin' | 'destination';
}

// eslint-disable-next-line complexity
function AccessPointDisplay({
  accessPoint,
  isLoading,
  error,
  type,
}: AccessPointDisplayProps) {
  const { t } = useTranslation('shipments');
  const isOrigin = type === 'origin';
  const icon = isOrigin ? <IconTruck size="1.2rem" color="green" /> : <IconFlag size="1.2rem" color="red" />;
  const title = isOrigin ? t('originAccessPoint') : t('destinationAccessPoint');
  const subtitle = isOrigin ? t('packageDropoffLocation') : t('packagePickupLocation');
  const badgeColor = isOrigin ? 'green' : 'red';

  if (isLoading) {
    return (
      <Stack gap="xs" p="md" style={{ border: '1px solid #e0e0e0', borderRadius: '8px', minHeight: '120px' }}>
        <Group gap="xs" mb="xs">
          {icon}
          <Text size="sm" fw={600} c="dimmed">{title}</Text>
        </Group>
        <Group gap="xs">
          <Loader size="xs" />
          <Text size="xs" c="dimmed">Loading access point details...</Text>
        </Group>
      </Stack>
    );
  }

  if (error || !accessPoint) {
    return (
      <Stack gap="xs" p="md" style={{ border: '1px solid #e0e0e0', borderRadius: '8px', minHeight: '120px' }}>
        <Group gap="xs" mb="xs">
          {icon}
          <Text size="sm" fw={600} c="dimmed">{title}</Text>
        </Group>
        <Alert color="red" variant="light">
          <Text size="xs">Failed to load access point details</Text>
        </Alert>
      </Stack>
    );
  }

  return (
    <Stack
      gap="sm"
      p="md"
      style={{
        border: `2px solid ${isOrigin ? '#51cf66' : '#ff6b6b'}`,
        borderRadius: '8px',
        backgroundColor: isOrigin ? '#f8fff8' : '#fff8f8',
        minHeight: '160px',
      }}
    >
      {/* Header */}
      <Group gap="xs" justify="space-between">
        <Group gap="xs">
          {icon}
          <div>
            <Text size="sm" fw={600} c={isOrigin ? 'green.8' : 'red.8'}>
              {title}
            </Text>
            <Text size="xs" c="dimmed" fw={500}>
              {subtitle}
            </Text>
          </div>
        </Group>
        <Badge size="sm" color={badgeColor} variant="filled" radius="md">
          {isOrigin ? t('origin').toUpperCase() : t('destination').toUpperCase()}
        </Badge>
      </Group>

      {/* Business Information */}
      <Stack gap="xs">
        <Group gap="xs">
          <IconBuilding size="0.8rem" color="gray" />
          <Text fw={600} size="sm" c="dark">
            {accessPoint.businessName || accessPoint.name}
          </Text>
        </Group>

        {accessPoint.address && (
          <Group gap="xs" align="flex-start">
            <IconMapPin size="0.8rem" color="gray" style={{ marginTop: '2px' }} />
            <Text size="xs" c="dimmed" style={{ lineHeight: 1.4 }}>
              {accessPoint.address}
            </Text>
          </Group>
        )}
      </Stack>

      {/* Contact Information */}
      <Stack gap="xs">
        {accessPoint.phone && (
          <Group gap="xs">
            <IconPhone size="0.8rem" color="blue" />
            <Text size="xs" c="blue" fw={500} style={{ fontFamily: 'monospace' }}>
              {accessPoint.phone}
            </Text>
          </Group>
        )}
        {accessPoint.email && (
          <Group gap="xs">
            <IconMail size="0.8rem" color="blue" />
            <Text size="xs" c="blue" fw={500} style={{ wordBreak: 'break-all' }}>
              {accessPoint.email}
            </Text>
          </Group>
        )}
      </Stack>

      {/* Service Information */}
      <Group gap="xs" mt="xs">
        <IconClock size="0.7rem" color="orange" />
        <Text size="xs" c="dimmed" style={{ fontStyle: 'italic' }}>
          {t('operatingHours')}
          :
          {t('operatingHoursTime')}
        </Text>
      </Group>

      {/* Status Badge */}
      <Group justify="flex-end">
        <Badge
          size="xs"
          color={accessPoint.status === 'ACTIVE' ? 'green' : 'orange'}
          variant="light"
          radius="sm"
        >
          {accessPoint.status}
        </Badge>
      </Group>
    </Stack>
  );
}

export default function ShipmentAccessPointsInfo({ shipment }: ShipmentAccessPointsInfoProps) {
  const { t } = useTranslation('shipments');

  // Fetch origin access point
  const {
    data: originData,
    isLoading: originLoading,
    error: originError,
  } = useQuery(
    getAccessPointQuery({
      id: shipment.originAoId,
      enabled: !!shipment.originAoId,
    }),
  );

  // Fetch destination access point
  const {
    data: destData,
    isLoading: destLoading,
    error: destError,
  } = useQuery(
    getAccessPointQuery({
      id: shipment.destAoId,
      enabled: !!shipment.destAoId,
    }),
  );

  // Extract access point data
  const originAccessPoint: AccessPoint | null = originData?.data?.accessOperator || null;
  const destAccessPoint: AccessPoint | null = destData?.data?.accessOperator || null;

  return (
    <Stack gap="md">
      <Grid gutter="md">
        <Grid.Col span={{ base: 12, md: 6 }}>
          <AccessPointDisplay
            accessPoint={originAccessPoint}
            isLoading={originLoading}
            error={originError}
            type="origin"
          />
        </Grid.Col>
        <Grid.Col span={{ base: 12, md: 6 }}>
          <AccessPointDisplay
            accessPoint={destAccessPoint}
            isLoading={destLoading}
            error={destError}
            type="destination"
          />
        </Grid.Col>
      </Grid>

      {/* Additional Information */}
      {(originAccessPoint && destAccessPoint) && (
        <Alert color="blue" variant="light">
          <Text size="xs">
            <strong>
              {t('important')}
              :
            </strong>
            {' '}
            {t('dropoffLocationAlert')}
          </Text>
        </Alert>
      )}
    </Stack>
  );
}
