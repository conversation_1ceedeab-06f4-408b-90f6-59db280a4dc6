import {
  Text, TextInput, Stack, Textarea, Alert,
} from '@mantine/core';
import { IconLicense, IconAlertCircle } from '@tabler/icons-react';
import useTranslation from 'next-translate/useTranslation';
import dynamic from 'next/dynamic';
import type { CarOperatorSectionProps } from './types';

// Dynamically import the map component to avoid SSR issues
const TransitPointsMap = dynamic(() => import('../maps/TransitPointsMap'), {
  ssr: false,
  loading: () => <div>Loading map...</div>,
});

export default function CarOperatorSection({
  form,
  isEditing,
  accessPointsData,
  transitPointIds,
  onTransitPointsChange,
}: CarOperatorSectionProps) {
  const { t } = useTranslation('profile');

  return (
    <div>
      <Text fw={600} mb="sm">{t('vehicleInformation')}</Text>
      <Stack gap="sm">
        <TextInput
          label={t('licenseNumber')}
          placeholder={t('enterLicenseNumber')}
          leftSection={<IconLicense size="1rem" />}
          disabled={!isEditing}
          {...form.getInputProps('licenseNumber')}
        />

        <Textarea
          label={t('vehicleInfo')}
          placeholder={t('enterVehicleInfo')}
          disabled={!isEditing}
          {...form.getInputProps('vehicleInfo')}
        />

        {/* Transit Points Map */}
        <div>
          <Text fw={500} mb="sm">{t('transitPoints')}</Text>
          {accessPointsData && accessPointsData.length > 0 ? (
            <TransitPointsMap
              accessPoints={accessPointsData}
              selectedPickupId={transitPointIds?.[0]}
              selectedDropoffId={transitPointIds?.[1]}
              onPickupSelect={(ap) => onTransitPointsChange?.([ap.id, transitPointIds?.[1] || ''])}
              onDropoffSelect={(ap) => onTransitPointsChange?.([transitPointIds?.[0] || '', ap.id])}
              isEditable={isEditing}
            />
          ) : (
            <Alert icon={<IconAlertCircle size="1rem" />} color="yellow">
              {t('noAccessPointsAvailable')}
            </Alert>
          )}
        </div>
      </Stack>
    </div>
  );
}
