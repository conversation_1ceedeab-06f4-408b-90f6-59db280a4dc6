import { useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import {
  Container,
  Stack,
  Loader,
  Center,
} from '@mantine/core';
import { useQuery } from '@tanstack/react-query';
import { getDashboardQuery } from '../../src/requests/dashboard/call';
import type {
  DashboardApiResponse,
  CarOperatorDashboardData,
} from '../../src/requests/dashboard/type';
import {
  DashboardHeader,
  QuickStatsGrid,
  QuickActions,
  RecentShipmentsList,
  VehicleStatus,
} from '../../src/components/dashboard/CarOperatorDashboardComponents';

const formatNumber = (n?: number) => (typeof n === 'number' ? n.toLocaleString() : '—');

export default function CarOperatorDashboard() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const isRTL = router.locale === 'ar';
  const {
    data: dashboard,
    isLoading: isDashboardLoading,
  } = useQuery<DashboardApiResponse>(getDashboardQuery({ enabled: status === 'authenticated' }));

  useEffect(() => {
    if (status === 'loading') return; // Still loading

    if (!session) {
      router.push('/auth/login');
      return;
    }

    // Check if user is a car operator
    if (session.user?.user_type !== 'CAR_OPERATOR') {
      router.push('/'); // Redirect to home if not a car operator
    }
  }, [session, status, router]);

  if (status === 'loading' || isDashboardLoading) {
    return (
      <Center style={{ height: '100vh' }}>
        <Loader />
      </Center>
    );
  }

  if (!session || session.user?.user_type !== 'CAR_OPERATOR') {
    return null; // Will redirect
  }

  // Extract data
  const coData = dashboard?.data?.dashboardData as CarOperatorDashboardData | undefined;
  const shipmentStats = coData?.shipmentStats ?? {};
  const recentShipments = coData?.recentShipments ?? [];

  // Extract operator info for vehicle status
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const operatorInfo = (dashboard?.data?.dashboardData as any)?.operatorInfo;

  return (
    <Container size="xl" p="xl">
      <Stack gap="xl">
        <DashboardHeader userName={session.user?.name || session.user?.email} />
        <QuickStatsGrid shipmentStats={shipmentStats} formatNumber={formatNumber} />
        <QuickActions />
        <RecentShipmentsList recentShipments={recentShipments} isRTL={isRTL} />
        <VehicleStatus operatorInfo={operatorInfo} shipmentStats={shipmentStats} formatNumber={formatNumber} />
      </Stack>
    </Container>
  );
}
