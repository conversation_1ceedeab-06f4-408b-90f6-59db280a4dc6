import { z } from 'zod';
import {
  createShipmentApiRequestSchema,
  createShipmentBackendRequestSchema,
  cancelShipmentApiRequestSchema,
  scanShipmentApiRequestSchema,
  deliverShipmentApiRequestSchema,
} from './request-transformer';
import {
  shipmentSchema,
  shipmentBackendSchema,
  getShipmentApiResponseSchema,
  createShipmentApiResponseSchema,
  cancelShipmentApiResponseSchema,
  scanShipmentApiResponseSchema,
  deliverShipmentApiResponseSchema,
  listShipmentsApiResponseSchema,
  shipmentApiResponseSchema,
} from './response-transformer';
import { Pagination } from '../../types';

// Shipment status enum
export const SHIPMENT_STATUS = [
  'PENDING',
  'AWAITING_PICKUP',
  'IN_TRANSIT',
  'ARRIVED_AT_DESTINATION',
  'DELIVERED',
  'CANCELLED',
] as const;

export const SHIPMENT_SIZE = ['SMALL', 'MEDIUM', 'LARGE', 'EXTRA_LARGE'] as const;

export const CANCELLATION_REASON = [
  'USER_CANCELLED',
  'SYSTEM_EXPIRED',
  'ADMIN_CANCELLED',
] as const;

export const SCAN_ACTION = ['DROPOFF', 'PICKUP', 'ARRIVAL'] as const;

// Filter types for shipment queries
export interface ShipmentFilter {
  search?: string;
  status?: string;
  size?: string;
  customerId?: string;
  originAoId?: string;
  destAoId?: string;
  assignedCarOperatorId?: string;
  cancellationReason?: string;
  role?: string;
  weightGte?: number | null;
  weightLte?: number | null;
  createdAtGte?: string | null;
  createdAtLte?: string | null;
  updatedAtGte?: string | null;
  updatedAtLte?: string | null;
  pickedUpAtGte?: string | null;
  pickedUpAtLte?: string | null;
  cancelledAtGte?: string | null;
  cancelledAtLte?: string | null;
  expiresAtGte?: string | null;
  expiresAtLte?: string | null;
  hasTrackingCode?: boolean | null;
  hasPickupCode?: boolean | null;
  isExpired?: boolean | null;
}

// Query props interfaces following example pattern
export interface GetShipmentQueryProps {
  id: string;
  enabled?: boolean;
  refetchOnWindowFocus?: boolean;
}

export interface GetShipmentsQueryProps {
  populate?: {
    customer?: boolean;
    originAo?: boolean;
    destAo?: boolean;
    assignedCarOperator?: boolean;
    // Add other populate options as needed
  };
  pagination?: Pagination;
  filters?: ShipmentFilter;
  sort?: string;
  enabled?: boolean;
}

export interface GetMyShipmentsQueryProps {
  pagination?: Pagination;
  filters?: ShipmentFilter;
  sort?: string;
  enabled?: boolean;
}

export interface GetPendingShipmentsQueryProps {
  pagination?: Pagination;
  filters?: ShipmentFilter;
  sort?: string;
  enabled?: boolean;
}

// Infer types from Zod schemas
export type Shipment = z.infer<typeof shipmentSchema>;
export type ShipmentBackend = z.infer<typeof shipmentBackendSchema>;

// Request types
export type CreateShipmentApiRequest = z.infer<typeof createShipmentApiRequestSchema>;
export type CreateShipmentBackendRequest = z.infer<typeof createShipmentBackendRequestSchema>;
export type CancelShipmentApiRequest = z.infer<typeof cancelShipmentApiRequestSchema>;
export type ScanShipmentApiRequest = z.infer<typeof scanShipmentApiRequestSchema>;
export type DeliverShipmentApiRequest = z.infer<typeof deliverShipmentApiRequestSchema>;

export interface CreateShipmentQueryProps {
  data: CreateShipmentApiRequest;
  enabled?: boolean;
}

export interface CancelShipmentQueryProps {
  id: string;
  data: CancelShipmentApiRequest;
  enabled?: boolean;
}

export interface ScanShipmentQueryProps {
  data: ScanShipmentApiRequest;
  enabled?: boolean;
}

export interface DeliverShipmentQueryProps {
  data: DeliverShipmentApiRequest;
  enabled?: boolean;
}

// Response types
export type GetShipmentApiResponse = z.infer<typeof getShipmentApiResponseSchema>;
export type CreateShipmentApiResponse = z.infer<typeof createShipmentApiResponseSchema>;
export type CancelShipmentApiResponse = z.infer<typeof cancelShipmentApiResponseSchema>;
export type ScanShipmentApiResponse = z.infer<typeof scanShipmentApiResponseSchema>;
export type DeliverShipmentApiResponse = z.infer<typeof deliverShipmentApiResponseSchema>;
export type ListShipmentsApiResponse = z.infer<typeof listShipmentsApiResponseSchema>;
export type ShipmentApiResponse = ReturnType<typeof shipmentApiResponseSchema>;

// Legacy types for backward compatibility
export type CreateShipmentRequest = CreateShipmentApiRequest;
export type CancelShipmentRequest = CancelShipmentApiRequest;
export type ScanShipmentRequest = ScanShipmentApiRequest;
export type DeliverShipmentRequest = DeliverShipmentApiRequest;
