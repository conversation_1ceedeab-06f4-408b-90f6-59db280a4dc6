import { z } from 'zod';

// User types and status enums (using different names to avoid conflicts)
export const PROFILE_USER_TYPES = ['CUSTOMER', 'ACCESS_OPERATOR', 'CAR_OPERATOR', 'ADMIN'] as const;
export const PROFILE_USER_STATUS = ['PENDING', 'ACTIVE', 'SUSPENDED'] as const;

// Backend user schema - what comes from Prisma/database
export const userBackendSchema = z.object({
  id: z.string().uuid(),
  name: z.string(),
  email: z.string().email(),
  phone: z.string(),
  user_type: z.enum(PROFILE_USER_TYPES).nullable(),
  status: z.enum(PROFILE_USER_STATUS),
  email_verified: z.boolean().optional(),
  created_at: z.string().optional(),
  updated_at: z.string().optional(),
  // For ACCESS_OPERATOR
  business_name: z.string().nullable().optional(),
  address: z.string().nullable().optional(),
  geo_latitude: z.number().nullable().optional(),
  geo_longitude: z.number().nullable().optional(),
  approved: z.boolean().nullable().optional(),
  // For CAR_OPERATOR
  license_number: z.string().nullable().optional(),
  vehicle_info: z.string().nullable().optional(),
  pickup_access_point_id: z.string().nullable().optional(),
  dropoff_access_point_id: z.string().nullable().optional(),
});

// User profile API response transformer - converts backend to frontend format
export const userProfileApiResponseSchema = (item: z.infer<typeof userBackendSchema>) => ({
  id: item.id,
  name: item.name,
  email: item.email,
  phone: item.phone,
  userType: item.user_type ?? null,
  status: item.status,
  emailVerified: item.email_verified ?? false,
  createdAt: item.created_at,
  updatedAt: item.updated_at,
  // For ACCESS_OPERATOR
  businessName: item.business_name ?? null,
  address: item.address ?? null,
  geoLatitude: item.geo_latitude ?? null,
  geoLongitude: item.geo_longitude ?? null,
  approved: item.approved ?? null,
  // For CAR_OPERATOR
  licenseNumber: item.license_number ?? null,
  vehicleInfo: item.vehicle_info ?? null,
  pickupAccessPointId: item.pickup_access_point_id ?? null,
  dropoffAccessPointId: item.dropoff_access_point_id ?? null,
});

// Transformed user schema using the transformer function
export const userProfileSchema = userBackendSchema.transform(userProfileApiResponseSchema);

// Multiple users schema (for future list endpoints)
export const usersBackendSchema = z.array(userBackendSchema);

// Get profile API response schema
export const getProfileApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    user: userBackendSchema,
  }),
}).transform(({ success, message, data }) => ({
  success,
  message,
  data: {
    user: userProfileApiResponseSchema(data.user),
  },
}));

// Update profile API response schema
export const updateProfileApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    user: userBackendSchema,
  }),
}).transform(({ success, message, data }) => ({
  success,
  message,
  data: {
    user: userProfileApiResponseSchema(data.user),
  },
}));

// List profiles API response schema (for future use)
export const listProfilesApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    users: usersBackendSchema,
    pagination: z.object({
      page: z.number(),
      pageSize: z.number(),
      total: z.number(),
      pageCount: z.number(),
    }).optional(),
  }),
}).transform(({ success, message, data }) => ({
  success,
  message,
  data: {
    users: data.users.map(userProfileApiResponseSchema),
    pagination: data.pagination,
  },
}));

// Export schemas collection
export const profileResponseSchemas = {
  userBackend: userBackendSchema,
  userProfile: userProfileSchema,
  userProfileApiResponse: userProfileApiResponseSchema,
  getProfileApiResponse: getProfileApiResponseSchema,
  updateProfileApiResponse: updateProfileApiResponseSchema,
  listProfilesApiResponse: listProfilesApiResponseSchema,
  PROFILE_USER_TYPES,
  PROFILE_USER_STATUS,
};

export default profileResponseSchemas;
