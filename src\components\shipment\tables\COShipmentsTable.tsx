/* eslint-disable react/jsx-no-useless-fragment */
/* eslint-disable no-nested-ternary */
/* eslint-disable complexity */
/* eslint-disable max-lines */
/* eslint-disable react/require-default-props */
import React, {
  useState, useRef, useMemo, useEffect,
} from 'react';
import { useRouter } from 'next/router';
import { useDebouncedCallback } from 'use-debounce';
import {
  Stack,
  Group,
  TextInput,
  Select,
  Button,
  Text,
  Loader,
  Center,
  Alert,
  Paper,
  Box,
  Grid,
  Pagination,
} from '@mantine/core';
import {
  IconSearch,
  IconFilter,
  IconAlertCircle,
  IconTruck,
  IconEye,
  IconRefresh,
  IconUserCheck,
} from '@tabler/icons-react';
import { useQuery } from '@tanstack/react-query';

import { useMediaQuery } from '@mantine/hooks';
import useTranslation from 'next-translate/useTranslation';
import { useIsClient } from '../../../hooks/useIsClient';

import { Shipment, shipmentSortKeysMapping, getMyTransportedShipmentsQuery } from '../../../requests/shipment';
import { DataTableColumn, DataTable } from '../../common/DataTable';
import { StatusBadge } from '../../common/StatusBadge';
import { ShipmentCard } from '../cards';

interface COShipmentsTableProps {
  onViewShipment?: (shipment: Shipment) => void;
  onAssignShipment?: (shipment: Shipment) => void;
  onPickup?: (shipment: Shipment) => void;
}

const ITEMS_PER_PAGE = 10;

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const getSizeLabel = (size: string, t: any) => {
  switch (size) {
    case 'SMALL':
      return t('sizeSmallLabel');
    case 'MEDIUM':
      return t('sizeMediumLabel');
    case 'LARGE':
      return t('sizeLargeLabel');
    case 'EXTRA_LARGE':
      return t('sizeExtraLargeLabel');
    default:
      return size;
  }
};

// eslint-disable-next-line sonarjs/cognitive-complexity
export default function COShipmentsTable({
  onViewShipment,
  onAssignShipment,
  onPickup,
}: COShipmentsTableProps) {
  const { t } = useTranslation('shipments');
  const router = useRouter();

  // Add client-side rendering guard to prevent hydration issues
  const isClient = useIsClient();
  const isDesktop = useMediaQuery('(min-width: 992px)');
  const isRTL = router.locale === 'ar';

  // Note: CO users don't need profile data for filtering - they see all available shipments

  // Safe date formatting function
  const formatDate = (dateString: string) => {
    if (!isClient) return 'Loading...';
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return 'Invalid date';
    }
  };

  // State for pagination and filters
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(ITEMS_PER_PAGE);
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [searchInput, setSearchInput] = useState('');
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Sort state - default to newest first (createdAt desc)
  const [sortStatus, setSortStatus] = useState({
    columnAccessor: 'createdAt',
    direction: 'desc' as 'asc' | 'desc',
  });

  // Get filters from URL query parameters
  const statusFilterFromUrl = `${router.query.status || ''}`;
  const searchFromUrl = `${router.query.query || ''}`;

  // Debounced search handler - only updates search value and URL
  const debouncedSearch = useDebouncedCallback((term: string) => {
    setPage(1); // Reset to first page when searching
    setSearchInput(term); // This triggers the API call

    // Update URL without causing re-render
    const newQuery = { ...router.query };
    if (term.trim()) {
      newQuery.query = term;
    } else {
      delete newQuery.query;
    }

    // Use replace to avoid adding to history and reduce re-renders
    router.replace({
      pathname: router.pathname,
      query: newQuery,
    }, undefined, { shallow: true });
  }, 800); // Increased debounce time for smoother typing

  // Handle input change with immediate UI feedback
  const handleInputChange = (value: string) => {
    setSearchInput(value); // Immediate UI update
    debouncedSearch(value); // Debounced API call
  };

  // Effect to sync URL params with local state - only on mount and when URL actually changes
  useEffect(() => {
    // Sync status filter
    const newStatusFilter = statusFilterFromUrl !== 'undefined' && statusFilterFromUrl !== ''
      ? statusFilterFromUrl
      : null;

    if (newStatusFilter !== statusFilter) {
      setStatusFilter(newStatusFilter);
    }

    // Sync search value
    const urlSearchValue = searchFromUrl !== 'undefined' && searchFromUrl !== ''
      ? searchFromUrl
      : '';

    if (urlSearchValue !== searchInput) {
      setSearchInput(urlSearchValue);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [statusFilterFromUrl, searchFromUrl]);

  // Build filters object for backend - memoized to prevent unnecessary re-renders
  const filters = useMemo(() => {
    const searchValue = searchInput.trim();

    // For CO users, don't filter by access points - they should see all available shipments
    // CO users should see:
    // - AWAITING_PICKUP: shipments ready for pickup
    // - IN_TRANSIT: shipments they are currently transporting
    // - Other statuses: for tracking purposes
    return {
      search: searchValue,
      status: statusFilter || undefined,
    };
  }, [searchInput, statusFilter]);

  // Build sort object for backend - memoized to prevent unnecessary re-renders
  const sortObj = useMemo(() => ({
    sortBy: shipmentSortKeysMapping.has(sortStatus.columnAccessor)
      ? shipmentSortKeysMapping.get(sortStatus.columnAccessor)
      : sortStatus.columnAccessor,
    sortOrder: sortStatus.direction as 'asc' | 'desc',
  }), [sortStatus.columnAccessor, sortStatus.direction]);

  // Fetch shipments with backend filtering
  const {
    data: shipmentsData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    ...getMyTransportedShipmentsQuery({
      pagination: {
        page: page - 1, // API uses 0-based pagination
        pageSize,
      },
      filters,
      sort: `${sortObj.sortBy}:${sortObj.sortOrder}`,
    }),
    refetchOnWindowFocus: false,
    staleTime: 0, // Don't cache to ensure fresh data
    gcTime: 0, // Don't keep in cache
    refetchOnMount: true, // Always refetch on mount
    // Add retry configuration to prevent excessive requests
    retry: 1,
    retryDelay: 1000,
  });

  const shipments = shipmentsData?.data?.shipments || [];
  const totalCount = shipmentsData?.data?.pagination?.total || 0;
  const totalPages = Math.ceil(totalCount / pageSize);

  const handlePageSizeChange = (newPageSize: string | null) => {
    if (newPageSize) {
      setPageSize(parseInt(newPageSize, 10));
      setPage(1); // Reset to first page when changing page size
    }
  };

  const handleStatusChange = (value: string | null) => {
    setPage(1); // Reset to first page when filtering
    setStatusFilter(value);

    // Update URL efficiently
    const newQuery = { ...router.query };
    if (value) {
      newQuery.status = value;
    } else {
      delete newQuery.status;
    }

    router.replace({
      pathname: router.pathname,
      query: newQuery,
    }, undefined, { shallow: true });
  };

  // Show loading skeleton during hydration
  if (!isClient) {
    return (
      <Stack gap="lg">
        <Paper p="md" withBorder>
          <Box h={60} bg="gray.1" style={{ borderRadius: 'var(--mantine-radius-sm)' }} />
        </Paper>
        <Paper withBorder>
          <Box h={400} bg="gray.1" style={{ borderRadius: 'var(--mantine-radius-sm)' }} />
        </Paper>
      </Stack>
    );
  }

  const columns: DataTableColumn<Shipment>[] = [
    {
      label: t('shipmentId'),
      accessor: 'id',
      render: (shipment) => (
        <Text size="sm" fw={500}>
          {`#${shipment.id.slice(-8).toUpperCase()}`}
        </Text>
      ),
      sortable: true,
    },
    {
      label: t('status'),
      accessor: 'status',
      render: (shipment) => <StatusBadge status={shipment.status} />,
      sortable: true,
    },
    {
      label: t('weightAndSize'),
      accessor: 'weight',
      render: (shipment) => (
        <Text size="sm">
          {shipment.weight}
          {'kg • '}
          {getSizeLabel(shipment.size, t)}
        </Text>
      ),
    },
    {
      label: t('destinationAccessPoint'),
      accessor: 'destAoId',
      render: (shipment) => (
        <Text size="sm">
          {shipment.destAoId ? shipment.destAoId : t('notAssigned')}
        </Text>
      ),
    },
    {
      label: t('lastUpdated'),
      accessor: 'updatedAt',
      render: (shipment) => (
        <Text size="sm">
          {formatDate(shipment.updatedAt)}
        </Text>
      ),
      sortable: true,
    },
  ];

  const rowActions = (shipment: Shipment) => (
    <Group gap="xs" justify="center">
      <Button
        variant="light"
        size="xs"
        leftSection={<IconEye size="1rem" />}
        onClick={() => onViewShipment?.(shipment)}
      >
        {t('viewButton')}
      </Button>
      {shipment.status === 'AWAITING_PICKUP' && onPickup && (
        <Button
          variant="light"
          color="blue"
          size="xs"
          leftSection={<IconUserCheck size="1rem" />}
          onClick={() => onPickup(shipment)}
        >
          {t('pickUpButton')}
        </Button>
      )}
      {shipment.status === 'IN_TRANSIT' && onAssignShipment && (
        <Button
          variant="light"
          color="green"
          size="xs"
          leftSection={<IconUserCheck size="1rem" />}
          onClick={() => onAssignShipment(shipment)}
        >
          {t('assignButton')}
        </Button>
      )}
    </Group>
  );

  return (
    <Stack gap="lg">
      {/* Header with stats */}
      <Paper p="md" withBorder>
        <Group justify="space-between" mb="md">
          {isRTL && (
            <Button
              leftSection={<IconRefresh size="1rem" />}
              variant="light"
              onClick={() => refetch()}
              loading={isLoading}
            >
              {t('refresh')}
            </Button>
          )}
          <Group gap="md">
            <IconTruck size="1.5rem" color="cyan" />
            <div>
              <Text size="lg" fw={600}>{t('allShipmentsTitle')}</Text>
              <Text size="sm" c="dimmed">{t('allShipmentsDescription')}</Text>
            </div>
          </Group>
          {!isRTL && (
            <Button
              leftSection={<IconRefresh size="1rem" />}
              variant="light"
              onClick={() => refetch()}
              loading={isLoading}
            >
              {t('refresh')}
            </Button>
          )}
        </Group>

        {/* Stats */}
        <Group gap="lg">
          <div>
            <Text size="xl" fw={700} c="cyan">{totalCount}</Text>
            <Text size="sm" c="dimmed">{t('total')}</Text>
          </div>
        </Group>
      </Paper>

      {/* Filters */}
      <Paper p="md" withBorder>
        <Group gap="md" align="flex-end">
          <TextInput
            ref={searchInputRef}
            placeholder={t('searchShipmentsPlaceholder')}
            leftSection={<IconSearch size="1rem" />}
            value={searchInput}
            onChange={(e) => handleInputChange(e.currentTarget.value)}
            style={{ flex: 1 }}
          />
          <Select
            placeholder={t('filterByStatusPlaceholder')}
            leftSection={<IconFilter size="1rem" />}
            data={[
              { value: 'PENDING', label: t('statusPending') },
              { value: 'AWAITING_PICKUP', label: t('statusAwaitingPickup') },
              { value: 'IN_TRANSIT', label: t('statusInTransit') },
              { value: 'ARRIVED_AT_DESTINATION', label: t('statusArrivedAtDestination') },
              { value: 'DELIVERED', label: t('statusDelivered') },
              { value: 'CANCELLED', label: t('statusCancelled') },
              { value: 'EXPIRED', label: t('statusExpired') },
            ]}
            value={statusFilter}
            onChange={handleStatusChange}
            clearable
            style={{ minWidth: 200 }}
          />
          <Select
            placeholder={t('itemsPerPagePlaceholder')}
            data={[
              { value: '10', label: t('perPage10') },
              { value: '25', label: t('perPage25') },
              { value: '50', label: t('perPage50') },
            ]}
            value={pageSize.toString()}
            onChange={handlePageSizeChange}
            style={{ minWidth: 150 }}
          />
        </Group>
      </Paper>

      {/* Loading State */}
      {isLoading && (
        <Center py="xl">
          <Stack align="center" gap="md">
            <Loader size="lg" />
            <Text c="dimmed">Loading available shipments...</Text>
          </Stack>
        </Center>
      )}

      {/* Error State */}
      {error && (
        <Alert
          icon={<IconAlertCircle size="1rem" />}
          title="Error loading shipments"
          color="red"
          variant="light"
        >
          {error instanceof Error ? error.message : 'Failed to load shipments. Please try again.'}
          <Button variant="light" size="sm" mt="sm" onClick={() => refetch()}>
            Try Again
          </Button>
        </Alert>
      )}

      {/* Shipments Table */}
      {!isLoading && !error && (
        <>
          {shipments.length === 0 ? (
            <Center py="xl">
              <Stack align="center" gap="md">
                <IconTruck size="3rem" color="gray" />
                <div style={{ textAlign: 'center' }}>
                  <Text size="lg" fw={500} c="dimmed">
                    {totalCount === 0 ? t('noShipmentsAvailableForPickup') : t('noShipmentsMatchFilters')}
                  </Text>
                  <Text size="sm" c="dimmed" mt="xs">
                    {totalCount === 0
                      ? t('shipmentsReadyForPickupWillAppearHere')
                      : t('adjustFilters')}
                  </Text>
                </div>
              </Stack>
            </Center>
          ) : (
            <>
              {isDesktop ? (
                <DataTable<Shipment>
                  columns={columns}
                  data={shipments}
                  loading={isLoading}
                  error={error && typeof error === 'object' && 'message' in error ? (error as { message?: string }).message : undefined}
                  emptyMessage={totalCount === 0 ? t('noShipmentsFound') : t('noShipmentsMatchFilters')}
                  pagination={{ page, totalPages, onPageChange: setPage }}
                  rowActions={rowActions}
                  onSortChange={(sort) => setSortStatus({ columnAccessor: sort.accessor, direction: sort.direction })}
                  sortState={{ accessor: sortStatus.columnAccessor, direction: sortStatus.direction }}
                />
              ) : (
                <>
                  <Grid>
                    {shipments.map((shipment: Shipment) => (
                      <Grid.Col key={shipment.id} span={12}>
                        <ShipmentCard
                          shipment={shipment}
                          hideDefaultActions
                          extraActions={rowActions(shipment)}
                        />
                      </Grid.Col>
                    ))}
                  </Grid>
                  {totalPages > 1 && (
                    <Center>
                      <Pagination value={page} total={totalPages} onChange={setPage} size="sm" />
                    </Center>
                  )}
                </>
              )}

              {/* Results Summary */}
              <Text c="dimmed" size="sm" ta="center">
                {t('showing')}
                {' '}
                {shipments.length}
                {' '}
                {t('of')}
                {' '}
                {totalCount}
                {' '}
                {t('availableShipmentsLowercase')}
                {(searchInput || statusFilter) && ` ${t('withCurrentFilters')}`}
              </Text>
            </>
          )}
        </>
      )}
    </Stack>
  );
}
