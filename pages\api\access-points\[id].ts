import { NextApiRequest, NextApiResponse } from 'next';
import { getJwt } from '../../../src/utils';
import { BACKEND_API } from '../../../src/lib/axios';
import { HTTP_CODE } from '../../../src/data';
import { getAccessPointApiResponseSchema } from '../../../src/requests/access-point/response-transformer';
import createApiError from '../../../src/utils/create-api-error';

async function handleGet(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Verify authentication
    const { token } = await getJwt(req);
    if (!token) {
      return res.status(HTTP_CODE.UNAUTHORIZED).json({
        success: false,
        message: 'Authentication required',
      });
    }

    // Get the access point ID from the URL
    const { id } = req.query;

    if (!id || typeof id !== 'string') {
      return res.status(HTTP_CODE.BAD_REQUEST).json({
        success: false,
        message: 'Access point ID is required',
      });
    }

    // Make request to backend API
    const response = await BACKEND_API.get(`/access-points/${id}`, {
      headers: {
        Authorization: token,
      },
    });

    // Validate and transform response using schema
    const validatedResponse = getAccessPointApiResponseSchema.parse(response.data);
    return res.status(HTTP_CODE.SUCCESS).json(validatedResponse);
  } catch (error: unknown) {
    const apiError = createApiError({ error });
    return res.status(apiError.code).json(apiError);
  }
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'GET') {
    return handleGet(req, res);
  }

  res.setHeader('Allow', ['GET']);
  return res.status(HTTP_CODE.METHOD_NOT_ALLOWED).json({
    success: false,
    message: `Method ${req.method} not allowed`,
  });
}
