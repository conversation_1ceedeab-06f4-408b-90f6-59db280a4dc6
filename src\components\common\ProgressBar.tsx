/* eslint-disable max-lines */
/* eslint-disable complexity */

'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { NavigationProgress, nprogress } from '@mantine/nprogress';
import {
  Box,
  Group,
  Text,
  Transition,
  useMantineTheme,
  Tooltip,
} from '@mantine/core';
import {
  IconPackage,
  IconTruck,
  IconMapPin,
  IconCircleCheck,
} from '@tabler/icons-react';

// Shipment stages for the progress visualization
const SHIPMENT_STAGES = [
  { icon: IconPackage, label: 'Package Ready', color: 'orange' },
  { icon: IconTruck, label: 'In Transit', color: 'blue' },
  { icon: IconMapPin, label: 'At Relay Point', color: 'grape' },
  { icon: IconCircleCheck, label: 'Delivered', color: 'green' },
];

// Stage component to reduce complexity
// Extracted connection line into separate component
function ConnectionLine({
  isActive,
  stage,
  index,
  theme,
}: {
  isActive: boolean;
  stage: typeof SHIPMENT_STAGES[0];
  index: number;
  theme: ReturnType<typeof useMantineTheme>;
}) {
  const gradientColors = isActive
    ? [
      theme.colors[stage.color][4],
      theme.colors[SHIPMENT_STAGES[index + 1]?.color || 'gray'][4],
    ]
    : [theme.colors.gray[3], theme.colors.gray[3]];

  return (
    <Box
      style={{
        position: 'absolute',
        left: '100%',
        top: '50%',
        width: 80,
        height: 4,
        backgroundImage: `linear-gradient(to right, ${gradientColors[0]} 0%, ${gradientColors[1]} 100%)`,
        transform: 'translateY(-50%)',
        transition: 'background-image 0.5s ease',
      }}
    />
  );
}

const TRANSFORM_TRANSITION = 'transform 0.3s ease';
const COLOR_TRANSITION = 'color 0.3s ease';

function ShipmentStage({
  stage,
  index,
  isActive,
  isCurrent,
  progress,
  pulseScale,
  theme,
  showConnection,
}: {
  stage: typeof SHIPMENT_STAGES[0];
  index: number;
  isActive: boolean;
  isCurrent: boolean;
  progress: number;
  pulseScale: number;
  theme: ReturnType<typeof useMantineTheme>;
  showConnection: boolean;
}) {
  const Icon = stage.icon;

  return (
    <Group gap={36} style={{ position: 'relative', alignItems: 'center' }}>
      {showConnection && (
        <ConnectionLine isActive={isActive} stage={stage} index={index} theme={theme} />
      )}

      <Box
        style={{
          position: 'relative',
          transform: isCurrent ? 'scale(1.1)' : 'scale(1)',
          transition: TRANSFORM_TRANSITION,
        }}
      >
        <Tooltip
          label={stage.label}
          position="bottom"
          withArrow
          arrowSize={4}
          offset={4}
          styles={() => ({
            arrow: {
              backgroundColor: isActive ? theme.colors[stage.color][7] : theme.colors.gray[7],
            },
            body: {
              backgroundColor: isActive ? theme.colors[stage.color][6] : theme.colors.gray[6],
              color: theme.white,
              padding: '4px 8px',
              fontSize: theme.fontSizes.xs,
              '&:hover': {
                backgroundColor: isActive ? theme.colors[stage.color][7] : theme.colors.gray[7],
              },
            },
          })}
        >
          <Box
            style={{
              cursor: 'pointer',
              transition: TRANSFORM_TRANSITION,
              '&:hover': {
                transform: 'scale(1.15)',
              },
            }}
          >
            <Icon
              size={32}
              color={isActive ? theme.colors[stage.color][5] : theme.colors.gray[5]}
              style={{
                transition: `${COLOR_TRANSITION}, ${TRANSFORM_TRANSITION}`,
                transform: isCurrent && index === 1 ? 'translateY(-4px)' : 'translateY(0)',
              }}
            />
          </Box>
        </Tooltip>

        {isCurrent && (
          <>
            <svg
              width={40}
              height={40}
              style={{
                position: 'absolute',
                top: -8,
                left: -8,
              }}
            >
              <circle
                cx={20}
                cy={20}
                r={18}
                fill="none"
                stroke={theme.colors[stage.color][4]}
                strokeWidth={2}
                strokeDasharray={2 * Math.PI * 18}
                strokeDashoffset={2 * Math.PI * 18 * (1 - progress / 100)}
                style={{
                  transition: 'stroke-dashoffset 0.3s ease',
                  transform: 'rotate(-90deg)',
                  transformOrigin: 'center',
                }}
              />
            </svg>

            <Box
              style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                width: 40,
                height: 40,
                borderRadius: '50%',
                backgroundColor: theme.colors[stage.color][4],
                opacity: pulseScale === 1 ? 0.4 : 0.2,
                transform: `translate(-50%, -50%) scale(${pulseScale})`,
                transition: 'transform 1s ease-in-out, opacity 1s ease-in-out',
              }}
            />
          </>
        )}
      </Box>

      <Text
        size="xs"
        fw={isCurrent ? 700 : 500}
        c={isActive ? stage.color : 'dimmed'}
        style={{
          transition: 'color 0.3s ease, opacity 0.3s ease',
          opacity: isActive ? 1 : 0.7,
          lineHeight: 1.2,
          marginTop: 4,
          '&:hover': {
            color: isActive ? theme.colors[stage.color][7] : theme.colors.dark[2],
          },
        }}
      >
        {stage.label}
      </Text>
    </Group>
  );
}

export function ProgressBar() {
  const router = useRouter();
  const theme = useMantineTheme();
  const [isLoading, setIsLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [currentStage, setCurrentStage] = useState(0);
  const [showShipmentProgress, setShowShipmentProgress] = useState(false);
  const [pulseScale, setPulseScale] = useState(1);

  // Pulse animation effect
  useEffect(() => {
    if (!isLoading || !showShipmentProgress) return undefined;

    const pulseInterval = setInterval(() => {
      setPulseScale((prev) => (prev === 1 ? 1.8 : 1));
    }, 1000);

    return () => clearInterval(pulseInterval);
  }, [isLoading, showShipmentProgress]);

  useEffect(() => {
    let progressInterval: ReturnType<typeof setInterval> | null = null;
    let stageInterval: ReturnType<typeof setInterval> | null = null;

    const clearAllIntervals = () => {
      if (progressInterval) clearInterval(progressInterval);
      if (stageInterval) clearInterval(stageInterval);
      progressInterval = null;
      stageInterval = null;
    };

    const isShipmentRoute = (pathname: string) => {
      const shipmentRoutes = ['/shipments', '/transit', '/delivery', '/tracking'];
      return shipmentRoutes.some((route) => pathname.includes(route));
    };

    const resetStates = () => {
      setIsLoading(false);
      setProgress(0);
      setCurrentStage(0);
      setShowShipmentProgress(false);
    };

    const handleStart = () => {
      clearAllIntervals();
      nprogress.start();
      setIsLoading(true);
      setProgress(0);
      setCurrentStage(0);
      setShowShipmentProgress(isShipmentRoute(router.pathname));
    };

    const handleComplete = () => {
      nprogress.complete();
      setProgress(100);
      if (showShipmentProgress) {
        setCurrentStage(SHIPMENT_STAGES.length - 1);
      }
      setTimeout(resetStates, 300);
      clearAllIntervals();
    };

    const handleError = () => {
      nprogress.complete();
      resetStates();
      clearAllIntervals();
    };

    router.events.on('routeChangeStart', handleStart);
    router.events.on('routeChangeComplete', handleComplete);
    router.events.on('routeChangeError', handleError);

    return () => {
      router.events.off('routeChangeStart', handleStart);
      router.events.off('routeChangeComplete', handleComplete);
      router.events.off('routeChangeError', handleError);
      clearAllIntervals();
    };
  }, [router, showShipmentProgress]);

  // Separate effect for managing intervals
  useEffect(() => {
    if (!isLoading) {
      return undefined;
    }

    const shipmentRoutes = ['/shipments', '/transit', '/delivery', '/tracking'];
    const isShipmentRoute = shipmentRoutes.some((route) => router.pathname.includes(route));

    const progressInterval = setInterval(() => {
      setProgress((prev) => {
        if (prev >= 90) {
          return prev;
        }
        return prev + Math.random() * 20;
      });
    }, 300);

    let stageInterval: ReturnType<typeof setInterval> | null = null;
    if (isShipmentRoute) {
      stageInterval = setInterval(() => {
        setCurrentStage((prev) => {
          if (prev >= SHIPMENT_STAGES.length - 1) {
            return prev;
          }
          return prev + 1;
        });
      }, 800);
    }

    return () => {
      clearInterval(progressInterval);
      if (stageInterval) {
        clearInterval(stageInterval);
      }
    };
  }, [isLoading, router.pathname]);

  return (
    <>
      <NavigationProgress color="blue" size={3} zIndex={10000} />

      {isLoading && showShipmentProgress && (
      <Transition
        mounted={isLoading && showShipmentProgress}
        transition="slide-down"
        duration={400}
        timingFunction="ease"
      >
        {(styles) => (
          <Box
            style={{
              ...styles,
              position: 'fixed',
              top: 3,
              left: 0,
              right: 0,
              height: 60,
              backgroundColor: theme.colors.gray[0],
              borderBottom: `1px solid ${theme.colors.gray[2]}`,
              zIndex: 9999,
              overflow: 'hidden',
            }}
          >
            {/* Animated wave background */}
            <Box
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                background: `linear-gradient(90deg,
                          transparent 0%,
                          ${theme.colors.blue[1]} 10%,
                          ${theme.colors.blue[2]} 50%,
                          ${theme.colors.blue[1]} 90%,
                          transparent 100%)`,
                opacity: 0.3,
                transform: `translateX(${(progress - 50) * 2}%)`,
                transition: 'transform 0.3s ease',
              }}
            />

            {/* Shipment stages */}
            <Group justify="center" gap="xl" style={{ height: '100%', position: 'relative', zIndex: 1 }}>
              {SHIPMENT_STAGES.map((stage, index) => (
                <ShipmentStage
                  key={`stage-${stage.label}`}
                  stage={stage}
                  index={index}
                  isActive={index <= currentStage}
                  isCurrent={index === currentStage}
                  progress={progress}
                  pulseScale={pulseScale}
                  theme={theme}
                  showConnection={index < SHIPMENT_STAGES.length - 1}
                />
              ))}
            </Group>

            {/* Moving indicators */}
            {currentStage === 1 && (
            <Box
              style={{
                position: 'absolute',
                bottom: 8,
                left: `${20 + (progress * 0.6)}%`,
                transition: 'left 0.3s ease',
              }}
            >
              <IconPackage size={16} color={theme.colors.orange[6]} style={{ transform: 'rotate(-15deg)' }} />
            </Box>
            )}

            {currentStage >= 2 && (
            <Box
              style={{
                position: 'absolute',
                bottom: 8,
                right: currentStage === 3 ? '10%' : '30%',
                transition: 'right 0.5s ease',
              }}
            >
              <IconTruck size={20} color={theme.colors.blue[6]} style={{ transform: 'scaleX(-1)' }} />
            </Box>
            )}
          </Box>
        )}
      </Transition>
      )}
    </>
  );
}
