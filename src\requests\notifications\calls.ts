import { API_ENDPOINT } from '../../data';
import { CLIENT_API } from '../../lib/axios';
import { handleApiError } from '../../utils/handle-backend-error';
import { QueryClient } from '@tanstack/react-query';
import { CacheInvalidationManager, QUERY_KEYS } from '../cache-invalidation';
import {
  GetNotificationsQueryProps,
  GetUnreadCountQueryProps,
  MarkAsReadQueryProps,
  MarkAllAsReadQueryProps,
  GetPreferencesQueryProps,
  UpdatePreferencesQueryProps,
  GetFilterOptionsQueryProps,
} from './types';
import {
  transformGetNotificationsParams,
  transformUpdatePreferencesRequest,
} from './request-transformer';
import {
  transformNotificationListResponse,
  transformUnreadCountResponse,
  transformMarkAsReadResponse,
  transformMarkAllAsReadResponse,
  transformGetPreferencesResponse,
  transformUpdatePreferencesResponse,
  transformFilterOptionsResponse,
} from './response-transformer';

// Use centralized query keys
const queryKeys = QUERY_KEYS.notifications;

/**
 * @description Get paginated notifications with optional filters
 * @param props
 * @returns notifications list with pagination and unread count
 */
const getNotificationsRequest = (props: GetNotificationsQueryProps) => {
  const { params = {} } = props;
  const queryParams = transformGetNotificationsParams(params);

  const urlParams = new URLSearchParams();
  Object.entries(queryParams).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      urlParams.append(key, String(value));
    }
  });

  const queryString = urlParams.toString();
  const url = queryString ? `${API_ENDPOINT.notifications.list}?${queryString}` : API_ENDPOINT.notifications.list;

  return CLIENT_API.get(url)
    .then((res) => transformNotificationListResponse(res?.data))
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

/**
 * @description Get count of unread notifications
 * @param props
 * @returns unread count
 */
const getUnreadCountRequest = (props: GetUnreadCountQueryProps) => CLIENT_API.get(API_ENDPOINT.notifications.unreadCount)
  .then((res) => transformUnreadCountResponse(res?.data))
  .catch((e) => {
    handleApiError(e);
    throw e.response?.data;
  });

/**
 * @description Mark specific notification as read
 * @param props
 * @returns updated notification
 */
const markAsReadRequest = (props: MarkAsReadQueryProps) => {
  const { notificationId } = props;
  return CLIENT_API.put(API_ENDPOINT.notifications.markAsRead(notificationId))
    .then((res) => transformMarkAsReadResponse(res?.data))
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

/**
 * @description Mark all notifications as read
 * @param props
 * @returns count of updated notifications
 */
const markAllAsReadRequest = (props: MarkAllAsReadQueryProps) => CLIENT_API.put(API_ENDPOINT.notifications.markAllAsRead)
  .then((res) => transformMarkAllAsReadResponse(res?.data))
  .catch((e) => {
    handleApiError(e);
    throw e.response?.data;
  });

/**
 * @description Get user notification preferences
 * @param props
 * @returns notification preferences
 */
const getPreferencesRequest = (props: GetPreferencesQueryProps) => CLIENT_API.get(API_ENDPOINT.notifications.preferences)
  .then((res) => transformGetPreferencesResponse(res?.data))
  .catch((e) => {
    handleApiError(e);
    throw e.response?.data;
  });

/**
 * @description Update user notification preferences
 * @param props
 * @returns updated notification preferences
 */
const updatePreferencesRequest = (props: UpdatePreferencesQueryProps) => {
  const { data } = props;
  const transformedData = transformUpdatePreferencesRequest(data);

  return CLIENT_API.put(API_ENDPOINT.notifications.preferences, transformedData)
    .then((res) => transformUpdatePreferencesResponse(res?.data))
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

/**
 * @description Get notification filter options
 * @param props
 * @returns available filter options
 */
const getFilterOptionsRequest = (props: GetFilterOptionsQueryProps) => CLIENT_API.get(API_ENDPOINT.notifications.filterOptions)
  .then((res) => transformFilterOptionsResponse(res?.data))
  .catch((e) => {
    handleApiError(e);
    throw e.response?.data;
  });

// React Query configurations
export const getNotificationsQuery = (props: GetNotificationsQueryProps) => ({
  queryKey: [queryKeys.list, props.params],
  queryFn: () => getNotificationsRequest(props),
  refetchOnWindowFocus: false,
  enabled: props?.enabled,
  staleTime: 30000, // 30 seconds
});

export const getUnreadCountQuery = (props: GetUnreadCountQueryProps) => ({
  queryKey: [queryKeys.unreadCount],
  queryFn: () => getUnreadCountRequest(props),
  refetchOnWindowFocus: false,
  enabled: props?.enabled,
  staleTime: 15000, // 15 seconds
});

export const getPreferencesQuery = (props: GetPreferencesQueryProps) => ({
  queryKey: [queryKeys.preferences],
  queryFn: () => getPreferencesRequest(props),
  refetchOnWindowFocus: false,
  enabled: props?.enabled,
  staleTime: 300000, // 5 minutes
});

// Mutation functions
export const markAsReadMutation = () => ({
  mutationKey: [queryKeys.list, 'markAsRead'],
  mutationFn: (props: MarkAsReadQueryProps) => markAsReadRequest(props),
});

export const markAllAsReadMutation = () => ({
  mutationKey: [queryKeys.list, 'markAllAsRead'],
  mutationFn: (props: MarkAllAsReadQueryProps) => markAllAsReadRequest(props),
});

export const updatePreferencesMutation = () => ({
  mutationKey: [queryKeys.preferences, 'update'],
  mutationFn: (props: UpdatePreferencesQueryProps) => updatePreferencesRequest(props),
});

export const getFilterOptionsQuery = (props: GetFilterOptionsQueryProps) => ({
  queryKey: [queryKeys.filterOptions],
  queryFn: () => getFilterOptionsRequest(props),
  refetchOnWindowFocus: false,
  enabled: props?.enabled,
  staleTime: 300000, // 5 minutes - filter options don't change frequently
});

/**
 * Enhanced mutation functions with built-in cache invalidation
 */

// Mark as read mutation with auto-invalidation
export const markAsReadMutationWithInvalidation = (queryClient: QueryClient) => {
  const cacheManager = new CacheInvalidationManager(queryClient);
  return {
    ...markAsReadMutation(),
    onSuccess: async () => {
      await cacheManager.invalidateAfterNotificationUpdate();
    },
  };
};

// Mark all as read mutation with auto-invalidation
export const markAllAsReadMutationWithInvalidation = (queryClient: QueryClient) => {
  const cacheManager = new CacheInvalidationManager(queryClient);
  return {
    ...markAllAsReadMutation(),
    onSuccess: async () => {
      await cacheManager.invalidateAfterNotificationUpdate();
    },
  };
};

// Update preferences mutation with auto-invalidation
export const updatePreferencesMutationWithInvalidation = (queryClient: QueryClient) => {
  const cacheManager = new CacheInvalidationManager(queryClient);
  return {
    ...updatePreferencesMutation(),
    onSuccess: async () => {
      await cacheManager.invalidateAfterNotificationPreferencesUpdate();
    },
  };
};

// Export request functions for direct use
export {
  getNotificationsRequest,
  getUnreadCountRequest,
  markAsReadRequest,
  markAllAsReadRequest,
  getPreferencesRequest,
  updatePreferencesRequest,
  getFilterOptionsRequest,
};
