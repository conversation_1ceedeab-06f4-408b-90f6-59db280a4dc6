import { NextApiRequest, NextApiResponse } from 'next';
import { getJwt } from '../../../src/utils';
import { BACKEND_API } from '../../../src/lib/axios';
import { HTTP_CODE } from '../../../src/data';
import { validateMarkAllAsReadResponse } from '../../../src/requests/notifications';
import createApiError from '../../../src/utils/create-api-error';

const apiMethods = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
} as const;

async function handleMarkAllAsRead(_req: NextApiRequest, res: NextApiResponse, token: string) {
  try {
    const response = await BACKEND_API.put('/notifications/mark-all-read', {}, {
      headers: {
        Authorization: token,
      },
    });

    const validatedResponse = validateMarkAllAsReadResponse(response.data);

    return res.status(HTTP_CODE.SUCCESS).json(validatedResponse);
  } catch (error: unknown) {
    if (error && typeof error === 'object' && 'response' in error) {
      const apiError = error as { response?: { status?: number; data?: unknown } };
      return res.status(apiError.response?.status || HTTP_CODE.INTERNAL_SERVER_ERROR).json(
        apiError.response?.data || createApiError('Backend API error', 'BACKEND_ERROR'),
      );
    }

    return res.status(HTTP_CODE.INTERNAL_SERVER_ERROR).json(
      createApiError('Failed to mark all notifications as read', 'INTERNAL_ERROR'),
    );
  }
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { method } = req;

  try {
    const { token } = await getJwt(req);
    if (!token) {
      return res.status(HTTP_CODE.UNAUTHORIZED).json(
        createApiError('Authentication required', 'UNAUTHORIZED'),
      );
    }

    switch (method) {
      case apiMethods.PUT:
        return await handleMarkAllAsRead(req, res, token as string);

      case apiMethods.GET:
      case apiMethods.POST:
      default:
        return res.status(HTTP_CODE.METHOD_NOT_ALLOWED).json(
          createApiError(`Method ${method} not allowed`, 'METHOD_NOT_ALLOWED'),
        );
    }
  } catch (error) {
    return res.status(HTTP_CODE.INTERNAL_SERVER_ERROR).json(
      createApiError('Internal server error', 'INTERNAL_ERROR'),
    );
  }
}
