/* eslint-disable @typescript-eslint/no-explicit-any */
import z from 'zod';

// Base notification schema
export const notificationSchema = z.object({
  id: z.string(),
  user_id: z.string(),
  shipment_id: z.string().nullable(),
  notification_type: z.enum([
    'SHIPMENT_CREATED',
    'SHIPMENT_ASSIGNED_QR',
    'SHIPMENT_DROPPED_OFF',
    'SHIPMENT_PICKED_UP',
    'SHIPMENT_IN_TRANSIT',
    'SHIPMENT_ARRIVED',
    'SHIPMENT_READY_FOR_DELIVERY',
    'SHIPMENT_DELIVERED',
    'SHIPMENT_CANCELLED',
    'SHIPMENT_EXPIRED',
    'SHIPMENT_STATUS_CHANGED',
    'QR_CODE_ASSIGNED',
    'PACKAGE_READY_FOR_PICKUP',
    'DELIVERY_REMINDER',
    'SYSTEM_ALERT',
    'USER_REGISTERED',
    'USER_EMAIL_VERIFIED',
    'OPERATOR_NEEDS_APPROVAL',
    'USER_STATUS_CHANGED',
    'SECURITY_ALERT',
    'SYSTEM_ERROR',
    'ADMIN_CREATED',
    'BULK_OPERATION_COMPLETED',
    'SYSTEM_MAINTENANCE',
  ]),
  title: z.string(),
  message: z.string(),
  priority: z.enum(['LOW', 'NORMAL', 'HIGH', 'URGENT']),
  read: z.boolean(),
  read_at: z.string().nullable(),
  metadata: z.record(z.any()).nullable(),
  expires_at: z.string().nullable(),
  created_at: z.string(),
  updated_at: z.string(),
});

// Notification preferences schema
export const notificationPreferencesSchema = z.object({
  id: z.string(),
  user_id: z.string(),
  email_notifications: z.boolean(),
  sms_notifications: z.boolean(),
  push_notifications: z.boolean(),
  shipment_created: z.boolean(),
  shipment_status_change: z.boolean(),
  qr_assignment: z.boolean(),
  package_ready: z.boolean(),
  delivery_completed: z.boolean(),
  system_alerts: z.boolean(),
  created_at: z.string(),
  updated_at: z.string(),
});

// Pagination schema
export const paginationSchema = z.object({
  page: z.number(),
  limit: z.number(),
  total: z.number(),
  totalPages: z.number(),
  hasNext: z.boolean(),
  hasPrev: z.boolean(),
});

// API Response schemas
export const notificationListApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string().optional(),
  data: z.object({
    notifications: z.array(notificationSchema),
    pagination: paginationSchema,
    unreadCount: z.number(),
  }),
});

export const unreadCountApiResponseSchema = z.object({
  success: z.boolean().optional(),
  message: z.string().optional(),
  data: z.object({
    unreadCount: z.number(),
  }).optional(),
  unreadCount: z.number().optional(),
}).refine(
  (data) =>
  // Handle both wrapped and direct response formats
    // eslint-disable-next-line implicit-arrow-linebreak
    (data.data?.unreadCount !== undefined) || (data.unreadCount !== undefined),
  {
    message: 'Either data.unreadCount or unreadCount must be provided',
  },
);

export const markAsReadApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string().optional(),
  data: z.object({
    notification: notificationSchema,
  }).optional(),
});

export const markAllAsReadApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string().optional(),
  data: z.object({
    updatedCount: z.number(),
  }).optional(),
});

export const getPreferencesApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string().optional(),
  data: z.object({
    preferences: notificationPreferencesSchema,
  }).optional(),
});

export const updatePreferencesApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string().optional(),
  data: z.object({
    preferences: notificationPreferencesSchema,
  }).optional(),
});

// Response validation functions
export const validateNotificationListResponse = (data: unknown) => notificationListApiResponseSchema.parse(data);

export const validateUnreadCountResponse = (data: unknown) => unreadCountApiResponseSchema.parse(data);

export const validateMarkAsReadResponse = (data: unknown) => markAsReadApiResponseSchema.parse(data);

export const validateMarkAllAsReadResponse = (data: unknown) => markAllAsReadApiResponseSchema.parse(data);

export const validateGetPreferencesResponse = (data: unknown) => getPreferencesApiResponseSchema.parse(data);

export const validateUpdatePreferencesResponse = (data: unknown) => updatePreferencesApiResponseSchema.parse(data);

// Transform functions for API responses
export const transformNotificationListResponse = (response: any) => {
  const validated = validateNotificationListResponse(response);
  return {
    notifications: validated.data.notifications,
    pagination: validated.data.pagination,
    unreadCount: validated.data.unreadCount,
  };
};

export const transformUnreadCountResponse = (response: unknown): number => {
  const validated = validateUnreadCountResponse(response);

  if (validated.data?.unreadCount !== undefined) {
    return validated.data.unreadCount;
  }

  if (validated.unreadCount !== undefined) {
    return validated.unreadCount;
  }

  throw new Error('Unread count is missing in response payload');
};

export const transformMarkAsReadResponse = (response: any) => {
  const validated = validateMarkAsReadResponse(response);
  return validated.data?.notification ?? null; // Return null if data is not provided
};

export const transformMarkAllAsReadResponse = (response: any) => {
  const validated = validateMarkAllAsReadResponse(response);
  return validated.data?.updatedCount ?? 0; // Default to 0 if data is not provided
};

export const transformGetPreferencesResponse = (response: any) => {
  const validated = validateGetPreferencesResponse(response);
  return validated.data?.preferences ?? null; // Return null if data is not provided
};

export const transformUpdatePreferencesResponse = (response: any) => {
  const validated = validateUpdatePreferencesResponse(response);
  return validated.data?.preferences ?? null; // Return null if data is not provided
};

// Filter options schema
export const filterOptionsSchema = z.object({
  notificationTypes: z.array(z.object({
    value: z.string(),
    label: z.string(),
  })),
  priorities: z.array(z.object({
    value: z.string(),
    label: z.string(),
  })),
  readStatus: z.array(z.object({
    value: z.string(),
    label: z.string(),
  })),
});

// Filter options API response schema
export const filterOptionsApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string().optional(),
  data: filterOptionsSchema.optional(),
});

// Validation functions
export const validateFilterOptionsResponse = (response: any) => filterOptionsApiResponseSchema.parse(response);

// Transform functions
export const transformFilterOptionsResponse = (response: any) => {
  const validated = validateFilterOptionsResponse(response);
  return validated.data ?? null;
};
