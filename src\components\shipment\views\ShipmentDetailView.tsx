/* eslint-disable complexity */
/* eslint-disable max-lines */
/* eslint-disable react/require-default-props */
import {
  Paper,
  Stack,
  Group,
  Text,
  Badge,
  Button,
  Grid,
  Card,
  Timeline,
  ActionIcon,
  Title,
  Alert,
} from '@mantine/core';
import {
  IconArrowLeft,
  IconArrowRight,
  IconPackage,
  IconMapPin,
  IconUser,
  IconClock,
  IconQrcode,
  IconCancel,
  IconCheck,
  IconTruck,
  IconAlertTriangle,
  IconRoute,
} from '@tabler/icons-react';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/router';
import useTranslation from 'next-translate/useTranslation';
import { Shipment } from '../../../requests/shipment';
import { formatExpiryDate, getExpiryInfo, shouldHighlightExpiry } from '../../../utils/shipmentExpiry';
import { EnhancedShipmentAccessPointsInfo } from '../info';
import { QRCodeDisplay } from '../cards';
import { useIsClient } from '../../../hooks/useIsClient';

// Dynamically import the map component to avoid SSR issues
const ShipmentRouteMap = dynamic(() => import('../maps/ShipmentRouteMap'), {
  ssr: false,
  loading: () => (
    <Paper withBorder style={{ height: '400px' }}>
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
      >
        Loading map...
      </div>
    </Paper>
  ),
});

interface ShipmentDetailViewProps {
  shipment: Shipment;
  onBack: () => void;
  onCancel?: (shipment: Shipment) => void;
}

// Helper functions
const getStatusColor = (status: string) => {
  switch (status) {
    case 'PENDING':
      return 'orange';
    case 'AWAITING_PICKUP':
      return 'blue';
    case 'IN_TRANSIT':
      return 'cyan';
    case 'ARRIVED_AT_DESTINATION':
      return 'grape';
    case 'DELIVERED':
      return 'green';
    case 'CANCELLED':
      return 'red';
    default:
      return 'gray';
  }
};

// Use translations for status labels
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const getStatusLabel = (status: string, t: any) => {
  switch (status) {
    case 'PENDING':
      return t('statusPending');
    case 'AWAITING_PICKUP':
      return t('statusAwaitingPickup');
    case 'IN_TRANSIT':
      return t('statusInTransit');
    case 'ARRIVED_AT_DESTINATION':
      return t('statusArrivedAtDestination');
    case 'DELIVERED':
      return t('statusDelivered');
    case 'CANCELLED':
      return t('statusCancelled');
    default:
      return status.replace('_', ' ').toLowerCase().replace(/\b\w/g, (l) => l.toUpperCase());
  }
};

// Use translations for size labels
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const getSizeLabel = (size: string, t: any) => {
  switch (size) {
    case 'SMALL':
      return t('sizeSmallLabel');
    case 'MEDIUM':
      return t('sizeMediumLabel');
    case 'LARGE':
      return t('sizeLargeLabel');
    case 'EXTRA_LARGE':
      return t('sizeExtraLargeLabel');
    default:
      return size;
  }
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const getTimelineItems = (shipment: Shipment, t: any) => {
  const items = [];

  // Created
  items.push({
    title: t('shipmentCreated'),
    description: t('shipmentCreatedDescription'),
    icon: <IconPackage size="1rem" />,
    color: 'blue',
    timestamp: shipment.createdAt,
    active: true,
  });

  // Awaiting Pickup
  if (['AWAITING_PICKUP', 'IN_TRANSIT', 'ARRIVED_AT_DESTINATION', 'DELIVERED'].includes(shipment.status)) {
    items.push({
      title: t('readyForPickupTitle'),
      description: t('readyForPickupDescription'),
      icon: <IconClock size="1rem" />,
      color: 'orange',
      timestamp: shipment.createdAt, // This would be updated when status changes
      active: true,
    });
  }

  // In Transit
  if (['IN_TRANSIT', 'ARRIVED_AT_DESTINATION', 'DELIVERED'].includes(shipment.status)) {
    items.push({
      title: t('inTransitTitle'),
      description: t('inTransitDescription'),
      icon: <IconTruck size="1rem" />,
      color: 'cyan',
      timestamp: shipment.pickedUpAt || shipment.createdAt,
      active: true,
    });
  }

  // Arrived at Destination
  if (['ARRIVED_AT_DESTINATION', 'DELIVERED'].includes(shipment.status)) {
    items.push({
      title: t('arrivedAtDestination'),
      description: t('arrivedAtDestinationDescription'),
      icon: <IconMapPin size="1rem" />,
      color: 'grape',
      timestamp: shipment.createdAt, // This would be updated when status changes
      active: true,
    });
  }

  // Delivered
  if (shipment.status === 'DELIVERED') {
    items.push({
      title: t('deliveredTitle'),
      description: t('deliveredDescription'),
      icon: <IconCheck size="1rem" />,
      color: 'green',
      timestamp: shipment.createdAt, // This would be updated when delivered
      active: true,
    });
  }

  // Cancelled
  if (shipment.status === 'CANCELLED') {
    items.push({
      title: t('cancelledTitle'),
      description: t('cancelledDescription', {
        reason: shipment.cancellationReason?.replace('_', ' ').toLowerCase() || t('noReasonProvided'),
      }),
      icon: <IconCancel size="1rem" />,
      color: 'red',
      timestamp: shipment.cancelledAt || shipment.createdAt,
      active: true,
    });
  }

  return items;
};

export default function ShipmentDetailView({
  shipment,
  onBack,
  onCancel,
}: ShipmentDetailViewProps) {
  const { t } = useTranslation('shipments');
  const router = useRouter();
  const isClient = useIsClient();
  const canCancel = shipment.status === 'PENDING';
  const isDelivered = shipment.status === 'DELIVERED';
  const isCancelled = shipment.status === 'CANCELLED';
  const timelineItems = getTimelineItems(shipment, t);
  const isRTL = router.locale === 'ar';

  // Safe date formatting function
  const formatDate = (dateString: string) => {
    if (!isClient) return t('loading');
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return t('invalidDate');
    }
  };

  const formatDateTime = (dateString: string) => {
    if (!isClient) return t('loading');
    try {
      return new Date(dateString).toLocaleString();
    } catch {
      return t('invalidDate');
    }
  };

  return (
    <Stack gap="md">
      {/* Compact Header */}
      <Paper p="md" withBorder radius="md">
        <Group justify="space-between" align="center">
          <Group gap="md" style={{ order: isRTL ? 2 : 1 }}>
            <ActionIcon variant="subtle" onClick={onBack}>
              {isRTL ? <IconArrowRight size="1.2rem" /> : <IconArrowLeft size="1.2rem" />}
            </ActionIcon>
            <div>
              <Title order={2} mb={4}>
                {t('shipmentNumber')}
                {shipment.id.slice(-8).toUpperCase()}
              </Title>
              <Text size="sm" c="dimmed">
                {t('created')}
                {' '}
                {formatDate(shipment.createdAt)}
              </Text>
            </div>
          </Group>
          <Group gap="sm" style={{ order: isRTL ? 1 : 2 }}>
            <Badge color={getStatusColor(shipment.status)} variant="light" size="lg">
              {getStatusLabel(shipment.status, t)}
            </Badge>
            {canCancel && onCancel && (
              <Button
                variant="light"
                color="red"
                size="sm"
                leftSection={<IconCancel size="0.9rem" />}
                onClick={() => onCancel(shipment)}
              >
                {t('cancel')}
              </Button>
            )}
          </Group>
        </Group>
      </Paper>

      {/* Compact 24-hour alert */}
      {shipment.status === 'PENDING' && (
        <Alert
          icon={<IconAlertTriangle size="1rem" />}
          color="orange"
          variant="light"
          title={t('actionRequired24Hour')}
        >
          <Text size="sm">
            {t('visit24HourMessage')}
          </Text>
        </Alert>
      )}

      {/* Expire date alert */}
      {shipment.expiresAt && shouldHighlightExpiry(shipment.expiresAt) && (
        <Alert
          icon={<IconClock size="1rem" />}
          color={getExpiryInfo(shipment.expiresAt).color}
          variant="light"
          title={getExpiryInfo(shipment.expiresAt).isExpired ? t('shipmentExpired') : t('shipmentExpiresSoon')}
        >
          <Text size="sm">
            {getExpiryInfo(shipment.expiresAt).isExpired
              ? t('expiredMessage', { date: formatExpiryDate(shipment.expiresAt) })
              : t('expiresInMessage', { timeRemaining: getExpiryInfo(shipment.expiresAt).timeRemaining })}
          </Text>
        </Alert>
      )}

      <Grid gutter="md">
        {/* Left Column - Shipment Details */}
        <Grid.Col span={{ base: 12, md: 8 }}>
          <Stack gap="md">
            {/* Compact Package Information */}
            <Card withBorder p="md">
              <Stack gap="sm">
                <Group gap="xs">
                  <IconPackage size="1.1rem" color="blue" />
                  <Text fw={600} size="md">{t('packageInformation')}</Text>
                </Group>

                <Grid gutter="sm">
                  <Grid.Col span={3}>
                    <Text size="xs" c="dimmed">{t('weight')}</Text>
                    <Text fw={600}>
                      {shipment.weight}
                      {' '}
                      kg
                    </Text>
                  </Grid.Col>
                  <Grid.Col span={3}>
                    <Text size="xs" c="dimmed">{t('size')}</Text>
                    <Text fw={600}>{getSizeLabel(shipment.size, t)}</Text>
                  </Grid.Col>
                  <Grid.Col span={3}>
                    <Text size="xs" c="dimmed">{t('status')}</Text>
                    <Badge size="sm" color={getStatusColor(shipment.status)} variant="light">
                      {getStatusLabel(shipment.status, t)}
                    </Badge>
                  </Grid.Col>
                  {shipment.expiresAt && (
                    <Grid.Col span={3}>
                      <Text size="xs" c="dimmed">{t('expires')}</Text>
                      <Group gap="xs">
                        <Text fw={600} size="sm" c={getExpiryInfo(shipment.expiresAt).color}>
                          {getExpiryInfo(shipment.expiresAt).timeRemaining}
                        </Text>
                        {getExpiryInfo(shipment.expiresAt).isExpired && (
                          <Badge size="xs" color="red" variant="filled">
                            {t('expired')}
                          </Badge>
                        )}
                      </Group>
                      <Text size="xs" c="dimmed">
                        {formatExpiryDate(shipment.expiresAt)}
                      </Text>
                    </Grid.Col>
                  )}
                </Grid>

                <div>
                  <Text size="xs" c="dimmed">{t('description')}</Text>
                  <Text size="sm">{shipment.description}</Text>
                </div>
              </Stack>
            </Card>

            {/* Compact Receiver Information */}
            <Card withBorder p="md">
              <Stack gap="sm">
                <Group gap="xs">
                  <IconUser size="1.1rem" color="green" />
                  <Text fw={600} size="md">{t('receiverInformation')}</Text>
                </Group>

                <Grid gutter="sm">
                  <Grid.Col span={6}>
                    <Text size="xs" c="dimmed">{t('name')}</Text>
                    <Text fw={600}>{shipment.receiverName}</Text>
                  </Grid.Col>
                  <Grid.Col span={6}>
                    <Text size="xs" c="dimmed">{t('phone')}</Text>
                    <Text fw={600}>{shipment.receiverPhone}</Text>
                  </Grid.Col>
                </Grid>
              </Stack>
            </Card>

            {/* Enhanced Access Points */}
            <Card withBorder p="md">
              <Stack gap="sm">
                <Group gap="xs">
                  <IconRoute size="1.1rem" color="orange" />
                  <Text fw={600} size="md">{t('accessPointsAndRoute')}</Text>
                </Group>
                <EnhancedShipmentAccessPointsInfo
                  shipment={shipment}
                  variant="default"
                  showRouteInfo={false}
                  showInstructions
                />
              </Stack>
            </Card>

            {/* Compact Route Map */}
            <Card withBorder p="md">
              <Stack gap="sm">
                <Group gap="xs">
                  <IconRoute size="1.1rem" color="blue" />
                  <Text fw={600} size="md">{t('shipmentRoute')}</Text>
                </Group>
                <ShipmentRouteMap shipment={shipment} />
              </Stack>
            </Card>

            {/* Compact QR Code */}
            {shipment.pickupCode && !isDelivered && !isCancelled && (
              <Card withBorder p="md">
                <Group gap="xs" mb="sm">
                  <IconQrcode size="1.1rem" color="orange" />
                  <Text fw={600} size="md">{t('pickupQRCode')}</Text>
                </Group>
                <QRCodeDisplay
                  value={shipment.pickupCode}
                  title=""
                  description={t('showQRCodeForPickup')}
                  size={150}
                  downloadFileName={`pickup-code-${shipment.id.slice(-8)}`}
                />
              </Card>
            )}

            {/* Compact Tracking Code */}
            {shipment.trackingCode && (
              <Card withBorder p="md">
                <Group gap="xs" mb="sm">
                  <IconQrcode size="1.1rem" color="green" />
                  <Text fw={600} size="md">{t('trackingCodeTitle')}</Text>
                </Group>
                <Text fw={700} c="green" size="lg" style={{ fontFamily: 'monospace' }}>
                  {shipment.trackingCode}
                </Text>
                <Text size="xs" c="dimmed" mt="xs">{t('useToTrackShipment')}</Text>
              </Card>
            )}
          </Stack>
        </Grid.Col>

        {/* Right Column - Compact Timeline */}
        <Grid.Col span={{ base: 12, md: 4 }}>
          <Card withBorder p="md">
            <Stack gap="sm">
              <Group gap="xs">
                <IconClock size="1.1rem" color="blue" />
                <Text fw={600} size="md">{t('timeline')}</Text>
              </Group>

              <Timeline active={timelineItems.length - 1} bulletSize={20} lineWidth={2}>
                {timelineItems.map((item, index) => (
                  <Timeline.Item
                    // eslint-disable-next-line react/no-array-index-key
                    key={index}
                    bullet={item.icon}
                    title={<Text fw={500} size="sm">{item.title}</Text>}
                    color={item.color}
                    style={{ paddingBottom: '12px' }}
                  >
                    <Text size="xs" c="dimmed" mt={2}>
                      {item.description}
                    </Text>
                    <Text size="xs" c="dimmed" mt={2}>
                      {formatDateTime(item.timestamp)}
                    </Text>
                  </Timeline.Item>
                ))}
              </Timeline>
            </Stack>
          </Card>
        </Grid.Col>
      </Grid>
    </Stack>
  );
}
