import {
  Stack,
  Group,
  Text,
  Button,
  Alert,
  TextInput,
  Tabs,
} from '@mantine/core';
import {
  IconCamera,
  IconCheck,
  IconKeyboard,
} from '@tabler/icons-react';
import useTranslation from 'next-translate/useTranslation';
import QRScanner from '../../common/QRScanner';

interface PackageQRStepProps {
  packageQR: string;
  setPackageQR: (value: string) => void;
  scanMethod: 'camera' | 'manual';
  setScanMethod: (method: 'camera' | 'manual') => void;
  error: string | null;
  setError: (error: string | null) => void;
  onNext: () => void;
  opened: boolean;
  activeStep: number;
  expectedActiveStep?: number;
}

export default function PackageQRStep({
  packageQR,
  setPackageQR,
  scanMethod,
  setScanMethod,
  error,
  setError,
  onNext,
  opened,
  activeStep,
  expectedActiveStep = 0,
}: PackageQRStepProps) {
  const { t } = useTranslation('shipments');

  const clearAlerts = () => {
    setError(null);
  };

  const handlePackageQRScan = (value: string) => {
    clearAlerts();
    setPackageQR(value);
  };

  const validatePackageQR = () => {
    if (!packageQR) {
      setError(t('pleaseScanEnterPackageQR'));
      return false;
    }
    if (!packageQR.startsWith('AO_')) {
      setError(t('invalidQRFormat'));
      return false;
    }
    return true;
  };

  const scanned = !!packageQR && !error;

  return (
    <Stack gap="md">
      {scanned ? (
        <Alert
          icon={<IconCheck size="1rem" />}
          color="green"
          variant="light"
        >
          <Text size="sm">{t('packageQRScanned')}</Text>
        </Alert>
      ) : (
        <>
          <Text fw={500}>{t('scanPackageQRCodeTitle')}</Text>
          <Alert color="blue" variant="light">
            <Text size="sm">
              <strong>
                {t('important')}
                :
              </strong>
              {' '}
              {t('scanPackageQRImportant')}
            </Text>
          </Alert>

          <Tabs value={scanMethod} onChange={(value) => setScanMethod(value as 'camera' | 'manual')}>
            <Tabs.List>
              <Tabs.Tab value="camera" leftSection={<IconCamera size="0.8rem" />}>
                {t('cameraScan')}
              </Tabs.Tab>
              <Tabs.Tab value="manual" leftSection={<IconKeyboard size="0.8rem" />}>
                {t('manualEntry')}
              </Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="camera" pt="md">
              <QRScanner
                onScan={handlePackageQRScan}
                onError={(e) => setError(e)}
                expectedFormat="AO_"
                title={t('scanPackageQRCode')}
                description={t('scanPackageQRCodeDescription')}
                isActive={scanMethod === 'camera' && opened && activeStep === expectedActiveStep && !packageQR}
                debug
              />
            </Tabs.Panel>

            <Tabs.Panel value="manual" pt="md">
              <TextInput
                label={t('enterPackageQRCode')}
                placeholder={t('enterPackageQRCodePlaceholder')}
                value={packageQR}
                onChange={(event) => {
                  clearAlerts();
                  setPackageQR(event.currentTarget.value);
                }}
                required
                description={t('enterPackageQRCodeDescription')}
              />
            </Tabs.Panel>
          </Tabs>
        </>
      )}

      <Group justify="flex-end" mt="md">
        <Button
          onClick={() => {
            if (validatePackageQR()) {
              onNext();
            }
          }}
        >
          {t('nextStep')}
        </Button>
      </Group>
    </Stack>
  );
}

PackageQRStep.defaultProps = {
  expectedActiveStep: 0,
};
