/* eslint-disable linebreak-style */
import {
  Stack,
  Text,
  Button,
  Box,
  Image,
  Alert,
  Group,
  Paper,
} from '@mantine/core';
import { IconCamera, IconCheck } from '@tabler/icons-react';

interface PhotoStepProps {
  photoPreview: string | null;
  photoUrl: string | null;
  onFileChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  uploadPending: boolean;
  validatePhoto: () => boolean;
  onBack: () => void;
  onNext: () => void;
  error: string | null;
}

export default function PhotoStep({
  photoPreview,
  photoUrl,
  onFileChange,
  uploadPending,
  validatePhoto,
  onBack,
  onNext,
  error,
}: PhotoStepProps) {
  return (
    <Paper p="md" withBorder>
      <Stack gap="md">
        <Text size="sm" c="dimmed">
          Take a photo of the package for documentation
        </Text>

        <input
          type="file"
          accept="image/*"
          capture="environment"
          onChange={onFileChange}
          style={{ display: 'none' }}
          id="photo-input"
        />
        <Button
          component="label"
          htmlFor="photo-input"
          leftSection={<IconCamera size="1rem" />}
          variant="light"
          fullWidth
          loading={uploadPending}
          disabled={uploadPending}
        >
          {uploadPending ? 'Uploading Photo...' : 'Take Photo'}
        </Button>

        {photoPreview && (
          <Box>
            <Text size="sm" c="dimmed" mb="xs">
              Package Photo:
            </Text>
            <Image src={photoPreview} alt="Package photo" height={200} fit="contain" radius="md" />
            {photoUrl && !error && (
              <Alert icon={<IconCheck size="1rem" />} color="green" variant="light" mt="md">
                Photo uploaded successfully!
              </Alert>
            )}
          </Box>
        )}

        <Group justify="flex-end" mt="md">
          <Button variant="light" onClick={onBack}>
            Back
          </Button>
          <Button onClick={() => {
            if (validatePhoto()) {
              onNext();
            }
          }}
          >
            Next Step
          </Button>
        </Group>
      </Stack>
    </Paper>
  );
}
