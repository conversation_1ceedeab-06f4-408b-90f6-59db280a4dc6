/* eslint-disable max-lines */
import { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import {
  IconCamera,
  IconCheck,
  IconAlertTriangle,
  IconPhone,
} from '@tabler/icons-react';
import { useMutation } from '@tanstack/react-query';
import useTranslation from 'next-translate/useTranslation';
import { Shipment } from '../../../requests/shipment';
import {
  useScanShipmentMutation,
  useDeliverShipmentMutation,
} from '../../../requests/hooks/enhanced-mutations';
import { useIsClient } from '../../../hooks/useIsClient';

// Extracted step UIs
import PackageQRStep from './PackageQRStep';
import PickupQRStep from './PickupQRStep';
import ReceiverInfoStep from './ReceiverInfoStep';
import PhotoNotesStep from './PhotoNotesStep';

interface PickupProcessModalProps {
  opened: boolean;
  onClose: () => void;
  shipment: Shipment | null;
  // eslint-disable-next-line react/require-default-props
  onSuccess?: () => void;
  pickupType: 'CO_PICKUP' | 'AO_DELIVERY';
}

export default function PickupProcessModal({
  opened,
  onClose,
  shipment, // eslint-disable-line @typescript-eslint/no-unused-vars
  onSuccess,
  pickupType,
}: PickupProcessModalProps) {
  const isClient = useIsClient();
  const { t } = useTranslation('shipments');

  // State management
  const [activeStep, setActiveStep] = useState(0);
  const [packageQR, setPackageQR] = useState(''); // AO QR from package
  const [pickupQR, setPickupQR] = useState(''); // Pickup QR from receiver (only for AO_DELIVERY)
  const [receiverPhone, setReceiverPhone] = useState('');
  const [photoPreview, setPhotoPreview] = useState<string | null>(null);
  const [photoUrl, setPhotoUrl] = useState<string | null>(null);
  const [notes, setNotes] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [scanMethod, setScanMethod] = useState<'camera' | 'manual'>('camera');
  const [loading, setLoading] = useState(false);

  // Determine steps based on pickup type
  const isCarOperatorPickup = pickupType === 'CO_PICKUP';
  const totalSteps = isCarOperatorPickup ? 3 : 4; // CO: QR->Photo->Notes, AO: QR->PickupQR->Phone->Photo
  const completedIndex = totalSteps;

  // Calculate step indices for AO_DELIVERY mode
  const stepIndices = isCarOperatorPickup ? {
    packageQR: 0,
    photo: 1,
  } : {
    packageQR: 0,
    pickupQR: 1,
    receiverInfo: 2,
    photo: 3,
  };

  const getModalTitle = () => (isCarOperatorPickup ? t('carOperatorPickup') : t('deliverToReceiver'));

  const handleClose = () => {
    setActiveStep(0);
    setPackageQR('');
    setPickupQR('');
    setReceiverPhone('');
    setPhotoPreview(null);
    setPhotoUrl(null);
    setNotes('');
    setError(null);
    setScanMethod('camera');
    setLoading(false);
    onClose();
  };

  const clearAlerts = () => {
    setError(null);
  };

  const handleStepChange = (step: number) => {
    clearAlerts();
    setActiveStep(step);
  };

  // Upload photo mutation
  const uploadPhotoMutation = useMutation({
    mutationFn: async (data: { photoBase64: string; folder: string }) => {
      const response = await fetch('/api/uploads/photo', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          photoBase64: data.photoBase64,
          folder: data.folder,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to upload photo');
      }

      return response.json();
    },
    onError: (e: Error) => {
      notifications.show({
        title: 'Upload Failed',
        message: e.message || 'Failed to upload photo. Please try again.',
        color: 'red',
        icon: <IconAlertTriangle size="1rem" />,
        autoClose: 5000,
      });
    },
  });

  // Car Operator pickup mutation with auto cache invalidation
  const scanShipmentMutationInstance = useScanShipmentMutation({
    successMessage: 'Package has been successfully picked up and is now in transit.',
    onSuccess: () => {
      setActiveStep(completedIndex);
      setError(null);

      if (onSuccess) {
        setTimeout(() => {
          onSuccess();
          handleClose();
        }, 2000);
      }
    },
    onError: (mutationError) => {
      const message = mutationError instanceof Error ? mutationError.message : 'Failed to complete pickup. Please try again.';
      setError(message);
    },
  });

  // Access Operator delivery mutation with auto cache invalidation
  const deliverShipmentMutationInstance = useDeliverShipmentMutation({
    successMessage: 'The package has been successfully delivered to the receiver.',
    onSuccess: () => {
      setActiveStep(completedIndex);
      setError(null);

      if (onSuccess) {
        setTimeout(() => {
          onSuccess();
          handleClose();
        }, 2000);
      }
    },
    onError: (mutationError) => {
      const message = mutationError instanceof Error ? mutationError.message : 'Failed to complete delivery. Please try again.';
      setError(message);
    },
  });

  const handlePhotoUpload = async (base64Data: string) => {
    try {
      const folder = isCarOperatorPickup ? 'shipment-pickups' : 'shipment-deliveries';
      const photoResponse = await uploadPhotoMutation.mutateAsync({
        photoBase64: base64Data,
        folder,
      });

      const uploadedPhotoUrl = photoResponse.data.photo_url;
      setPhotoUrl(uploadedPhotoUrl);
      setError(null);
    } catch (e) {
      if (e instanceof Error) {
        setError(e.message);
      } else {
        setError('Failed to upload photo. Please try again.');
      }
      setPhotoUrl(null);
      setPhotoPreview(null);
    }
  };

  const handlePhotoChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = async (e) => {
        const base64Data = e.target?.result as string;
        setPhotoPreview(base64Data);
        await handlePhotoUpload(base64Data);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleCompleteProcess = async () => {
    if (!photoUrl) {
      setError('Photo is required');
      return;
    }

    setLoading(true);

    try {
      if (isCarOperatorPickup) {
        // Car Operator pickup - use scan API
        await scanShipmentMutationInstance.mutateAsync({
          data: {
            shipmentId: shipment?.id as string,
            qrValue: packageQR,
            photoUrl,
            action: 'PICKUP',
            notes: notes || 'Package picked up by Car Operator',
          },
        });
      } else {
        // Access Operator delivery - use deliver API
        await deliverShipmentMutationInstance.mutateAsync({
          data: {
            shipmentQr: packageQR,
            pickupQr: pickupQR,
            photoUrl,
            notes: notes || 'Package delivered to receiver',
          },
        });
      }
    } catch (err) {
      // Error handling is done by the mutation hooks
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  if (!isClient) {
    return null;
  }

  return (
    <Modal
      opened={opened}
      onClose={handleClose}
      title={getModalTitle()}
      size="lg"
      centered
    >
      <Stack gap="md">
        {error && (
          <Alert
            icon={<IconAlertTriangle size="1rem" />}
            color="red"
            variant="light"
            onClose={() => setError(null)}
            withCloseButton
          >
            {error}
          </Alert>
        )}

        <Stepper
          active={activeStep}
          onStepClick={handleStepChange}
          allowNextStepsSelect={false}
        >
          {/* Step 1: Scan Package QR Code */}
          <Stepper.Step label={t('packageQR')} description={t('scanPackageQRCode')}>
            <PackageQRStep
              packageQR={packageQR}
              setPackageQR={setPackageQR}
              scanMethod={scanMethod}
              setScanMethod={setScanMethod}
              error={error}
              setError={setError}
              onNext={() => setActiveStep((prev) => prev + 1)}
              opened={opened}
              activeStep={activeStep}
              expectedActiveStep={stepIndices.packageQR}
            />
          </Stepper.Step>

          {/* Step 2: Pickup QR Code (only for AO_DELIVERY) */}
          {!isCarOperatorPickup && (
            <Stepper.Step label="Pickup QR" description="Scan pickup code from receiver">
              <PickupQRStep
                pickupQR={pickupQR}
                setPickupQR={setPickupQR}
                scanMethod={scanMethod}
                setScanMethod={setScanMethod}
                error={error}
                setError={setError}
                onBack={() => setActiveStep((prev) => prev - 1)}
                onNext={() => setActiveStep((prev) => prev + 1)}
                opened={opened}
                activeStep={activeStep}
                expectedActiveStep={stepIndices.pickupQR}
              />
            </Stepper.Step>
          )}

          {/* Step 3: Receiver Phone (only for AO_DELIVERY) */}
          {!isCarOperatorPickup && (
            <Stepper.Step
              label="Receiver Info"
              description="Enter receiver's phone number"
              icon={<IconPhone size="1.1rem" />}
            >
              <ReceiverInfoStep
                receiverPhone={receiverPhone}
                setReceiverPhone={setReceiverPhone}
                error={error}
                setError={setError}
                onBack={() => setActiveStep((prev) => prev - 1)}
                onNext={() => setActiveStep((prev) => prev + 1)}
              />
            </Stepper.Step>
          )}

          {/* Step: Photo & Notes (final step for both types) */}
          <Stepper.Step
            label={t('photoAndNotes')}
            description={isCarOperatorPickup ? t('takePickupPhoto') : t('takeDeliveryPhoto')}
            icon={<IconCamera size="1.1rem" />}
          >
            <PhotoNotesStep
              photoPreview={photoPreview}
              photoUrl={photoUrl}
              notes={notes}
              setNotes={setNotes}
              onFileChange={handlePhotoChange}
              uploadPending={uploadPhotoMutation.isPending}
              error={error}
              setError={setError}
              onBack={() => setActiveStep((prev) => prev - 1)}
              onComplete={handleCompleteProcess}
              loading={loading || scanShipmentMutationInstance.isPending || deliverShipmentMutationInstance.isPending}
              isCarOperatorPickup={isCarOperatorPickup}
            />
          </Stepper.Step>

          {/* Completed Step */}
          <Stepper.Completed>
            <Stack align="center">
              <IconCheck size="3rem" color="green" />
              <Text fw={600} size="lg">
                {isCarOperatorPickup ? t('pickupComplete') : t('deliveryComplete')}
              </Text>
              <Text c="dimmed" size="sm" ta="center">
                {isCarOperatorPickup
                  ? t('packagePickedUpSuccessMessage')
                  : t('packageDeliveredSuccessMessage')}
              </Text>
              <Button mt="md" onClick={handleClose}>{t('close')}</Button>
            </Stack>
          </Stepper.Completed>
        </Stepper>
      </Stack>
    </Modal>
  );
}

PickupProcessModal.default = {
  onSuccess: () => {},
};
