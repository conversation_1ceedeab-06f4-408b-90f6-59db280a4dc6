import { z } from 'zod';

// Request Schemas for Frontend (camelCase)
export const generateForShipmentRequestSchema = z.object({
  shipmentId: z.string().uuid('Invalid shipment ID format'),
});

export const generateBulkRequestSchema = z.object({
  quantity: z.number().int().min(1, 'Quantity must be at least 1').max(100, 'Maximum 100 QR codes per batch'),
  prefix: z.string().optional(),
});

export const getQRLabelsRequestSchema = z.object({
  pagination: z.object({
    page: z.number().int().min(1).optional(),
    limit: z.number().int().min(1).max(100)
      .optional(),
  }).optional(),
  filters: z.object({
    status: z.enum(['AVAILABLE', 'ASSIGNED', 'USED']).optional(),
    shipmentId: z.string().uuid().optional(),
    createdAtGte: z.string().optional(),
    createdAtLte: z.string().optional(),
  }).optional(),
  sort: z.object({
    field: z.enum(['createdAt', 'updatedAt', 'assignedAt', 'usedAt']).optional(),
    order: z.enum(['asc', 'desc']).optional(),
  }).optional(),
});

// Backend Schemas (snake_case)
export const generateForShipmentBackendSchema = z.object({
  shipment_id: z.string().uuid(),
});

export const generateBulkBackendSchema = z.object({
  quantity: z.number().int().min(1).max(100),
  prefix: z.string().optional(),
});

// Schema for generating PDF labels (without shipment)
export const generatePDFRequestSchema = z.object({
  count: z.number().int().min(1).max(50),
});

export const generatePDFBackendSchema = z.object({
  count: z.number().int().min(1).max(50),
});

// Transform functions
export const transformGenerateForShipmentRequest = (data: z.infer<typeof generateForShipmentRequestSchema>) => generateForShipmentBackendSchema.parse({
  shipment_id: data.shipmentId,
});

export const transformGenerateBulkRequest = (data: z.infer<typeof generateBulkRequestSchema>) => generateBulkBackendSchema.parse({
  quantity: data.quantity,
  prefix: data.prefix,
});

export const transformGeneratePDFRequest = (
  data: z.infer<typeof generatePDFRequestSchema>,
) => generatePDFBackendSchema.parse({
  count: data.count,
});

// Export schemas for use in API routes
export const qrLabelRequestSchemas = {
  generateForShipmentFrontend: generateForShipmentRequestSchema,
  generateForShipmentBackend: generateForShipmentBackendSchema,
  generateBulkFrontend: generateBulkRequestSchema,
  generateBulkBackend: generateBulkBackendSchema,
  generatePDFFrontend: generatePDFRequestSchema,
  generatePDFBackend: generatePDFBackendSchema,
  getQRLabelsFrontend: getQRLabelsRequestSchema,
};

// Export transform functions
export const qrLabelRequestTransformers = {
  generateForShipment: transformGenerateForShipmentRequest,
  generateBulk: transformGenerateBulkRequest,
  generatePDF: transformGeneratePDFRequest,
};
