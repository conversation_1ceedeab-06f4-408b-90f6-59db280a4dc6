import { useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import useTranslation from 'next-translate/useTranslation';
import {
  Container,
  Paper,
  Title,
  Text,
  Loader,
  Center,
  Alert,
  Stack,
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { IconMapPin, IconAlertCircle, IconCheck } from '@tabler/icons-react';
import { useQuery } from '@tanstack/react-query';
import dynamic from 'next/dynamic';
import { getProfileQuery } from '../../src/requests/profile/calls';
import { useUpdateProfileMutation } from '../../src/requests/hooks/enhanced-mutations';

// Dynamically import the map component to avoid SSR issues
const AccessPointMap = dynamic(
  () => import('../../src/components/maps/AccessPointMap'),
  {
    ssr: false,
    loading: () => <Center style={{ height: '400px' }}><Loader /></Center>,
  },
);

export default function AccessPointsMapPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { t } = useTranslation('profile');
  const isRTL = router.locale === 'ar';

  // Redirect if not authenticated or not an access operator
  useEffect(() => {
    if (status === 'loading') return;

    if (!session) {
      router.push('/auth/login');
      return;
    }

    if (session.user?.user_type !== 'ACCESS_OPERATOR') {
      router.push('/dashboard');
    }
  }, [session, status, router]);

  // Get profile data
  const { data: profileData, isLoading, error } = useQuery(
    getProfileQuery({ enabled: !!session && session.user?.user_type === 'ACCESS_OPERATOR' }),
  );

  // Update location mutation using enhanced hook
  const updateLocationMutation = useUpdateProfileMutation({
    successMessage: 'Access point location updated successfully',
    onError: (updateError: unknown) => {
      const errorMessage = (updateError as { response?: { data?: { message?: string } } })?.response?.data?.message || 'Failed to update location';
      notifications.show({
        title: 'Error',
        message: errorMessage,
        color: 'red',
        icon: <IconAlertCircle size="1rem" />,
      });
    },
  });

  const handleLocationSave = (lat: number, lng: number) => {
    updateLocationMutation.mutate({
      data: {
        geo_latitude: lat,
        geo_longitude: lng,
      },
    });
  };

  if (status === 'loading' || isLoading) {
    return (
      <Center style={{ height: '100vh' }}>
        <Loader />
      </Center>
    );
  }

  if (error) {
    return (
      <Container size="lg" p="xl">
        <Alert icon={<IconAlertCircle size="1rem" />} title="Error" color="red">
          Failed to load profile data. Please try again.
        </Alert>
      </Container>
    );
  }

  if (!session || session.user?.user_type !== 'ACCESS_OPERATOR') {
    return null;
  }

  if (!profileData?.data?.user) {
    return (
      <Container size="lg" p="xl">
        <Alert icon={<IconAlertCircle size="1rem" />} title="No Data" color="yellow">
          Unable to load your profile data.
        </Alert>
      </Container>
    );
  }

  const { user } = profileData.data;

  return (
    <Container size="lg" p="xl">
      <Stack gap="lg">
        <Paper shadow="sm" p="xl" radius="md" withBorder>
          <div style={{ marginBottom: '1.5rem' }}>
            <Title order={2} mb="xs">
              <IconMapPin
                size="1.5rem"
                style={{
                  [isRTL ? 'marginLeft' : 'marginRight']: '0.5rem',
                  display: 'inline',
                }}
              />
              {t('accessPointLocation')}
            </Title>
            <Text c="dimmed">
              {t('clickMapToSetLocation')}
            </Text>
          </div>

          {!user.geo_latitude || !user.geo_longitude ? (
            <Alert icon={<IconAlertCircle size="1rem" />} color="orange" mb="lg">
              <Text fw={500}>Location Not Set</Text>
              <Text size="sm">
                Please set your access point location by clicking on the map or entering coordinates manually.
                This is required for customers to find your services.
              </Text>
            </Alert>
          ) : (
            <Alert icon={<IconCheck size="1rem" />} color="green" mb="lg">
              <Text fw={500}>Location Set</Text>
              <Text size="sm">
                Your access point location is currently set. You can update it by clicking on a new location on the map.
              </Text>
            </Alert>
          )}

          <AccessPointMap
            initialLat={user.geo_latitude || 24.7136}
            initialLng={user.geo_longitude || 46.6753}
            businessName={user.business_name}
            address={user.address}
            onSave={handleLocationSave}
            isEditable
          />
        </Paper>

        {/* Additional Information */}
        <Paper shadow="sm" p="lg" radius="md" withBorder>
          <Title order={4} mb="md">Important Information</Title>
          <Stack gap="sm">
            <Text size="sm">
              • Your access point location will be visible to customers looking for pickup and delivery services.
            </Text>
            <Text size="sm">
              • Make sure the location is accurate and accessible to vehicles.
            </Text>
            <Text size="sm">
              • You can update your location at any time, but frequent changes may confuse customers.
            </Text>
            <Text size="sm">
              • The location should match your business address for consistency.
            </Text>
          </Stack>
        </Paper>
      </Stack>
    </Container>
  );
}
