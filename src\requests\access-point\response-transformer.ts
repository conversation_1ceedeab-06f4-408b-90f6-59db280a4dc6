import { z } from 'zod';

// User types and status enums for access points
export const ACCESS_POINT_USER_STATUS = ['PENDING', 'ACTIVE', 'SUSPENDED'] as const;

// Backend access point schema - what comes from Prisma/database
export const accessPointBackendSchema = z.object({
  id: z.string().uuid(),
  name: z.string(),
  email: z.string().email(),
  phone: z.string(),
  status: z.enum(ACCESS_POINT_USER_STATUS),
  email_verified: z.boolean().optional(),
  created_at: z.string().optional(),
  updated_at: z.string().optional(),
  // Access Operator specific fields
  business_name: z.string().nullable().optional(),
  address: z.string().nullable().optional(),
  geo_latitude: z.number().nullable().optional(),
  geo_longitude: z.number().nullable().optional(),
  approved: z.boolean().nullable().optional(),
});

// Access point API response transformer - converts backend to frontend format
export const accessPointApiResponseSchema = (item: z.infer<typeof accessPointBackendSchema>) => ({
  id: item.id,
  name: item.name,
  email: item.email,
  phone: item.phone,
  userType: 'ACCESS_OPERATOR' as const,
  status: item.status,
  emailVerified: item.email_verified ?? false,
  createdAt: item.created_at,
  updatedAt: item.updated_at,
  // Access Operator specific fields
  businessName: item.business_name ?? null,
  address: item.address ?? null,
  geoLatitude: item.geo_latitude ?? null,
  geoLongitude: item.geo_longitude ?? null,
  approved: item.approved ?? null,
});

// Transformed access point schema using the transformer function
export const accessPointSchema = accessPointBackendSchema.transform(accessPointApiResponseSchema);

// Multiple access points schema
export const accessPointsBackendSchema = z.array(accessPointBackendSchema);

// Get single access point API response schema
export const getAccessPointApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    access_operator: accessPointBackendSchema,
  }),
}).transform(({ success, message, data }) => ({
  success,
  message,
  data: {
    accessOperator: accessPointApiResponseSchema(data.access_operator),
  },
}));

// Get multiple access points API response schema
export const getAccessPointsApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    access_operators: accessPointsBackendSchema,
    pagination: z.object({
      page: z.number(),
      limit: z.number(),
      total: z.number(),
      totalPages: z.number(),
      hasNext: z.boolean(),
      hasPrev: z.boolean(),
    }).optional(),
  }),
}).transform(({ success, message, data }) => ({
  success,
  message,
  data: {
    accessOperators: data.access_operators.map(accessPointApiResponseSchema),
    pagination: data.pagination,
  },
}));

// Create access point API response schema
export const createAccessPointApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    access_operator: accessPointBackendSchema,
  }),
}).transform(({ success, message, data }) => ({
  success,
  message,
  data: {
    accessOperator: accessPointApiResponseSchema(data.access_operator),
  },
}));

// Update access point API response schema
export const updateAccessPointApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    access_operator: accessPointBackendSchema,
  }),
}).transform(({ success, message, data }) => ({
  success,
  message,
  data: {
    accessOperator: accessPointApiResponseSchema(data.access_operator),
  },
}));

// Export schemas collection
export const accessPointResponseSchemas = {
  accessPointBackend: accessPointBackendSchema,
  accessPoint: accessPointSchema,
  accessPointApiResponse: accessPointApiResponseSchema,
  getAccessPointApiResponse: getAccessPointApiResponseSchema,
  getAccessPointsApiResponse: getAccessPointsApiResponseSchema,
  createAccessPointApiResponse: createAccessPointApiResponseSchema,
  updateAccessPointApiResponse: updateAccessPointApiResponseSchema,
  ACCESS_POINT_USER_STATUS,
};

export default accessPointResponseSchemas;
