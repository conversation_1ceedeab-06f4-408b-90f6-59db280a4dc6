import { z } from 'zod';

// My shipments request schema - for query parameters
export const getMyShipmentsApiRequestSchema = z.object({
  page: z.number().int().min(0).optional()
    .default(0),
  limit: z.number().int().min(1).max(100)
    .optional()
    .default(12),
  search: z.string().optional(),
  status: z.string().optional(),
  sort: z.string().optional(),
  createdAtGte: z.string().optional(),
  createdAtLte: z.string().optional(),
  updatedAtGte: z.string().optional(),
  updatedAtLte: z.string().optional(),
});

// Request schemas collection
export const myRequestSchemas = {
  getMyShipments: getMyShipmentsApiRequestSchema,
};

export default myRequestSchemas;
