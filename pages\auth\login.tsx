import { useState } from 'react';
import { useRouter } from 'next/router';
import useTranslation from 'next-translate/useTranslation';
import Link from 'next/link';
import {
  TextInput,
  PasswordInput,
  Button,
  Paper,
  Title,
  Text,
  Container,
  Group,
  Anchor,
  Stack,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { z } from 'zod';
import { signIn } from 'next-auth/react';
import { notifications } from '@mantine/notifications';
import { loginSchema } from '../../src/requests';

type LoginFormValues = z.infer<typeof loginSchema>;

export default function LoginPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const { t } = useTranslation('auth');
  const { t: tCommon } = useTranslation('common');
  const { t: tError } = useTranslation('error');

  const form = useForm<LoginFormValues>({
    validate: (values) => {
      const parsed = loginSchema.safeParse(values);
      const errors: Partial<Record<keyof LoginFormValues, string | null>> = {};

      if (!parsed.success) {
        parsed.error.errors.forEach((issue) => {
          const field = issue.path[0] as keyof LoginFormValues;
          errors[field] = issue.message;
        });
      }

      return errors as Record<keyof LoginFormValues, string | null>;
    },
    initialValues: {
      email: '',
      password: '',
    },
  });

  const handleSubmit = async (values: LoginFormValues) => {
    setIsLoading(true);
    try {
      const result = await signIn('shipment-platform', {
        redirect: false,
        email: values.email,
        password: values.password,
      });

      if (result?.ok) {
        notifications.show({
          title: tCommon('success'),
          message: t('loginSuccess'),
          color: 'green',
        });
        const callbackUrl = router.query.callbackUrl as string || '/';
        router.push(callbackUrl);
      } else {
        // Handle different error types with proper translations
        let errorMessage: string;

        if (result?.error === 'CredentialsSignin' || result?.error === 'INVALID_CREDENTIALS') {
          errorMessage = tError('invalidCredentials');
        } else if (result?.error === 'INTERNAL_SERVER_ERROR') {
          errorMessage = tError('internalServerError');
        } else if (result?.error === 'LOGIN_FAILED') {
          errorMessage = tError('authenticationError');
        } else {
          errorMessage = result?.error || tCommon('somethingWentWrong');
        }

        notifications.show({
          title: tCommon('error'),
          message: errorMessage,
          color: 'red',
        });
      }
    } catch (error) {
      // This catch is for unexpected errors during the signIn process itself, not for auth failures.
      notifications.show({
        title: tCommon('error'),
        message: tCommon('somethingWentWrong'),
        color: 'red',
      });
    }
    setIsLoading(false);
  };

  return (
    <Container size={420} my={40}>
      <Title ta="center">
        {t('loginTitle')}
      </Title>
      <Text c="dimmed" size="sm" ta="center" mt={5}>
        {t('dontHaveAccount')}
        {' '}
        <Link href="/auth/register" passHref legacyBehavior>
          <Anchor component="a" size="sm">
            {t('createAccount')}
          </Anchor>
        </Link>
      </Text>

      <Paper withBorder shadow="md" p={30} mt={30} radius="md">
        <form onSubmit={form.onSubmit(handleSubmit)}>
          <Stack>
            <TextInput
              required
              label={t('emailAddress')}
              placeholder={t('enterEmail')}
              {...form.getInputProps('email')}
            />
            <PasswordInput
              required
              label={tCommon('password')}
              placeholder={t('enterPassword')}
              {...form.getInputProps('password')}
            />
            <Group justify="space-between" mt="lg">
              <Link href="/auth/forgot-password" passHref legacyBehavior>
                <Anchor component="a" size="sm">
                  {tCommon('forgotPassword')}
                </Anchor>
              </Link>
            </Group>
            <Button type="submit" fullWidth mt="xl" loading={isLoading}>
              {t('signIn')}
            </Button>
          </Stack>
        </form>
      </Paper>
    </Container>
  );
}
