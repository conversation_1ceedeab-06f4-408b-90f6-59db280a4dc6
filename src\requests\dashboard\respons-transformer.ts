import { z } from 'zod';

const QuickAction = z.object({
  label: z.string(),
  action: z.string(),
  icon: z.string(),
});

// Removed unused specific dashboard schemas since we're using FlexibleDashboardData

const UserInfo = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string(),
  user_type: z.enum(['CUSTOMER', 'ACCESS_OPERATOR', 'CAR_OPERATOR', 'ADMIN']),
  status: z.string(),
  email_verified: z.boolean(),
});

// Create a more flexible dashboard data schema that can handle partial data
const FlexibleDashboardData = z.object({
  // Common fields that might appear in any dashboard
  quick_actions: z.array(QuickAction).optional(),
  recent_shipments: z.array(z.object({
    id: z.string(),
    tracking_code: z.string(),
    status: z.string(),
    description: z.string().nullable(),
    created_at: z.string(),
    estimated_delivery: z.string().nullable().optional(),
    originAO: z.object({ business_name: z.string(), address: z.string() }).optional(),
    destAO: z.object({ business_name: z.string(), address: z.string() }).optional(),
    customer: z.object({ name: z.string(), phone: z.string().nullable() }).optional(),
  })).optional(),

  // Customer-specific fields
  shipment_stats: z.object({
    total: z.number().optional(),
    pending: z.number().optional(),
    awaiting_pickup: z.number().optional(),
    in_transit: z.number().optional(),
    arrived_at_destination: z.number().optional(),
    delivered: z.number().optional(),
    cancelled: z.number().optional(),
    assigned: z.number().optional(),
    completed: z.number().optional(),
    completion_rate: z.number().optional(),
    active: z.number().optional(),
    delivery_rate: z.number().optional(),
  }).optional(),

  // Access operator specific fields
  business_info: z.object({
    business_name: z.string().nullable(),
    address: z.string().nullable(),
    approved: z.boolean(),
    has_location: z.boolean(),
  }).optional(),

  qr_label_stats: z.object({
    unused: z.number(),
    assigned: z.number(),
    total: z.number(),
  }).optional(),

  // Car operator specific fields
  operator_info: z.object({
    license_number: z.string().nullable(),
    vehicle_info: z.string().nullable(),
    approved: z.boolean(),
    pickup_access_point: z
      .object({ business_name: z.string(), address: z.string() })
      .nullable()
      .optional(),
    dropoff_access_point: z
      .object({ business_name: z.string(), address: z.string() })
      .nullable()
      .optional(),
    has_access_points: z.boolean().nullable().optional(),
  }).optional(),

  // Admin specific fields
  user_stats: z.object({
    total: z.number(),
    customers: z.number(),
    access_operators: z.number(),
    car_operators: z.number(),
    pending_approvals: z.number(),
  }).optional(),

  recent_users: z.array(
    z.object({
      id: z.string(),
      name: z.string(),
      email: z.string().email(),
      user_type: z.enum(['CUSTOMER', 'ACCESS_OPERATOR', 'CAR_OPERATOR', 'ADMIN']),
      status: z.string(),
      created_at: z.string(),
    }),
  ).optional(),
});

const DashboardResponseBackendSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    user_info: UserInfo,
    dashboard_data: FlexibleDashboardData, // Use flexible schema as primary
  }),
});

// Helper to convert object keys from snake_case to camelCase recursively
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const snakeToCamel = (data: unknown): any => {
  if (Array.isArray(data)) {
    return data.map(snakeToCamel);
  }
  if (data !== null && typeof data === 'object') {
    return Object.entries(data as Record<string, unknown>).reduce<Record<string, unknown>>(
      (acc, [key, value]) => {
        const camelKey = key.replace(/_([a-z])/g, (_, char) => char.toUpperCase());
        acc[camelKey] = snakeToCamel(value);
        return acc;
      },
      {},
    );
  }
  return data;
};

export const DashboardResponseSchema = DashboardResponseBackendSchema.transform((item) => snakeToCamel(item));
