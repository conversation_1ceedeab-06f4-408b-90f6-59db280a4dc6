import { useRouter } from 'next/router';
import Link from 'next/link';
import useTranslation from 'next-translate/useTranslation';
import {
  TextInput,
  PasswordInput,
  Button,
  Paper,
  Title,
  Text,
  Container,
  Anchor,
  Stack,
  Select,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { z } from 'zod';
import { registrationSchema, USER_TYPES, emailSchema } from '../../src/requests';
import { useRegisterMutation } from '../../src/requests/hooks/enhanced-mutations';
import { notifications } from '@mantine/notifications';
import { resendVerificationOtpRequest } from '../../src/requests/auth/calls';
import { useState } from 'react';
import { PhoneNumberInput } from '../../src/components/common/PhoneNumberInput';
import { API_ENDPOINT } from '../../src/data';

type RegisterFormValues = z.infer<typeof registrationSchema>;

export default function RegisterPage() {
  const router = useRouter();
  const [duplicateEmail, setDuplicateEmail] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const { t } = useTranslation('auth');
  const { t: tCommon } = useTranslation('common');

  // User type options with translations
  const userTypeOptions = USER_TYPES.map((type: string) => ({
    value: type,
    label: t(`userTypes.${type}`),
  }));

  const form = useForm<RegisterFormValues>({
    validate: (values) => {
      const parsed = registrationSchema.safeParse(values);
      const errors: Partial<Record<keyof RegisterFormValues, string | null>> = {};

      if (!parsed.success) {
        parsed.error.errors.forEach((issue) => {
          const field = issue.path[0] as keyof RegisterFormValues;
          errors[field] = issue.message;
        });
      }

      return errors as Record<keyof RegisterFormValues, string | null>;
    },
    initialValues: {
      userType: 'CUSTOMER' as RegisterFormValues['userType'],
      name: '',
      email: '',
      password: '',
      phone: '',
    },
  });

  // Enhanced register mutation with custom error handling
  const registerMutation = useRegisterMutation({
    successMessage: t('registrationSuccess'),
    onSuccess: () => {
      router.push(`${API_ENDPOINT.auth.verifyEmail}?email=${form.values.email}`);
    },
    onError: (error: unknown) => {
      let errorMessage: string | undefined;
      if (typeof error === 'string') errorMessage = error;
      else if (error && typeof error === 'object' && 'message' in error && typeof (error as Record<string, unknown>).message === 'string') {
        errorMessage = (error as Record<string, unknown>).message as string;
      }

      if (errorMessage === 'User with this email already exists') {
        setDuplicateEmail(true);
      }
    },
  });

  const handleSubmit = (values: RegisterFormValues) => {
    registerMutation.mutate({ data: values });
  };

  const handleResendOtp = async () => {
    const { email } = form.values;

    // basic validation
    try {
      emailSchema.parse({ email });
    } catch {
      notifications.show({
        title: t('invalidEmail'),
        message: tCommon('somethingWentWrong'),
        color: 'red',
      });
      return;
    }

    setIsResending(true);
    try {
      const response = await resendVerificationOtpRequest({ data: { email } });
      if (response.success) {
        notifications.show({
          title: t('otpResent'),
          message: response.message || t('otpSentMessage'),
          color: 'blue',
        });
        router.push(`/auth/verify-email?email=${encodeURIComponent(email)}`);
      } else {
        notifications.show({
          title: t('failedToResendOtp'),
          message: response.message || t('failedToResendOtp'),
          color: 'red',
        });
      }
    } catch (err: unknown) {
      const msg = (err as { message?: string })?.message || t('failedToResendOtp');
      notifications.show({
        title: t('failedToResendOtp'),
        message: msg,
        color: 'red',
      });
    }
    setIsResending(false);
  };

  return (
    <Container size={460} my={40}>
      <Title ta="center">
        {t('createYourAccount')}
      </Title>
      <Text c="dimmed" size="sm" ta="center" mt={5}>
        {t('alreadyHaveAccountSignIn')}
        {' '}
        <Link href="/auth/login" passHref legacyBehavior>
          <Anchor component="a" size="sm">
            {t('signIn')}
          </Anchor>
        </Link>
      </Text>

      <Paper withBorder shadow="md" p={30} mt={30} radius="md">
        <form onSubmit={form.onSubmit(handleSubmit)}>
          <Stack>
            <Select
              withAsterisk
              label={t('iAmA')}
              placeholder={t('selectUserType')}
              data={userTypeOptions}
              {...form.getInputProps('userType')}
            />
            <TextInput
              withAsterisk
              label={t('fullName')}
              placeholder={t('enterFullName')}
              {...form.getInputProps('name')}
            />
            <TextInput
              withAsterisk
              label={t('emailAddress')}
              placeholder={t('enterEmail')}
              {...form.getInputProps('email')}
            />
            <PasswordInput
              withAsterisk
              label={tCommon('password')}
              placeholder={t('enterPassword')}
              {...form.getInputProps('password')}
            />
            <PhoneNumberInput
              form={form}
              field="phone"
              label={t('phoneNumber')}
              placeholder={t('enterPhoneNumber')}
              required
            />
            <Button type="submit" fullWidth mt="xl" loading={registerMutation.isPending}>
              {t('signUp')}
            </Button>
            {duplicateEmail && (
              <Button
                variant="subtle"
                onClick={handleResendOtp}
                loading={isResending}
              >
                Resend verification code
              </Button>
            )}
          </Stack>
        </form>
      </Paper>
    </Container>
  );
}
