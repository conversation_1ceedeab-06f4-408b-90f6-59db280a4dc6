/* eslint-disable sonarjs/no-all-duplicated-branches */
/* eslint-disable complexity */
import { useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import useTranslation from 'next-translate/useTranslation';
import {
  Container,
  Title,
  Text,
  Paper,
  Grid,
  Card,
  Group,
  Badge,
  Stack,
  Loader,
  Center,
  Button,
} from '@mantine/core';
import {
  IconPackage,
  IconTruck,
  IconMapPin,
  IconPlus,
} from '@tabler/icons-react';
import { useQuery } from '@tanstack/react-query';
import { getDashboardQuery } from '../../src/requests/dashboard/call';
import type {
  DashboardApiResponse,
  CustomerDashboardData,
  RecentShipment,
} from '../../src/requests/dashboard/type';

const formatNumber = (n?: number) => (typeof n === 'number' ? n.toLocaleString() : '—');

function DashboardHeader() {
  const router = useRouter();
  const isRTL = router.locale === 'ar';
  const { t } = useTranslation('dashboard');
  const { data: session } = useSession();

  const textContent = (
    <div style={{ textAlign: isRTL ? 'right' : 'left' }}>
      <Title
        order={1}
        mb="sm"
        style={{
          textAlign: isRTL ? 'right' : 'left',
          direction: isRTL ? 'rtl' : 'ltr',
        }}
      >
        {t('customerDashboard')}
      </Title>
      <Text
        size="lg"
        c="dimmed"
        style={{
          textAlign: isRTL ? 'right' : 'left',
          direction: isRTL ? 'rtl' : 'ltr',
        }}
      >
        {t('welcomeBack')}
        {isRTL ? '،' : ','}
        {' '}
        {' '}
        {session?.user?.name || session?.user?.email}
        {isRTL ? '!' : '!'}
        {' '}
        {' '}
        {t('manageShipmentsAndTrack')}
      </Text>
    </div>
  );

  const buttonContent = (
    <Button
      leftSection={!isRTL ? <IconPlus size="1rem" /> : undefined}
      rightSection={isRTL ? <IconPlus size="1rem" /> : undefined}
      size="md"
      onClick={() => router.push('/customer/shipments/create')}
      style={{
        direction: isRTL ? 'rtl' : 'ltr',
      }}
    >
      {t('createNewShipment')}
    </Button>
  );

  return (
    <Group
      justify="space-between"
      align="flex-start"
      style={{
        direction: isRTL ? 'rtl' : 'ltr',
      }}
    >
      {isRTL ? (
        <>
          {buttonContent}
          {textContent}
        </>
      ) : (
        <>
          {textContent}
          {buttonContent}
        </>
      )}
    </Group>
  );
}

export default function CustomerDashboard() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { t } = useTranslation('dashboard');
  const {
    data: dashboard,
    isLoading: isDashboardLoading,
  } = useQuery<DashboardApiResponse>(getDashboardQuery({ enabled: status === 'authenticated' }));

  useEffect(() => {
    if (status === 'loading') return;

    if (!session) {
      router.push('/auth/login');
      return;
    }

    // Check if user is a customer
    if (session.user?.user_type !== 'CUSTOMER') {
      router.push('/'); // Redirect to home if not a customer
    }
  }, [session, status, router]);

  if (status === 'loading' || isDashboardLoading) {
    return (
      <Center style={{ height: '100vh' }}>
        <Loader />
      </Center>
    );
  }

  if (!session || session.user?.user_type !== 'CUSTOMER') {
    return null; // Will redirect
  }

  const cData = dashboard?.data?.dashboardData as CustomerDashboardData | undefined;
  const shipmentStats = cData?.shipmentStats ?? {};
  const recentShipments = cData?.recentShipments ?? [];

  return (
    <Container size="xl" p="xl">
      <Stack gap="xl">
        {/* Header */}
        <DashboardHeader />

        {/* Quick Stats */}
        <Grid>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card shadow="sm" padding="lg" radius="md" withBorder>
              <Group justify="space-between" mb="xs">
                <Text fw={500}>{t('activeShipments')}</Text>
                <IconPackage size="1.4rem" color="blue" />
              </Group>
              <Text size="xl" fw={700} c="blue">
                {formatNumber(shipmentStats.inTransit)}
              </Text>
              <Text size="sm" c="dimmed">
                {t('currentlyInTransit')}
              </Text>
            </Card>
          </Grid.Col>

          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card shadow="sm" padding="lg" radius="md" withBorder>
              <Group justify="space-between" mb="xs">
                <Text fw={500}>{t('completed')}</Text>
                <IconTruck size="1.4rem" color="green" />
              </Group>
              <Text size="xl" fw={700} c="green">
                {formatNumber(shipmentStats.delivered)}
              </Text>
              <Text size="sm" c="dimmed">
                {t('thisMonth')}
              </Text>
            </Card>
          </Grid.Col>

          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card shadow="sm" padding="lg" radius="md" withBorder>
              <Group justify="space-between" mb="xs">
                <Text fw={500}>{t('pending')}</Text>
                <IconMapPin size="1.4rem" color="orange" />
              </Group>
              <Text size="xl" fw={700} c="orange">
                {formatNumber(shipmentStats.pending)}
              </Text>
              <Text size="sm" c="dimmed">
                {t('awaitingPickup')}
              </Text>
            </Card>
          </Grid.Col>

          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card shadow="sm" padding="lg" radius="md" withBorder>
              <Group justify="space-between" mb="xs">
                <Text fw={500}>{t('totalShipments')}</Text>
                <IconPackage size="1.4rem" color="gray" />
              </Group>
              <Text size="xl" fw={700}>
                {formatNumber(shipmentStats.total)}
              </Text>
              <Text size="sm" c="dimmed">
                {t('allTime')}
              </Text>
            </Card>
          </Grid.Col>
        </Grid>

        {/* Recent Shipments */}
        <Paper shadow="sm" p="lg" radius="md" withBorder>
          <Title order={3} mb="md">
            {t('recentShipments')}
          </Title>
          <Stack gap="md">
            {recentShipments.length === 0 && (
              <Text size="sm" c="dimmed">{t('noRecentShipments')}</Text>
            )}
            {recentShipments.slice(0, 5).map((s: RecentShipment) => (
              <Card key={s.id} shadow="xs" padding="md" radius="sm" withBorder>
                <Group justify="space-between" align="flex-start">
                  <div>
                    <Text fw={500} mb="xs">
                      {s.trackingCode}
                    </Text>
                    <Text size="sm" c="dimmed" mb="xs">
                      Created:
                      {' '}
                      {new Date(s.createdAt).toLocaleDateString()}
                    </Text>
                  </div>
                  <Badge color="blue" variant="light">
                    {s.status}
                  </Badge>
                </Group>
              </Card>
            ))}
          </Stack>
        </Paper>
      </Stack>
    </Container>
  );
}
