import { useState } from 'react';
import {
  Container,
  Loader,
  Center,
} from '@mantine/core';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import { useIsClient } from '../../../src/hooks/useIsClient';
import {
  AOShipmentsTable,
  AOShipmentDetailView,
  ShipmentArrivalModal,
  PickupProcessModal,
} from '../../../src/components/shipment';
import { Shipment } from '../../../src/requests/shipment';

export default function MyAssignedShipmentsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const isClient = useIsClient();
  const [selectedShipment, setSelectedShipment] = useState<Shipment | null>(null);
  const [scanArrivalShipment, setScanArrivalShipment] = useState<Shipment | null>(null);
  const [pickupShipment, setPickupShipment] = useState<Shipment | null>(null);

  const handleViewShipment = (shipment: Shipment) => {
    setSelectedShipment(shipment);
  };

  const handleScanArrival = (shipment: Shipment) => {
    setScanArrivalShipment(shipment);
  };

  const handlePickup = (shipment: Shipment) => {
    setPickupShipment(shipment);
  };

  const handleScanArrivalSuccess = () => {
    setScanArrivalShipment(null);
    setSelectedShipment(null); // Return to list view if in detail view
  };

  const handlePickupSuccess = () => {
    setPickupShipment(null);
    setSelectedShipment(null); // Return to list view if in detail view
  };

  const handleBackToList = () => {
    setSelectedShipment(null);
  };

  // Redirect if not authenticated or not AO user
  if (isClient && status === 'authenticated' && session?.user?.user_type !== 'ACCESS_OPERATOR') {
    router.push('/');
    return null;
  }

  // Show loading while checking authentication
  if (!isClient || status === 'loading') {
    return (
      <Center h="100vh">
        <Loader size="lg" />
      </Center>
    );
  }

  // Redirect to login if not authenticated
  if (status === 'unauthenticated') {
    router.push('/auth/login');
    return null;
  }

  return (
    <Container size="xl" py="md">
      {selectedShipment ? (
        <AOShipmentDetailView
          shipment={selectedShipment}
          onBack={handleBackToList}
          onScanArrival={handleScanArrival}
          onPickup={handlePickup}
        />
      ) : (
        <AOShipmentsTable
          onViewShipment={handleViewShipment}
          onScanArrival={handleScanArrival}
          onPickup={handlePickup}
        />
      )}

      <ShipmentArrivalModal
        opened={!!scanArrivalShipment}
        onClose={() => setScanArrivalShipment(null)}
        shipment={scanArrivalShipment}
        onSuccess={handleScanArrivalSuccess}
      />

      {/* Use new PickupProcessModal for delivery to receiver */}
      <PickupProcessModal
        opened={!!pickupShipment}
        onClose={() => setPickupShipment(null)}
        shipment={pickupShipment}
        onSuccess={handlePickupSuccess}
        pickupType="AO_DELIVERY"
      />
    </Container>
  );
}
