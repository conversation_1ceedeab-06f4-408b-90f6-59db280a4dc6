import { NextApiRequest, NextApiResponse } from 'next';
import { BACKEND_API } from '../../../src/lib/axios';
import { API_ENDPOINT, HTTP_CODE } from '../../../src/data';
import { forgotPasswordSchema } from '../../../src/requests';
import createApiError from '../../../src/utils/create-api-error';
import { forgotPasswordApiResponseSchema } from '../../../src/requests/auth/response-transform';

async function handlePost(req: NextApiRequest, res: NextApiResponse) {
  try {
    const validatedData = forgotPasswordSchema.parse(req.body);

    const response = await BACKEND_API.post(
      API_ENDPOINT.auth.forgotPassword,
      validatedData,
    );

    const validatedResponse = forgotPasswordApiResponseSchema.parse(response.data);
    return res.status(HTTP_CODE.SUCCESS).json(validatedResponse);
  } catch (e) {
    const error = createApiError({ error: e });
    return res.status(error.code).json(error);
  }
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'POST') {
    return handlePost(req, res);
  }

  res.setHeader('Allow', ['POST']);
  return res.status(HTTP_CODE.METHOD_NOT_ALLOWED).json({
    success: false,
    message: `Method ${req.method} not allowed`,
  });
}
