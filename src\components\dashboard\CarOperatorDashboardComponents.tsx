import {
  Title,
  Text,
  Paper,
  Grid,
  Card,
  Group,
  Badge,
  Button,
  Stack,
} from '@mantine/core';
import {
  IconTruck,
  IconRoute,
  IconClock,
  IconMapPin,
  IconCheck,
} from '@tabler/icons-react';
import useTranslation from 'next-translate/useTranslation';
import type {
  CarOperatorDashboardData,
  RecentShipment,
} from '../../requests/dashboard/type';

interface DashboardHeaderProps {
  // eslint-disable-next-line react/require-default-props
  userName?: string | null;
}

export function DashboardHeader({ userName }: DashboardHeaderProps) {
  const { t } = useTranslation('dashboard');
  return (
    <div>
      <Title order={1} mb="sm">
        {t('carOperatorDashboard')}
      </Title>
      <Text size="lg" c="dimmed">
        {t('welcomeBack')}
        ,
        {' '}
        {userName}
        !
        {' '}
        {t('manageDeliveries')}
      </Text>
    </div>
  );
}

interface QuickStatsGridProps {
  shipmentStats: CarOperatorDashboardData['shipmentStats'];
  formatNumber: (n?: number) => string;
}

export function QuickStatsGrid({ shipmentStats, formatNumber }: QuickStatsGridProps) {
  const { t } = useTranslation('dashboard');
  return (
    <Grid>
      <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
        <Card shadow="sm" padding="lg" radius="md" withBorder>
          <Group justify="space-between" mb="xs">
            <Text fw={500}>{t('activeDeliveriesText')}</Text>
            <IconTruck size="1.4rem" color="blue" />
          </Group>
          <Text size="xl" fw={700} c="blue">
            {formatNumber(shipmentStats?.assigned)}
          </Text>
          <Text size="sm" c="dimmed">
            {t('currentlyAssigned')}
          </Text>
        </Card>
      </Grid.Col>

      <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
        <Card shadow="sm" padding="lg" radius="md" withBorder>
          <Group justify="space-between" mb="xs">
            <Text fw={500}>{t('completedToday')}</Text>
            <IconCheck size="1.4rem" color="green" />
          </Group>
          <Text size="xl" fw={700} c="green">
            {formatNumber(shipmentStats?.completed)}
          </Text>
          <Text size="sm" c="dimmed">
            {t('deliveriesFinished')}
          </Text>
        </Card>
      </Grid.Col>

      <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
        <Card shadow="sm" padding="lg" radius="md" withBorder>
          <Group justify="space-between" mb="xs">
            <Text fw={500}>{t('pendingPickups')}</Text>
            <IconMapPin size="1.4rem" color="orange" />
          </Group>
          <Text size="xl" fw={700} c="orange">
            {formatNumber(shipmentStats?.assigned && shipmentStats.completed !== undefined ? shipmentStats.assigned - shipmentStats.completed : undefined)}
          </Text>
          <Text size="sm" c="dimmed">
            {t('awaitingCollection')}
          </Text>
        </Card>
      </Grid.Col>

      <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
        <Card shadow="sm" padding="lg" radius="md" withBorder>
          <Group justify="space-between" mb="xs">
            <Text fw={500}>{t('totalDistance')}</Text>
            <IconRoute size="1.4rem" color="gray" />
          </Group>
          <Text size="xl" fw={700}>
            {shipmentStats?.completionRate !== undefined ? `${shipmentStats.completionRate}%` : '—'}
          </Text>
          <Text size="sm" c="dimmed">
            {t('thisWeek')}
          </Text>
        </Card>
      </Grid.Col>
    </Grid>
  );
}

export function QuickActions() {
  const { t } = useTranslation('dashboard');
  return (
    <Paper shadow="sm" p="lg" radius="md" withBorder>
      <Title order={3} mb="md">
        {t('quickActions')}
      </Title>
      <Group>
        <Button leftSection={<IconTruck size="1rem" />} size="md">
          {t('viewAvailableJobs')}
        </Button>
        <Button variant="outline" size="md">
          {t('updateLocation')}
        </Button>
        <Button variant="outline" size="md">
          {t('reportIssue')}
        </Button>
      </Group>
    </Paper>
  );
}

interface RecentShipmentsListProps {
  recentShipments: RecentShipment[];
  isRTL: boolean;
}

export function RecentShipmentsList({ recentShipments, isRTL }: RecentShipmentsListProps) {
  const { t } = useTranslation('dashboard');
  return (
    <Paper shadow="sm" p="lg" radius="md" withBorder>
      <Title order={3} mb="md">
        {t('recentShipments')}
      </Title>
      <Stack gap="md">
        {recentShipments.length === 0 && (
          <Text size="sm" c="dimmed">{t('noRecentShipments')}</Text>
        )}
        {recentShipments.slice(0, 5).map((s: RecentShipment) => (
          <Card key={s.id} shadow="xs" padding="md" radius="sm" withBorder>
            <Group justify="space-between" align="flex-start">
              <div>
                <Text fw={500} mb="xs">
                  {s.trackingCode}
                </Text>
                <Text size="sm" c="dimmed" mb="xs">
                  {t('status')}
                  :
                  {' '}
                  {s.status}
                </Text>
                <Text size="sm" c="dimmed">
                  <IconClock
                    size="0.8rem"
                    style={{
                      display: 'inline',
                      [isRTL ? 'marginLeft' : 'marginRight']: '4px',
                    }}
                  />
                  {new Date(s.createdAt).toLocaleString()}
                </Text>
              </div>
              <Badge color="blue" variant="light">
                {s.status}
              </Badge>
            </Group>
          </Card>
        ))}
      </Stack>
    </Paper>
  );
}

interface VehicleStatusProps {
  operatorInfo: any; // eslint-disable-line @typescript-eslint/no-explicit-any
  shipmentStats: CarOperatorDashboardData['shipmentStats'];
  formatNumber: (n?: number) => string;
}

export function VehicleStatus({ operatorInfo, shipmentStats, formatNumber }: VehicleStatusProps) {
  const { t } = useTranslation('dashboard');
  return (
    <Paper shadow="sm" p="lg" radius="md" withBorder>
      <Title order={3} mb="md">
        {t('vehicleStatus')}
      </Title>
      <Grid>
        <Grid.Col span={{ base: 12, md: 6 }}>
          <Text fw={500} mb="xs">{t('vehicleInformation')}</Text>
          <Text size="sm" c="dimmed" mb="xs">
            {t('license')}
            :
            {' '}
            {operatorInfo?.licenseNumber || t('notProvided')}
          </Text>
          <Text size="sm" c="dimmed" mb="xs">
            {t('vehicleInfo')}
            :
            {' '}
            {operatorInfo?.vehicleInfo || t('notProvided')}
          </Text>
          <Text size="sm" c="dimmed">
            {t('approved')}
            :
            {' '}
            {operatorInfo?.approved ? t('yes') : t('no')}
          </Text>
        </Grid.Col>
        <Grid.Col span={{ base: 12, md: 6 }}>
          <Text fw={500} mb="xs">{t('currentStatus')}</Text>
          <Badge
            color={shipmentStats?.assigned && shipmentStats.assigned > 0 ? 'orange' : 'green'}
            variant="light"
            size="lg"
            mb="xs"
          >
            {shipmentStats?.assigned && shipmentStats.assigned > 0 ? t('busy') : t('available')}
          </Badge>
          <Text size="sm" c="dimmed">
            {t('activeDeliveries')}
            :
            {' '}
            {formatNumber(shipmentStats?.assigned)}
          </Text>
        </Grid.Col>
      </Grid>
    </Paper>
  );
}
