import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import useTranslation from 'next-translate/useTranslation';
import {
  PasswordInput,
  Button,
  Paper,
  Title,
  Text,
  Container,
  Group,
  Anchor,
  Stack,
  LoadingOverlay,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { z } from 'zod';
import { resetPasswordRequest, verifyResetTokenRequest } from '../../src/requests/auth/calls';
import { notifications } from '@mantine/notifications';
import { resetPasswordSchema } from '../../src/requests';

type ResetPasswordFormValues = z.infer<typeof resetPasswordSchema>;

export default function ResetPasswordPage() {
  const router = useRouter();
  const { token, email } = router.query; // Get token and email from URL query

  const [isLoading, setIsLoading] = useState(false);
  const [isValidatingToken, setIsValidatingToken] = useState(true);
  const [isTokenValid, setIsTokenValid] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasVerified, setHasVerified] = useState(false); // Add flag to prevent multiple verifications
  const { t } = useTranslation('auth');
  const { t: tCommon } = useTranslation('common');

  useEffect(() => {
    // Only run verification once when component mounts and we have the required params
    if (!router.isReady || !token || !email || typeof token !== 'string' || typeof email !== 'string' || hasVerified) {
      if (router.isReady && (!token || !email) && !hasVerified) {
        setIsValidatingToken(false);
        setError('Missing token or email. Please use the link from your email.');
        setHasVerified(true);
        notifications.show({
          title: tCommon('error'),
          message: 'Missing token or email. Please use the link from your email.',
          color: 'red',
        });
      }
      return;
    }

    const verifyToken = async () => {
      setHasVerified(true); // Mark as verified to prevent re-runs
      try {
        const response = await verifyResetTokenRequest({ token: token as string, email: email as string });
        if (response.success) {
          setIsTokenValid(true);
          setError(null);
        } else {
          setError(response.message || 'Invalid or expired token');
        }
      } catch (err) {
        setError('Invalid or expired token');
        setIsTokenValid(false);
      } finally {
        setIsValidatingToken(false);
      }
    };

    verifyToken();
  }, [router.isReady, token, email, tCommon, hasVerified]);

  const form = useForm<ResetPasswordFormValues>({
    validate: (values) => {
      const parsed = resetPasswordSchema.safeParse(values);
      const errors: Partial<Record<keyof ResetPasswordFormValues, string | null>> = {};

      if (!parsed.success) {
        parsed.error.errors.forEach((issue) => {
          const field = issue.path[0] as keyof ResetPasswordFormValues;
          errors[field] = issue.message;
        });
      }

      // Check if passwords match
      if (values.password !== values.confirmPassword) {
        errors.confirmPassword = t('passwordsDoNotMatch');
      }

      // Check password length
      if (values.password.length < 8) {
        errors.password = t('passwordMinLength');
      }

      return errors as Record<keyof ResetPasswordFormValues, string | null>;
    },
    initialValues: {
      email: typeof email === 'string' ? email : '',
      token: typeof token === 'string' ? token : '',
      password: '',
      confirmPassword: '',
    },
  });

  // Update form fields when the token and email from URL become available
  useEffect(() => {
    if (typeof token === 'string') {
      form.setFieldValue('token', token);
    }
    if (typeof email === 'string') {
      form.setFieldValue('email', email);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [token, email]);

  const handleSubmit = async (values: ResetPasswordFormValues) => {
    if (!token || typeof token !== 'string') {
      notifications.show({
        title: tCommon('error'),
        message: tCommon('somethingWentWrong'),
        color: 'red',
      });
      return;
    }
    setIsLoading(true);
    try {
      const response = await resetPasswordRequest({
        data: {
          email: typeof email === 'string' ? email : '',
          token: typeof token === 'string' ? token : values.token,
          password: values.password,
          confirmPassword: values.confirmPassword,
        },
      });

      if (response.success) {
        alert(t('passwordResetSuccess'));
        router.push('/auth/login');
      } else {
        notifications.show({
          title: tCommon('error'),
          message: response.message || tCommon('somethingWentWrong'),
          color: 'red',
        });
      }
    } catch (err) {
      notifications.show({
        title: tCommon('error'),
        message: tCommon('somethingWentWrong'),
        color: 'red',
      });
    }
    setIsLoading(false);
  };

  if (error) {
    return (
      <Container size={420} my={40}>
        <Paper withBorder shadow="md" p={30} radius="md">
          <div
            style={{
              border: '1px solid #fecaca',
              borderRadius: '0.375rem',
              padding: '1rem',
              marginBottom: '1rem',
            }}
          >
            <Title order={2} c="red" mb="xs">
              {t('invalidResetLink')}
            </Title>
            <Text c="red">{error}</Text>
          </div>
          <Group justify="center" mt="lg">
            <Link href="/auth/login" passHref legacyBehavior>
              <Anchor component="a" c="dimmed" size="sm">
                {t('backToSignIn')}
              </Anchor>
            </Link>
          </Group>
        </Paper>
      </Container>
    );
  }

  if (isValidatingToken || !router.isReady) {
    return (
      <Container size={420} my={40}>
        <Paper withBorder shadow="md" p={30} radius="md">
          <div style={{ textAlign: 'center' }}>
            <div
              style={{
                width: '48px',
                height: '48px',
                border: '4px solid #3b82f6',
                borderTop: '4px solid transparent',
                borderRadius: '50%',
                margin: '0 auto 1rem',
                animation: 'spin 1s linear infinite',
              }}
            />
            <Title order={2} ta="center">
              {t('verifyingResetToken')}
            </Title>
            <Text c="dimmed" mt="md">
              {t('pleaseWaitWhileWeVerify')}
            </Text>
          </div>
        </Paper>
      </Container>
    );
  }

  if (!isTokenValid) {
    return (
      <Container size={420} my={40}>
        <Paper withBorder shadow="md" p={30} radius="md">
          <div
            style={{
              backgroundColor: '#fee2e2',
              border: '1px solid #fecaca',
              borderRadius: '0.375rem',
              padding: '1rem',
              marginBottom: '1rem',
            }}
          >
            <Title order={2} c="red" mb="xs">
              {t('invalidResetLink')}
            </Title>
            <Text c="red">{t('invalidOrExpiredToken')}</Text>
          </div>
          <Group justify="center" mt="lg">
            <Link href="/auth/forgot-password" passHref legacyBehavior>
              <Button variant="filled" fullWidth mt="md">
                {t('requestNewResetLink')}
              </Button>
            </Link>
          </Group>
          <Group justify="center" mt="lg">
            <Link href="/auth/login" passHref legacyBehavior>
              <Anchor component="a" c="dimmed" size="sm">
                {t('backToSignIn')}
              </Anchor>
            </Link>
          </Group>
        </Paper>
      </Container>
    );
  }

  return (
    <Container size={420} my={40}>
      <Title ta="center">
        {t('resetPasswordTitle')}
      </Title>
      <Text c="dimmed" size="sm" ta="center" mt={5}>
        {t('resetPasswordSubtitle')}
      </Text>

      <Paper withBorder shadow="md" p={30} mt={30} radius="md">
        <LoadingOverlay visible={isLoading} />
        <form onSubmit={form.onSubmit(handleSubmit)}>
          <Stack>
            <PasswordInput
              required
              label={t('newPassword')}
              placeholder={t('enterNewPassword')}
              {...form.getInputProps('password')}
            />
            <PasswordInput
              required
              label={t('confirmNewPassword')}
              placeholder={t('confirmNewPassword')}
              {...form.getInputProps('confirmPassword')}
            />
            <Button type="submit" fullWidth mt="xl" loading={isLoading}>
              {tCommon('resetPassword')}
            </Button>
          </Stack>
        </form>
        <Group justify="center" mt="lg">
          <Link href="/auth/login" passHref legacyBehavior>
            <Anchor component="a" c="dimmed" size="sm">
              {t('backToSignIn')}
            </Anchor>
          </Link>
        </Group>
      </Paper>
    </Container>
  );
}
