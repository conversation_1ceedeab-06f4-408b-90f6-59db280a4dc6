/* eslint-disable no-console */
import { Group, Button } from '@mantine/core';
import { IconPackage } from '@tabler/icons-react';
import useTranslation from 'next-translate/useTranslation';

interface FormActionsProps {
  // eslint-disable-next-line react/require-default-props
  onCancel?: () => void;
  isLoading: boolean;
}

export function FormActions({ onCancel, isLoading }: FormActionsProps) {
  const { t } = useTranslation('shipments');

  return (
    <Group justify="flex-end" gap="sm" mt="lg">
      {onCancel && (
        <Button variant="light" onClick={onCancel} disabled={isLoading}>
          {t('cancel')}
        </Button>
      )}
      <Button
        type="submit"
        loading={isLoading}
        leftSection={<IconPackage size="1rem" />}
        onClick={(e) => {
          console.log('Create Shipment button clicked');
          console.log('Button event:', e);
          console.log('Is loading:', isLoading);
        }}
      >
        {t('createShipment')}
      </Button>
    </Group>
  );
}
