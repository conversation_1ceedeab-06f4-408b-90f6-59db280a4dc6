import { NextApiRequest, NextApiResponse } from 'next';
import { getJwt } from '../../../src/utils';
import { BACKEND_API } from '../../../src/lib/axios';
import { API_ENDPOINT, HTTP_CODE } from '../../../src/data';
import createApiError from '../../../src/utils/create-api-error';

async function handlePost(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { token } = await getJwt(req);

    try {
      await BACKEND_API.post(API_ENDPOINT.auth.logout, {}, {
        headers: {
          Authorization: token,
        },
      });
    } catch (backendError) {
      // eslint-disable-next-line no-console
      console.warn('Backend logout failed:', backendError);
    }

    return res.status(HTTP_CODE.SUCCESS).json({
      success: true,
      message: 'Logout successful',
    });
  } catch (e) {
    const error = createApiError({ error: e });
    return res.status(error.code).json(error);
  }
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'POST') {
    return handlePost(req, res);
  }

  res.setHeader('Allow', ['POST']);
  return res.status(HTTP_CODE.METHOD_NOT_ALLOWED).json({
    success: false,
    message: `Method ${req.method} not allowed`,
  });
}
