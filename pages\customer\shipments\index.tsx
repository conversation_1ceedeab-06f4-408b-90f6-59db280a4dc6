/* eslint-disable no-console */
import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import useTranslation from 'next-translate/useTranslation';
import {
  Container,
  Loader,
  Center,
  Modal,
  Button,
  Text,
  Group,
  Alert,
} from '@mantine/core';

import { ShipmentTable, ShipmentDetailView } from '../../../src/components/shipment';
import { useCancelShipmentMutation } from '../../../src/requests/hooks/enhanced-mutations';
import {
  Shipment,
  CancelShipmentApiRequest,
} from '../../../src/requests/shipment';

export default function CustomerShipmentsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [cancelModalOpen, setCancelModalOpen] = useState(false);
  const [shipmentToCancel, setShipmentToCancel] = useState<Shipment | null>(null);
  const [viewingShipment, setViewingShipment] = useState<Shipment | null>(null);
  const { t } = useTranslation('shipments');

  // Authentication and authorization check
  useEffect(() => {
    if (status === 'loading') return;

    if (!session) {
      router.push('/auth/login');
      return;
    }

    if (session.user?.user_type !== 'CUSTOMER') {
      router.push('/');
    }
  }, [session, status, router]);

  // Cancel shipment mutation
  const cancelMutation = useCancelShipmentMutation({
    // We rely on the hook to show notifications & invalidate relevant queries
    onSuccess: () => {
      // If the user is currently viewing the cancelled shipment, update its status locally
      setViewingShipment((prev) => {
        if (prev && shipmentToCancel && prev.id === shipmentToCancel.id) {
          return {
            ...prev,
            status: 'CANCELLED',
            cancellationReason: 'USER_CANCELLED',
            cancelledAt: new Date().toISOString(),
          } as Shipment;
        }
        return prev;
      });

      setCancelModalOpen(false);
      setShipmentToCancel(null);
    },
    // Remove custom error handling - let the hook handle notifications
  });

  const handleCreateNew = () => {
    try {
      router.push('/customer/shipments/create');
    } catch (error) {
      console.error('Router push error:', error);
    }
  };

  const handleViewShipment = (shipment: Shipment) => {
    setViewingShipment(shipment);
  };

  const handleBackToList = () => {
    setViewingShipment(null);
  };

  const handleCancelShipment = (shipment: Shipment) => {
    setShipmentToCancel(shipment);
    setCancelModalOpen(true);
  };

  const confirmCancel = () => {
    if (!shipmentToCancel) return;

    const requestData: CancelShipmentApiRequest = {
      reason: 'USER_CANCELLED', // Optional reason
    };

    cancelMutation.mutate({
      id: shipmentToCancel.id,
      data: requestData,
    });
  };

  const closeCancelModal = () => {
    setCancelModalOpen(false);
    setShipmentToCancel(null);
  };

  // Loading state
  if (status === 'loading') {
    return (
      <Center style={{ height: '50vh' }}>
        <Loader size="lg" />
      </Center>
    );
  }

  // Not authenticated or not a customer
  if (!session || session.user?.user_type !== 'CUSTOMER') {
    return null; // Will redirect
  }

  return (
    <Container size="xl" pt={10}>
      {viewingShipment ? (
        <ShipmentDetailView
          shipment={viewingShipment}
          onBack={handleBackToList}
          onCancel={handleCancelShipment}
        />
      ) : (
        <ShipmentTable
          onCreateNew={handleCreateNew}
          onViewShipment={handleViewShipment}
          onCancelShipment={handleCancelShipment}
          showCustomerColumn={false}
        />
      )}

      {/* Cancel Confirmation Modal */}
      <Modal
        opened={cancelModalOpen}
        onClose={closeCancelModal}
        title={t('cancelShipment')}
        centered
      >
        {shipmentToCancel && (
        <>
          <Text mb="md">
            {t('confirmCancelShipment')}
          </Text>

          <Alert color="orange" mb="md">
            <Text size="sm" fw={500}>
              {t('shipmentDetails')}
              :
            </Text>
            <Text size="sm">
              #
              {shipmentToCancel.id.slice(-8).toUpperCase()}
            </Text>
            <Text size="sm">{shipmentToCancel.description}</Text>
            <Text size="sm">
              {t('receiver')}
              :
              {shipmentToCancel.receiverName}
            </Text>
          </Alert>

          <Text size="sm" c="dimmed" mb="lg">
            {t('cancelWarning')}
          </Text>

          <Group justify="flex-end" gap="sm">
            <Button
              variant="light"
              onClick={closeCancelModal}
              disabled={cancelMutation.isPending}
            >
              {t('keepShipment')}
            </Button>
            <Button
              color="red"
              onClick={confirmCancel}
              loading={cancelMutation.isPending}
            >
              {t('cancelShipment')}
            </Button>
          </Group>
        </>
        )}
      </Modal>
    </Container>
  );
}
