/* eslint-disable complexity */
/* eslint-disable react/require-default-props */
/* eslint-disable sonarjs/no-nested-template-literals */
import { useEffect, useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>,
} from 'react-leaflet';
import { LatLngExpression, Icon } from 'leaflet';
import {
  Paper, Text, Button, Group, Stack, Alert, Badge, Card,
} from '@mantine/core';
import {
  IconMapPin, IconCheck, IconAlertCircle, IconCar,
} from '@tabler/icons-react';
import useTranslation from 'next-translate/useTranslation';

// Custom icons for different marker types
const pickupIcon = new Icon({
  iconUrl: `data:image/svg+xml;base64,${btoa(`
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="green" width="24" height="24">
      <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
    </svg>
  `)}`,
  iconSize: [30, 30],
  iconAnchor: [15, 30],
  popupAnchor: [0, -30],
});

const dropoffIcon = new Icon({
  iconUrl: `data:image/svg+xml;base64,${btoa(`
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="red" width="24" height="24">
      <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
    </svg>
  `)}`,
  iconSize: [30, 30],
  iconAnchor: [15, 30],
  popupAnchor: [0, -30],
});

interface AccessPoint {
  id: string;
  businessName: string;
  address: string;
  geoLatitude: number;
  geoLongitude: number;
}

interface TransitPointsMapProps {
  accessPoints: AccessPoint[];
  selectedPickupId?: string;
  selectedDropoffId?: string;
  onPickupSelect?: (accessPoint: AccessPoint) => void;
  onDropoffSelect?: (accessPoint: AccessPoint) => void;
  onSave?: (pickupId: string, dropoffId: string) => void;
  isEditable?: boolean;
}

export default function TransitPointsMap({
  accessPoints = [],
  selectedPickupId,
  selectedDropoffId,
  onPickupSelect,
  onDropoffSelect,
  onSave,
  isEditable = true,
}: TransitPointsMapProps) {
  const { t } = useTranslation('profile');
  const [isClient, setIsClient] = useState(false);
  const [selectionMode, setSelectionMode] = useState<'pickup' | 'dropoff' | null>(null);

  // Ensure this only renders on client side
  useEffect(() => {
    setIsClient(true);
  }, []);

  const selectedPickup = accessPoints.find((ap) => ap.id === selectedPickupId);
  const selectedDropoff = accessPoints.find((ap) => ap.id === selectedDropoffId);

  // Calculate center point for map
  const getMapCenter = (): LatLngExpression => {
    if (accessPoints.length === 0) {
      return [24.7136, 46.6753] as LatLngExpression; // Default to Riyadh
    }

    const avgLat = accessPoints.reduce((sum, ap) => sum + ap.geoLatitude, 0) / accessPoints.length;
    const avgLng = accessPoints.reduce((sum, ap) => sum + ap.geoLongitude, 0) / accessPoints.length;
    return [avgLat, avgLng] as LatLngExpression;
  };

  const handleMarkerClick = (accessPoint: AccessPoint) => {
    if (!isEditable) return;

    if (selectionMode === 'pickup') {
      onPickupSelect?.(accessPoint);
      setSelectionMode(null);
    } else if (selectionMode === 'dropoff') {
      onDropoffSelect?.(accessPoint);
      setSelectionMode(null);
    }
  };

  const handleSave = () => {
    if (selectedPickupId && selectedDropoffId && onSave) {
      onSave(selectedPickupId, selectedDropoffId);
    }
  };

  const getRoutePolyline = (): LatLngExpression[] => {
    if (selectedPickup && selectedDropoff) {
      return [
        [selectedPickup.geoLatitude, selectedPickup.geoLongitude] as LatLngExpression,
        [selectedDropoff.geoLatitude, selectedDropoff.geoLongitude] as LatLngExpression,
      ];
    }
    return [];
  };

  if (!isClient) {
    return <div>Loading map...</div>;
  }

  return (
    <Stack gap="md">
      {/* Selection Controls */}
      {isEditable && (
        <Paper p="md" withBorder>
          <Text fw={500} mb="sm">{t('selectTransitPointsTitle')}</Text>
          <Group>
            <Button
              variant={selectionMode === 'pickup' ? 'filled' : 'outline'}
              color="green"
              leftSection={<IconMapPin size="1rem" />}
              onClick={() => setSelectionMode(selectionMode === 'pickup' ? null : 'pickup')}
            >
              {selectionMode === 'pickup' ? t('cancelPickupSelection') : t('selectPickupPointButton')}
            </Button>
            <Button
              variant={selectionMode === 'dropoff' ? 'filled' : 'outline'}
              color="red"
              leftSection={<IconMapPin size="1rem" />}
              onClick={() => setSelectionMode(selectionMode === 'dropoff' ? null : 'dropoff')}
            >
              {selectionMode === 'dropoff' ? t('cancelDropoffSelection') : t('selectDropoffPointButton')}
            </Button>
          </Group>

          {selectionMode && (
            <Alert icon={<IconAlertCircle size="1rem" />} color="blue" mt="sm">
              {t('clickMarkerToSelect')}
              {' '}
              {selectionMode}
              {' '}
              {t('point')}
              .
            </Alert>
          )}
        </Paper>
      )}

      {/* Selected Points Display */}
      <Group grow>
        <Card withBorder p="md">
          <Group mb="xs">
            <IconMapPin size="1rem" color="green" />
            <Text fw={500} c="green">{t('pickupPoint')}</Text>
          </Group>
          {selectedPickup ? (
            <div>
              <Text size="sm" fw={500}>{selectedPickup.businessName}</Text>
              <Text size="xs" c="dimmed">{selectedPickup.address}</Text>
            </div>
          ) : (
            <Text size="sm" c="dimmed">{t('noPickupPointSelected')}</Text>
          )}
        </Card>

        <Card withBorder p="md">
          <Group mb="xs">
            <IconMapPin size="1rem" color="red" />
            <Text fw={500} c="red">{t('dropoffPoint')}</Text>
          </Group>
          {selectedDropoff ? (
            <div>
              <Text size="sm" fw={500}>{selectedDropoff.businessName}</Text>
              <Text size="xs" c="dimmed">{selectedDropoff.address}</Text>
            </div>
          ) : (
            <Text size="sm" c="dimmed">{t('noDropoffPointSelected')}</Text>
          )}
        </Card>
      </Group>

      {/* Map */}
      <Paper withBorder style={{ height: '500px', overflow: 'hidden' }}>
        <MapContainer
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          {...({ center: getMapCenter() } as any)}
          zoom={10}
          style={{ height: '100%', width: '100%' }}
        >
          <TileLayer
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            {...({ attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors' } as any)}
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          />

          {/* Access Point Markers */}
          {accessPoints.map((accessPoint) => {
            const isPickup = accessPoint.id === selectedPickupId;
            const isDropoff = accessPoint.id === selectedDropoffId;
            // eslint-disable-next-line no-nested-ternary
            const icon = isPickup ? pickupIcon : isDropoff ? dropoffIcon : undefined;

            return (
              <Marker
                key={accessPoint.id}
                position={[accessPoint.geoLatitude, accessPoint.geoLongitude] as LatLngExpression}
                {...(icon ? { icon } : {})}
                eventHandlers={{
                  click: () => handleMarkerClick(accessPoint),
                }}
              >
                <Popup>
                  <div>
                    <Text size="sm" fw={500}>{accessPoint.businessName}</Text>
                    <Text size="xs" c="dimmed" mb="xs">{accessPoint.address}</Text>
                    {isPickup && <Badge color="green" size="xs">Pickup Point</Badge>}
                    {isDropoff && <Badge color="red" size="xs">Dropoff Point</Badge>}
                    {!isPickup && !isDropoff && isEditable && (
                      <Group gap="xs" mt="xs">
                        <Button
                          size="xs"
                          color="green"
                          onClick={() => {
                            setSelectionMode('pickup');
                            handleMarkerClick(accessPoint);
                          }}
                        >
                          Set as Pickup
                        </Button>
                        <Button
                          size="xs"
                          color="red"
                          onClick={() => {
                            setSelectionMode('dropoff');
                            handleMarkerClick(accessPoint);
                          }}
                        >
                          Set as Dropoff
                        </Button>
                      </Group>
                    )}
                  </div>
                </Popup>
              </Marker>
            );
          })}

          {/* Route Line */}
          {getRoutePolyline().length === 2 && (
            <Polyline
              positions={getRoutePolyline()}
              pathOptions={{
                color: 'blue',
                weight: 3,
                opacity: 0.7,
                dashArray: '10, 10',
              }}
            />
          )}
        </MapContainer>
      </Paper>

      {/* Save Button */}
      {isEditable && onSave && selectedPickupId && selectedDropoffId && (
        <Group justify="flex-end">
          <Button
            leftSection={<IconCheck size="1rem" />}
            onClick={handleSave}
          >
            {t('saveTransitRoute')}
          </Button>
        </Group>
      )}

      {/* Instructions */}
      {isEditable && (
        <Alert icon={<IconCar size="1rem" />} color="blue">
          <Text fw={500} mb="xs">{t('howToSelectTransitRoute')}</Text>
          <Text size="sm">
            {t('transitRouteStep1')}
            <br />
            {t('transitRouteStep2')}
            <br />
            {t('transitRouteStep3')}
          </Text>
        </Alert>
      )}
    </Stack>
  );
}
