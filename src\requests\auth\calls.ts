import { API_ENDPOINT } from '../../data';
import { CLIENT_API } from '../../lib/axios';
import { handleApiError } from '../../utils/handle-backend-error';
import { QueryClient } from '@tanstack/react-query';
import { CacheInvalidationManager, QUERY_KEYS } from '../cache-invalidation';
import {
  LoginQueryProps,
  RegisterQueryProps,
  ForgotPasswordQueryProps,
  VerifyResetTokenQueryProps,
  ResetPasswordQueryProps,
  ChangePasswordQueryProps,
  SendVerificationOtpQueryProps,
  VerifyEmailOtpQueryProps,
  ResendVerificationOtpQueryProps,
} from './type';

// Use centralized query keys
const queryKeys = QUERY_KEYS.auth;

/**
 * @description function calls handler in "/auth/login" api route to authenticate user
 * @param props
 * @returns user data and token
 */
const loginRequest = (props: LoginQueryProps) => {
  const { data } = props;
  return CLIENT_API.post(API_ENDPOINT.auth.login, data)
    .then((res) => res?.data)
    .catch(async (e) => {
      // Don't show notification here since login page handles its own error notifications
      await handleApiError(e, false); // showNotification=false
      throw e.response?.data;
    });
};

/**
 * @description function calls handler in "/auth/register" api route to register new user
 * @param props
 * @returns registration success message
 */
const registerRequest = (props: RegisterQueryProps) => {
  const { data } = props;
  return CLIENT_API.post(API_ENDPOINT.auth.register, data)
    .then((res) => res?.data)
    .catch(async (e) => {
      // Don't show notification here since useRegisterMutation will handle it with proper translations
      await handleApiError(e, false); // showNotification=false
      throw e.response?.data;
    });
};

/**
 * @description function calls handler in "/auth/forgot-password" api route to request password reset
 * @param props
 * @returns password reset email sent confirmation
 */
const forgotPasswordRequest = (props: ForgotPasswordQueryProps) => {
  const { data } = props;
  return CLIENT_API.post(API_ENDPOINT.auth.forgotPassword, data)
    .then((res) => res?.data)
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

/**
 * @description function calls handler in "/auth/verify-reset-token" api route to verify reset token
 * @param props
 * @returns token validity confirmation
 */
const verifyResetTokenRequest = (props: VerifyResetTokenQueryProps) => {
  const { token, email } = props;
  return CLIENT_API.get(API_ENDPOINT.auth.verifyResetToken, {
    params: { token, email },
  })
    .then((res) => res?.data)
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

/**
 * @description function calls handler in "/auth/reset-password" api route to reset password
 * @param props
 * @returns password reset success confirmation
 */
const resetPasswordRequest = (props: ResetPasswordQueryProps) => {
  const { data } = props;
  return CLIENT_API.post(API_ENDPOINT.auth.resetPassword, data)
    .then((res) => res?.data)
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

/**
 * @description function calls handler in "/auth/change-password" api route to change password
 * @param props
 * @returns password change success confirmation
 */
const changePasswordRequest = (props: ChangePasswordQueryProps) => {
  const { data } = props;
  return CLIENT_API.post(API_ENDPOINT.auth.changePassword, data)
    .then((res) => res?.data)
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

/**
 * @description function calls handler in "/auth/send-verification-otp" api route to send OTP
 * @param props
 * @returns OTP sent confirmation
 */
const sendVerificationOtpRequest = (props: SendVerificationOtpQueryProps) => {
  const { data } = props;
  return CLIENT_API.post(API_ENDPOINT.auth.sendVerificationOtp, data)
    .then((res) => res?.data)
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

/**
 * @description function calls handler in "/auth/verify-email-otp" api route to verify email with OTP
 * @param props
 * @returns email verification success with user data
 */
export const verifyEmailOtpRequest = (props: VerifyEmailOtpQueryProps) => {
  const { data } = props;
  return CLIENT_API.post(API_ENDPOINT.auth.verifyEmail, data)
    .then((res) => res?.data)
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

/**
 * @description function calls handler in "/auth/resend-verification-otp" api route to resend OTP
 * @param props
 * @returns OTP resent confirmation
 */
export const resendVerificationOtpRequest = (props: ResendVerificationOtpQueryProps) => {
  const { data } = props;
  return CLIENT_API.post(API_ENDPOINT.auth.resendVerificationOtp, data)
    .then((res) => res?.data)
    .catch(async (e) => {
      // Don't show notification here since calling code handles its own error notifications
      await handleApiError(e, false); // showNotification=false
      throw e.response?.data;
    });
};

// React Query configurations
export const loginQuery = (props: LoginQueryProps) => ({
  queryKey: [queryKeys.login],
  queryFn: () => loginRequest(props),
  refetchOnWindowFocus: false,
  enabled: props?.enabled,
});

export const registerQuery = (props: RegisterQueryProps) => ({
  queryKey: [queryKeys.register],
  queryFn: () => registerRequest(props),
  refetchOnWindowFocus: false,
  enabled: props?.enabled,
});

export const forgotPasswordQuery = (props: ForgotPasswordQueryProps) => ({
  queryKey: [queryKeys.forgotPassword],
  queryFn: () => forgotPasswordRequest(props),
  refetchOnWindowFocus: false,
  enabled: props?.enabled,
});

export const verifyResetTokenQuery = (props: VerifyResetTokenQueryProps) => ({
  queryKey: [queryKeys.verifyResetToken, props.token, props.email],
  queryFn: () => verifyResetTokenRequest(props),
  refetchOnWindowFocus: false,
  enabled: props?.enabled,
});

export const resetPasswordQuery = (props: ResetPasswordQueryProps) => ({
  queryKey: [queryKeys.resetPassword],
  queryFn: () => resetPasswordRequest(props),
  refetchOnWindowFocus: false,
  enabled: props?.enabled,
});

export const changePasswordQuery = (props: ChangePasswordQueryProps) => ({
  queryKey: [queryKeys.changePassword],
  queryFn: () => changePasswordRequest(props),
  refetchOnWindowFocus: false,
  enabled: props?.enabled,
});

export const verifyEmailOtpQuery = (props: VerifyEmailOtpQueryProps) => ({
  queryKey: [queryKeys.verifyEmailOtp],
  queryFn: () => verifyEmailOtpRequest(props),
  refetchOnWindowFocus: false,
  enabled: props?.enabled,
});

export const sendVerificationOtpQuery = (props: SendVerificationOtpQueryProps) => ({
  queryKey: [queryKeys.sendVerificationOtp],
  queryFn: () => sendVerificationOtpRequest(props),
  refetchOnWindowFocus: false,
  enabled: props?.enabled,
});

export const resendVerificationOtpQuery = (props: ResendVerificationOtpQueryProps) => ({
  queryKey: [queryKeys.resendVerificationOtp],
  queryFn: () => resendVerificationOtpRequest(props),
  refetchOnWindowFocus: false,
  enabled: props?.enabled,
});

// Mutation functions
export const loginMutation = () => ({
  mutationKey: [queryKeys.login],
  mutationFn: (props: LoginQueryProps) => loginRequest(props),
});

export const registerMutation = () => ({
  mutationKey: [queryKeys.register],
  mutationFn: (props: RegisterQueryProps) => registerRequest(props),
});

export const changePasswordMutation = () => ({
  mutationKey: [queryKeys.changePassword],
  mutationFn: (props: ChangePasswordQueryProps) => changePasswordRequest(props),
});

export const resetPasswordMutation = () => ({
  mutationKey: [queryKeys.resetPassword],
  mutationFn: (props: ResetPasswordQueryProps) => resetPasswordRequest(props),
});

/**
 * Enhanced mutation functions with built-in cache invalidation
 */

// Login mutation with auto-invalidation
export const loginMutationWithInvalidation = (queryClient: QueryClient) => {
  const cacheManager = new CacheInvalidationManager(queryClient);
  return {
    ...loginMutation(),
    onSuccess: async () => {
      await cacheManager.invalidateAfterAuth();
    },
  };
};

// Register mutation with auto-invalidation
export const registerMutationWithInvalidation = (queryClient: QueryClient) => {
  const cacheManager = new CacheInvalidationManager(queryClient);
  return {
    ...registerMutation(),
    onSuccess: async () => {
      await cacheManager.invalidateAfterAuth();
    },
  };
};

// Change password mutation with auto-invalidation
export const changePasswordMutationWithInvalidation = (queryClient: QueryClient) => {
  const cacheManager = new CacheInvalidationManager(queryClient);
  return {
    ...changePasswordMutation(),
    onSuccess: async () => {
      await cacheManager.invalidateAfterAuth();
    },
  };
};

export { forgotPasswordRequest, resetPasswordRequest, verifyResetTokenRequest };
