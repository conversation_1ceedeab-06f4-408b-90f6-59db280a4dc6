import { z } from 'zod';

// Common validation patterns
const VALIDATION = {
  PHONE: {
    // eslint-disable-next-line no-useless-escape
    PATTERN: /^[\+]?[\d\s\-\(\)]{7,20}$/,
    MESSAGE: 'Phone number must be a valid format (7-20 characters, may include +, spaces, dashes, parentheses)',
  },
  NAME: {
    MIN_LENGTH: 2,
    MESSAGE: 'Name must be at least 2 characters',
  },
  BUSINESS_NAME: {
    MIN_LENGTH: 2,
    MESSAGE: 'Business name must be at least 2 characters',
  },
  ADDRESS: {
    MIN_LENGTH: 5,
    MESSAGE: 'Address must be at least 5 characters',
  },
  LICENSE: {
    MIN_LENGTH: 3,
    MESSAGE: 'License number must be at least 3 characters',
  },
  VEHICLE_INFO: {
    MIN_LENGTH: 3,
    MESSAGE: 'Vehicle info must be at least 3 characters',
  },
};

// Base profile update schema - common fields for all user types (frontend camelCase)
const baseProfileUpdateSchema = z.object({
  name: z.string().optional().refine((val) => !val || val.length >= VALIDATION.NAME.MIN_LENGTH, {
    message: VALIDATION.NAME.MESSAGE,
  }),
  phone: z.string().optional().refine((val) => !val || val === '' || val.length >= 7, {
    message: 'Phone number must be at least 7 characters',
  }),
});

// Access Operator specific fields (frontend camelCase)
const accessOperatorProfileSchema = z.object({
  businessName: z.string().optional().refine((val) => !val || val === '' || val.length >= VALIDATION.BUSINESS_NAME.MIN_LENGTH, {
    message: VALIDATION.BUSINESS_NAME.MESSAGE,
  }),
  address: z.string().optional().refine((val) => !val || val === '' || val.length >= VALIDATION.ADDRESS.MIN_LENGTH, {
    message: VALIDATION.ADDRESS.MESSAGE,
  }),
  geoLatitude: z.number()
    .min(-90)
    .max(90, 'Latitude must be between -90 and 90')
    .optional()
    .nullable(),
  geoLongitude: z.number()
    .min(-180)
    .max(180, 'Longitude must be between -180 and 180')
    .optional()
    .nullable(),
});

// Car Operator specific fields (frontend camelCase)
const carOperatorProfileSchema = z.object({
  licenseNumber: z.string().optional().refine((val) => !val || val === '' || val.length >= VALIDATION.LICENSE.MIN_LENGTH, {
    message: VALIDATION.LICENSE.MESSAGE,
  }),
  vehicleInfo: z.string().optional().refine((val) => !val || val === '' || val.length >= VALIDATION.VEHICLE_INFO.MIN_LENGTH, {
    message: VALIDATION.VEHICLE_INFO.MESSAGE,
  }),
  // Allow CO users to send null, empty string, or valid UUID
  pickupAccessPointId: z.union([
    z.string().uuid('Invalid pickup access point ID'),
    z.literal(''),
    z.null(),
    z.undefined(),
  ]).optional(),
  dropoffAccessPointId: z.union([
    z.string().uuid('Invalid dropoff access point ID'),
    z.literal(''),
    z.null(),
    z.undefined(),
  ]).optional(),
});

// Frontend profile update schema - what the frontend sends (camelCase)
export const updateProfileApiRequestSchema = baseProfileUpdateSchema
  .merge(accessOperatorProfileSchema)
  .merge(carOperatorProfileSchema)
  .partial() // Make all fields optional for more flexibility
  .passthrough() // Allow additional fields to pass through before further refinements
  // Ensure that name is present and meets minimum length requirement
  .refine((data: unknown) => {
    const d = data as { name?: string };
    return typeof d.name === 'string' && d.name.trim().length >= VALIDATION.NAME.MIN_LENGTH;
  }, {
    message: VALIDATION.NAME.MESSAGE,
    path: ['name'],
  });

// Helper function to normalize access point IDs
const normalizeAccessPointId = (value: string | null | undefined): string | null => {
  if (value === '' || value === null || value === undefined) {
    return '';
  }
  return value;
};

// Backend profile update schema - transforms frontend camelCase to backend snake_case
export const updateProfileBackendRequestSchema = updateProfileApiRequestSchema.transform((data) => ({
  // Transform frontend camelCase to backend snake_case
  name: data.name,
  phone: data.phone,
  // Access Operator fields
  business_name: data.businessName,
  address: data.address,
  geo_latitude: data.geoLatitude,
  geo_longitude: data.geoLongitude,
  // Car Operator fields
  license_number: data.licenseNumber,
  vehicle_info: data.vehicleInfo,
  // Handle access point IDs: empty string, null, or undefined should all become null
  pickup_access_point_id: normalizeAccessPointId(data.pickupAccessPointId),
  dropoff_access_point_id: normalizeAccessPointId(data.dropoffAccessPointId),
}));

// Legacy export for backward compatibility
export const updateProfileSchema = updateProfileApiRequestSchema;

// Request schemas collection
export const profileRequestSchemas = {
  updateProfile: updateProfileApiRequestSchema,
  updateProfileBackend: updateProfileBackendRequestSchema,
};

export default profileRequestSchemas;
