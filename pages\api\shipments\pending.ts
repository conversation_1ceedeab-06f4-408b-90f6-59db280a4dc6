/* eslint-disable no-console */
import { NextApiRequest, NextApiResponse } from 'next';
import { getJwt } from '../../../src/utils';
import { BACKEND_API } from '../../../src/lib/axios';
import { HTTP_CODE } from '../../../src/data';
import { returnShipmentParams } from '../../../src/requests/shipment';
import { listShipmentsApiResponseSchema } from '../../../src/requests/shipment/response-transformer';
import createApiError from '../../../src/utils/create-api-error';

const apiMethods = {
  GET: 'GET',
} as const;

/**
 * @description Handle GET request to fetch pending shipments
 */
async function handleGetPendingShipments(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  try {
    // Set cache control headers to prevent caching
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');

    // Verify authentication
    const { token } = await getJwt(req);
    if (!token) {
      return res.status(HTTP_CODE.UNAUTHORIZED).json({
        success: false,
        message: 'Authentication required',
      });
    }

    // Get converted params
    const params = returnShipmentParams(req);

    // Temporary debug logging
    console.log('Frontend API - Query:', req.query);
    console.log('Frontend API - Params:', params);

    // Send to backend with converted params
    const response = await BACKEND_API.get('/shipments/pending', {
      headers: {
        Authorization: token,
      },
      params,
    });

    // Validate and return response
    const validatedResponse = listShipmentsApiResponseSchema.parse(response.data);
    return res.status(HTTP_CODE.SUCCESS).json(validatedResponse);
  } catch (error: unknown) {
    const apiError = createApiError({ error });
    return res.status(apiError.code).json(apiError);
  }
}

/**
 * @description API endpoint to get pending shipments for the authenticated user
 * @param req NextApiRequest
 * @param res NextApiResponse
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { method } = req;

  switch (method) {
    case apiMethods.GET:
      return handleGetPendingShipments(req, res);
    case 'OPTIONS':
      res.setHeader('Allow', [apiMethods.GET]);
      return res.status(200).end();
    default:
      res.setHeader('Allow', [apiMethods.GET]);
      return res.status(HTTP_CODE.METHOD_NOT_ALLOWED).json({
        success: false,
        message: `Method ${method} not allowed`,
        code: HTTP_CODE.METHOD_NOT_ALLOWED,
      });
  }
}
