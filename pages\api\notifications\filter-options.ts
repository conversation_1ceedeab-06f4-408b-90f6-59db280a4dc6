import { NextApiRequest, NextApiResponse } from 'next';
import { getJwt } from '../../../src/utils';
import { BACKEND_API } from '../../../src/lib/axios';
import { API_ENDPOINT, HTTP_CODE } from '../../../src/data';
import { validateFilterOptionsResponse } from '../../../src/requests/notifications';
import createApiError from '../../../src/utils/create-api-error';

const apiMethods = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
} as const;

async function handleGetFilterOptions(req: NextApiRequest, res: NextApiResponse, token: string) {
  try {
    const response = await BACKEND_API.get(API_ENDPOINT.notifications.filterOptions, {
      headers: {
        Authorization: token,
      },
    });

    const validatedResponse = validateFilterOptionsResponse(response.data);
    return res.status(HTTP_CODE.SUCCESS).json(validatedResponse);
  } catch (error) {
    const apiError = createApiError({ error });
    return res.status(apiError.code).json(apiError);
  }
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { method } = req;

  try {
    const { token } = await getJwt(req);
    if (!token) {
      return res.status(HTTP_CODE.UNAUTHORIZED).json(
        createApiError('Authentication required', 'UNAUTHORIZED'),
      );
    }

    switch (method) {
      case apiMethods.GET:
        return await handleGetFilterOptions(req, res, token as string);

      case apiMethods.POST:
      case apiMethods.PUT:
      default:
        return res.status(HTTP_CODE.METHOD_NOT_ALLOWED).json(
          createApiError(`Method ${method} not allowed`, 'METHOD_NOT_ALLOWED'),
        );
    }
  } catch (error) {
    return res.status(HTTP_CODE.INTERNAL_SERVER_ERROR).json(
      createApiError('Internal server error', 'INTERNAL_ERROR'),
    );
  }
}
