Ran tool
Ran tool
Read file: example/request/calls.ts
Read file: example/request/request-transformer.ts
Read file: example/request/response-transformer.ts
Read file: example/request/params.ts
Read file: example/request/response-transformer.ts
Read file: example/request/types.d.ts
Here is a comprehensive summary of the approach used in the `example` folder for API integration, request/response transformation, parameter handling, and React Query usage. This summary is designed to give you a deep understanding of the architecture and patterns, so you can reuse or extend them in the future.

---

## 1. **API Call Structure (`calls.ts`)**

- **Centralized API Client:**  
  All API calls are made using a centralized `ApiClient` (imported from a shared library), which abstracts HTTP methods (GET, POST, PUT, etc.) and handles base URL, headers, and authentication.

- **Error Handling:**  
  Each API call uses a shared `handleApiError` utility to process errors, ensuring consistent error management and user feedback.

- **Request Functions:**  
  For each API endpoint, there is a dedicated function (e.g., `getPaymentMethodsRequest`, `getPaymentMethodRequest`, `updatePaymentMethodRequest`). These functions:
  - Accept typed parameters (for filters, pagination, etc.).
  - Map frontend parameters to backend query params or request bodies.
  - Return the raw API response data or throw a processed error.

- **React Query Integration:**  
  Each API request is wrapped in a React Query configuration object (e.g., `getPaymentMethodsQuery`, `getPaymentMethodQuery`, `updatePaymentMethodMutation`).  
  These objects define:
  - `queryKey` or `mutationKey` for cache management.
  - `queryFn` or `mutationFn` to execute the request.
  - Options like `refetchOnWindowFocus`, `enabled`, and custom `retry` logic (e.g., do not retry on unauthorized errors).
  - This pattern enables easy integration with React Query hooks (`useQuery`, `useMutation`), providing caching, refetching, and error handling out of the box.

---

## 2. **Request Transformation (`request-transformer.ts`)**

- **Zod Schemas for Validation:**  
  All request payloads are validated and shaped using [Zod](https://zod.dev/) schemas. This ensures that data sent to the backend is always in the correct format and type-safe.

- **Frontend vs. Backend Schemas:**  
  - **Frontend Schema:**  
    Represents the data as used in the UI (e.g., camelCase, optional fields, nullable values).
  - **Backend Schema:**  
    Transforms frontend data into the format expected by the backend (e.g., snake_case, nested objects, stringified numbers).
    - Uses `.transform()` on Zod schemas to map and convert fields (e.g., `label` → `title`, numbers to strings, arrays to backend structures).
    - Handles optional and nullable fields, and maps complex/nested fields (like custom fields, currencies, badges).

- **Custom Field Handling:**  
  Custom fields are defined with their own schema and transformed both ways (frontend ↔ backend), supporting dynamic forms and extensible data structures.

---

## 3. **Response Transformation (`response-transformer.ts`)**

- **Zod Schemas for Response Validation:**  
  All API responses are validated and parsed using Zod schemas, ensuring the frontend only works with well-structured, type-safe data.

- **Backend to Frontend Mapping:**  
  - **Backend Schema:**  
    Mirrors the backend's response structure (e.g., nested `attributes`, snake_case, nested relations).
  - **Frontend Transformation:**  
    Functions like `paymentMethodApiResponseSchema` and `currencyApiResponseSchema` convert backend data into frontend-friendly objects (e.g., flattening, camelCase, extracting nested data, converting strings to numbers).
    - Handles relations (e.g., currencies, badges, icons) and maps them to simple objects or arrays.
    - Ensures all fields are present and correctly typed for UI consumption.

- **Pagination Handling:**  
  Response transformers also extract and normalize pagination metadata, making it easy to build paginated UIs.

---

## 4. **Parameter Handling (`params.ts`)**

- **Dynamic Query Parameter Construction:**  
  The `returnPaymentMethodsParams` function takes a Next.js API request and dynamically builds the query parameters for the backend.
  - Handles search, filtering (badges, tags), and pagination.
  - Maps frontend filter keys to backend query syntax (e.g., `filters[$and][0][$or][0][id][$containsi]`).
  - Supports population of related entities (badges, currencies, icons, custom fields) via `populate` parameters.
  - Converts frontend sort keys to backend sort keys using a mapping.

- **Extensible and Reusable:**  
  This approach allows for easy extension of filters and parameters, supporting complex search/filter UIs without duplicating logic.

---

## 5. **Types and Contracts (`types.d.ts`)**

- **Strong Typing:**  
  All request and response shapes are strongly typed using TypeScript and Zod inference. This ensures:
  - Type safety across the entire data flow (from UI to API and back).
  - Easy refactoring and code completion in IDEs.
  - Reduced runtime errors due to type mismatches.

- **Reusable Types:**  
  Types for filters, pagination, and request/response payloads are defined once and reused across the codebase, promoting consistency.

---

## 6. **React Query Usage**

- **Query and Mutation Factories:**  
  Instead of directly using `useQuery`/`useMutation` everywhere, the code exports factory functions that return the config objects for React Query. This:
  - Centralizes all API logic and options.
  - Makes it easy to update retry logic, cache keys, or other options in one place.
  - Promotes DRY (Don't Repeat Yourself) principles.

- **Cache and Refetch Control:**  
  By carefully constructing `queryKey` arrays and using options like `enabled` and `refetchOnWindowFocus`, the codebase achieves fine-grained control over when data is fetched, cached, or refetched.

---

## 7. **Best Practices and Benefits**

- **Separation of Concerns:**  
  - API logic, parameter construction, request/response transformation, and React Query integration are all separated into their own files/modules.
  - This makes the codebase modular, testable, and easy to maintain.

- **Validation Everywhere:**  
  - Both requests and responses are validated using Zod, catching errors early and ensuring data integrity.

- **Extensibility:**  
  - Adding new endpoints, filters, or data transformations is straightforward—just add new schemas, transformers, and config objects.

- **Consistency:**  
  - All API interactions follow the same pattern, making it easy for new developers to onboard and for the team to maintain high code quality.

---

## **Summary Table**

| Layer                | File(s)                        | Responsibility                                                                 |
|----------------------|--------------------------------|-------------------------------------------------------------------------------|
| API Calls            | `calls.ts`                     | Make HTTP requests, handle errors, wrap in React Query configs                |
| Request Transformation| `request-transformer.ts`       | Validate and transform frontend data to backend format using Zod               |
| Response Transformation| `response-transformer.ts`     | Validate and transform backend data to frontend format using Zod               |
| Params Construction  | `params.ts`                    | Build backend query params from frontend filters and search                    |
| Types                | `types.d.ts`                   | Define and infer types for requests, responses, filters, and pagination        |

---

## **How to Use This Approach**

1. **Define your types and Zod schemas** for both frontend and backend data shapes.
2. **Write request and response transformers** to map between frontend and backend formats.
3. **Centralize API calls** in a `calls.ts` file, handling errors and returning only the data you need.
4. **Wrap API calls in React Query config objects** for easy integration with hooks and cache management.
5. **Build dynamic parameter constructors** for complex filtering and searching.
6. **Validate everything**—both requests and responses—using Zod.
7. **Keep everything modular and DRY** for easy maintenance and extensibility.

---

This architecture is robust, scalable, and designed for large, complex applications where data integrity, maintainability, and developer experience are top priorities.