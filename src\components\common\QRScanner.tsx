/* eslint-disable no-console */
/* eslint-disable react/require-default-props */
/* eslint-disable sonarjs/no-all-duplicated-branches */
/* eslint-disable complexity */
/* eslint-disable sonarjs/cognitive-complexity */
import { useEffect, useRef, useState } from 'react';
import { BrowserMultiFormatReader, NotFoundException } from '@zxing/library';
import {
  Box,
  Button,
  Text,
  Paper,
} from '@mantine/core';
import { IconCamera, IconCameraOff } from '@tabler/icons-react';
import useTranslation from 'next-translate/useTranslation';

/**
 * QRScanner (no‑fake version)
 * ---------------------------
 * Same API surface as before but **without** the fallback that randomly emits a
 * fake QR code when the browser lacks `BarcodeDetector`.  If the native API is
 * unavailable, the component now surfaces a clear "unsupported" error so the
 * user knows they need a compatible browser (or you can later swap in a JS
 * decoder library such as `@zxing/library`).
 */

interface Props {
  /** Callback function when a QR code is successfully scanned */
  onScan: (result: string) => void;
  /** Optional callback function when an error occurs */
  onError?: (error: string) => void;
  /** Optional format to validate scanned QR codes */
  expectedFormat?: string;
  /** Width of the scanner viewport in pixels */
  width?: number;
  /** Height of the scanner viewport in pixels */
  height?: number;
  /** Title displayed above the scanner */
  title?: string;
  /** Description text displayed below the title */
  description?: string;
  /** Whether the scanner is active (for conditional rendering) */
  isActive?: boolean;
  /** Enable debug mode */
  debug?: boolean;
}

const defaultProps = {
  onError: () => {},
  expectedFormat: '',
  width: 400, // Increased size for better scanning
  height: 400, // Increased size for better scanning
  title: 'Scan QR Code',
  description: 'Point your camera at the QR code attached to the package',
  isActive: true,
  debug: false,
} as const;

const GENERIC_SCAN_ERROR_MESSAGE = 'Failed to scan QR code. Please make sure the QR code is well-lit and clearly visible.';

export default function QRScanner({
  onScan,
  onError = defaultProps.onError,
  expectedFormat = defaultProps.expectedFormat,
  width = defaultProps.width,
  height = defaultProps.height,
  title = defaultProps.title,
  description = defaultProps.description,
  isActive = defaultProps.isActive,
  debug = defaultProps.debug,
}: Props) {
  const { t } = useTranslation('shipments');
  const videoRef = useRef<HTMLVideoElement>(null);
  const codeReaderRef = useRef<BrowserMultiFormatReader | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const cleanup = () => {
    if (codeReaderRef.current) {
      try {
        codeReaderRef.current.reset();
        codeReaderRef.current = null;
      } catch (err) {
        // Ignore cleanup errors
      }
    }
    setIsScanning(false);
    setError(null);
  };

  const startScanning = async () => {
    if (isScanning) {
      cleanup();
      return;
    }

    if (!videoRef.current || !isActive) return;

    try {
      setError(null);
      setIsScanning(true);

      const codeReader = new BrowserMultiFormatReader();
      codeReaderRef.current = codeReader;

      // First try to use the back camera
      const devices = await navigator.mediaDevices.enumerateDevices();
      const videoDevices = devices.filter((device) => device.kind === 'videoinput');
      const backCamera = videoDevices.find((device) => (
        device.label.toLowerCase().includes('back')
        || device.label.toLowerCase().includes('rear')
      ));

      try {
        await codeReader.decodeFromVideoDevice(
          backCamera?.deviceId || 'environment',
          videoRef.current,
          (result, scanError) => {
            if (result) {
              const rawValue = result.getText();
              if (debug) {
                console.log('QR Scanner: Raw value scanned:', rawValue);
                console.log('QR Scanner: Expected format:', expectedFormat);
              }
              if (!expectedFormat || rawValue.includes(expectedFormat)) {
                if (debug) {
                  console.log('QR Scanner: Format matches, calling onScan');
                }
                onScan(rawValue);
                cleanup();
              } else if (debug) {
                console.log('QR Scanner: Format does not match, continuing to scan');
              }
            } else if (
              scanError
              && !(scanError instanceof NotFoundException)
              // eslint-disable-next-line sonarjs/no-duplicate-string
              && !(scanError instanceof Error && scanError.message.includes('source width is 0'))
            ) {
              const errorMsg = scanError.message || GENERIC_SCAN_ERROR_MESSAGE;
              if (debug) {
                console.log('QR Scanner: Error occurred:', errorMsg);
              }
              setError(errorMsg);
              onError(errorMsg);
            }
            // For NotFoundException, we don't set error - just keep scanning
          },
        ).catch((cameraErr) => {
          // Ignore source width 0 errors which can happen on some devices
          if (cameraErr instanceof Error && cameraErr.message.includes('source width is 0')) {
            return;
          }
          throw cameraErr;
        });
      } catch (err) {
        // If back camera fails, try any available camera
        if (codeReaderRef.current && videoRef.current) {
          await codeReaderRef.current.decodeFromVideoDevice(
            null, // Use default camera
            videoRef.current,
            (result, fallbackError) => {
              if (result) {
                const rawValue = result.getText();
                if (debug) {
                  console.log('QR Scanner (fallback): Raw value scanned:', rawValue);
                }
                if (!expectedFormat || rawValue.includes(expectedFormat)) {
                  if (debug) {
                    console.log('QR Scanner (fallback): Format matches, calling onScan');
                  }
                  onScan(rawValue);
                  cleanup();
                }
              } else if (
                fallbackError
                && !(fallbackError instanceof NotFoundException)
                && !(fallbackError instanceof Error && fallbackError.message.includes('source width is 0'))
              ) {
                const errorMsg = fallbackError.message || GENERIC_SCAN_ERROR_MESSAGE;
                if (debug) {
                  console.log('QR Scanner (fallback): Error occurred:', errorMsg);
                }
                setError(errorMsg);
                onError(errorMsg);
              }
              // For NotFoundException, we don't set error - just keep scanning
            },
          ).catch((cameraErr) => {
            if (cameraErr instanceof Error && cameraErr.message.includes('source width is 0')) {
              return;
            }
            throw cameraErr;
          });
        }
      }
    } catch (finalErr) {
      const errorMsg = finalErr instanceof Error
        ? finalErr.message
        : 'Unable to access camera. Please make sure you have granted camera permissions and your camera is working.';
      setError(errorMsg);
      onError(errorMsg);
      cleanup();
    }
  };

  useEffect(() => {
    // Start scanning automatically only if active
    if (isActive) {
      startScanning();
    } else {
      cleanup();
    }
    return cleanup;
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isActive]); // Run when isActive changes

  return (
    <Box>
      <Paper p="md" radius="md" withBorder>
        <Text fw={500} size="lg" mb="xs">{title}</Text>
        <Text size="sm" c="dimmed" mb="md">{description}</Text>

        <Box
          style={{
            position: 'relative',
            width: `${width}px`,
            height: `${height}px`,
            overflow: 'hidden',
            borderRadius: '4px',
            border: '1px solid #ccc',
            margin: '0 auto',
          }}
        >
          <video
            ref={videoRef}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
            }}
            playsInline
            muted
            autoPlay
            aria-label="QR code scanner"
          >
            <track kind="captions" />
          </video>
        </Box>

        {error && (
          <Box mt="md">
            <Text size="sm" c="red">{error}</Text>
            <Button size="xs" variant="light" color="red" mt="xs" onClick={startScanning}>
              Try Again
            </Button>
          </Box>
        )}

        <Button
          fullWidth
          mt="md"
          onClick={startScanning}
          leftSection={isScanning ? <IconCameraOff size="1rem" /> : <IconCamera size="1rem" />}
          color={isScanning ? 'red' : 'blue'}
        >
          {isScanning ? t('stopScanning') : t('startScanning')}
        </Button>
      </Paper>
    </Box>
  );
}

// Default props for optional values
QRScanner.defaultProps = defaultProps;
