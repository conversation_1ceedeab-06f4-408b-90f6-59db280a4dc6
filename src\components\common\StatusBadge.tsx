import { Badge } from '@mantine/core';
import useTranslation from 'next-translate/useTranslation';

const STATUS_COLORS: Record<string, string> = {
  PENDING: 'orange',
  AWAITING_PICKUP: 'blue',
  IN_TRANSIT: 'cyan',
  ARRIVED_AT_DESTINATION: 'grape',
  DELIVERED: 'green',
  CANCELLED: 'red',
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const getStatusLabel = (status: string, t: any) => {
  switch (status) {
    case 'PENDING':
      return t('statusPending');
    case 'AWAITING_PICKUP':
      return t('statusAwaitingPickup');
    case 'IN_TRANSIT':
      return t('statusInTransit');
    case 'ARRIVED_AT_DESTINATION':
      return t('statusArrivedAtDestination');
    case 'DELIVERED':
      return t('statusDelivered');
    case 'CANCELLED':
      return t('statusCancelled');
    default:
      return status.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, (l) => l.toUpperCase());
  }
};

export function StatusBadge({ status }: { status: string }) {
  const { t } = useTranslation('shipments');

  return (
    <Badge color={STATUS_COLORS[status] || 'gray'} variant="light">
      {getStatusLabel(status, t)}
    </Badge>
  );
}
