/* eslint-disable no-console */
/* eslint-disable complexity */
import { useState, useEffect } from 'react';
import { useForm } from '@mantine/form';
import type {
  ProfileFormValues,
  GeoLocation,
  UseProfileFormProps,
  UseProfileFormReturn,
} from '../types';
import { updateProfileApiRequestSchema } from '../../../requests/profile';

export const useProfileForm = ({
  profileData,
  session,
  formOptions = {},
}: UseProfileFormProps): UseProfileFormReturn => {
  const user = profileData?.data?.user;

  // State for geo location (ACCESS_OPERATOR)
  const [geoLocation, setGeoLocation] = useState<GeoLocation>({
    lat: user?.geoLatitude || null,
    lng: user?.geoLongitude || null,
  });

  // State for transit points (CAR_OPERATOR)
  const [transitPointIds, setTransitPointIds] = useState<string[]>(() => {
    const pickupId = user?.pickupAccessPointId;
    const dropoffId = user?.dropoffAccessPointId;
    return [pickupId, dropoffId].filter(Boolean) as string[];
  });

  // Form setup with schema-based validation (leveraging Zod)
  const form = useForm<ProfileFormValues>({
    initialValues: {
      name: user?.name || '',
      email: user?.email || '',
      phone: user?.phone || '',
      address: user?.address || '',
      businessName: user?.businessName || '',
      licenseNumber: user?.licenseNumber || '',
      vehicleInfo: user?.vehicleInfo || '',
      pickupAccessPointId: user?.pickupAccessPointId || '',
      dropoffAccessPointId: user?.dropoffAccessPointId || '',
    },
    // Integrate Zod validation from the profile request layer
    validate: (values) => {
      const parsed = updateProfileApiRequestSchema.safeParse(values);

      // Collect errors either from Zod parsing or from custom validators provided via formOptions
      const errors: Partial<Record<keyof ProfileFormValues, string | null>> = {};

      if (!parsed.success) {
        parsed.error.errors.forEach((issue) => {
          const field = issue.path[0] as keyof ProfileFormValues;
          errors[field] = issue.message;
        });
      }

      // Run any additional/override validations passed in via formOptions
      if (formOptions.validate) {
        Object.entries(formOptions.validate).forEach(([key, validateFn]) => {
          const field = key as keyof ProfileFormValues;
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const customError = validateFn(values[field] as any);
          if (customError) {
            errors[field] = customError;
          }
        });
      }

      return errors as Record<keyof ProfileFormValues, string | null>;
    },
    validateInputOnBlur: formOptions.validateInputOnBlur ?? true,
    validateInputOnChange: formOptions.validateInputOnChange ?? false,
  });

  // Update form values when profile data changes
  useEffect(() => {
    if (user) {
      form.setValues({
        name: user.name || '',
        email: user.email || '',
        phone: user.phone || '',
        address: user.address || '',
        businessName: user.businessName || '',
        licenseNumber: user.licenseNumber || '',
        vehicleInfo: user.vehicleInfo || '',
        pickupAccessPointId: user.pickupAccessPointId || '',
        dropoffAccessPointId: user.dropoffAccessPointId || '',
      });

      // Update geo location
      const newGeoLocation = {
        lat: user.geoLatitude || null,
        lng: user.geoLongitude || null,
      };
      if (process.env.NODE_ENV === 'development') {
        console.log('useProfileForm: Updating geo location from user data', newGeoLocation);
      }
      setGeoLocation(newGeoLocation);

      // Update transit points
      const pickupId = user.pickupAccessPointId;
      const dropoffId = user.dropoffAccessPointId;
      const points = [pickupId, dropoffId].filter(Boolean) as string[];
      setTransitPointIds(points);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user]);

  // Debug: Track geoLocation changes (development only)
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('useProfileForm: geoLocation state changed', geoLocation);
    }
  }, [geoLocation]);

  // Handlers
  const handleLocationChange = (lat: number, lng: number) => {
    if (process.env.NODE_ENV === 'development') {
      console.log('useProfileForm: Location changed', { lat, lng });
    }
    setGeoLocation({ lat, lng });
  };

  const handleTransitPointsChange = (pointIds: string[]) => {
    setTransitPointIds(pointIds);

    // Update form values for transit points with better logic
    const [pickupId = '', dropoffId = ''] = pointIds;

    form.setFieldValue('pickupAccessPointId', pickupId);
    form.setFieldValue('dropoffAccessPointId', dropoffId || pickupId); // Use pickup for dropoff if only one selected
  };

  return {
    form,
    geoLocation,
    transitPointIds,
    handleLocationChange,
    handleTransitPointsChange,
  };
};
