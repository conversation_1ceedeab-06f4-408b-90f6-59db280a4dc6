import { Text, TextInput, Stack } from '@mantine/core';
import { IconUser, IconMail } from '@tabler/icons-react';
import useTranslation from 'next-translate/useTranslation';
import { PhoneNumberInput } from '../common/PhoneNumberInput';
import type { BasicInformationSectionProps } from './types';

export default function BasicInformationSection({
  form,
  isEditing,
  user,
}: BasicInformationSectionProps) {
  const { t } = useTranslation('profile');

  return (
    <div>
      <Text fw={600} mb="sm">{t('basicInformation')}</Text>
      <Stack gap="sm">
        <TextInput
          label={t('fullName')}
          placeholder={t('enterFullName')}
          leftSection={<IconUser size="1rem" />}
          disabled={!isEditing}
          {...form.getInputProps('name')}
        />

        <TextInput
          label={t('emailAddress')}
          value={user.email}
          disabled
          leftSection={<IconMail size="1rem" />}
        />

        <PhoneNumberInput
          form={form}
          field="phone"
          label={t('phoneNumber')}
          placeholder={t('enterPhoneNumber')}
          disabled={!isEditing}
          required
        />
      </Stack>
    </div>
  );
}
