import { Button, Group } from '@mantine/core';
import useTranslation from 'next-translate/useTranslation';
import type { ProfileActionButtonsProps } from './types';

export default function ProfileActionButtons({
  isEditing,
  isLoading,
  onEdit,
  onCancel,
  onReset,
}: ProfileActionButtonsProps) {
  const { t } = useTranslation('profile');
  const { t: tCommon } = useTranslation('common');

  return (
    <Group justify="flex-end" mt="lg">
      {isEditing ? (
        <>
          <Button
            variant="outline"
            onClick={() => {
              onCancel();
              onReset();
            }}
          >
            {t('cancelEdit')}
          </Button>
          <Button
            type="submit"
            loading={isLoading}
          >
            {tCommon('save')}
          </Button>
        </>
      ) : (
        <Button onClick={onEdit}>
          {t('editProfile')}
        </Button>
      )}
    </Group>
  );
}
