/* eslint-disable max-lines */
/* eslint-disable sonarjs/no-duplicate-string */
/* eslint-disable sonarjs/cognitive-complexity */
/* eslint-disable complexity */
/* eslint-disable react/require-default-props */
import {
  Paper,
  Stack,
  Group,
  Text,
  Badge,
  Button,
  Grid,
  Card,
  Title,
  Alert,
  Box,
  Divider,
  ThemeIcon,
  Transition,
} from '@mantine/core';
import {
  IconPackage,
  IconMapPin,
  IconAlertTriangle,
  IconQrcode,
  IconClock,
  IconWeight,
  IconRuler,
  IconTruck,
  IconCheck,
  IconX,
  IconLoader,
} from '@tabler/icons-react';
import { useRouter } from 'next/router';
import useTranslation from 'next-translate/useTranslation';
import { Shipment } from '../../../requests/shipment';
import { useIsClient } from '../../../hooks/useIsClient';
import { getExpiryInfo, formatExpiryDate, shouldHighlightExpiry } from '../../../utils/shipmentExpiry';
import { useSession } from 'next-auth/react';
import { useState } from 'react';
import AssignShipmentModal from '../assign/AssignShipmentModal';
import AOPickupModal from '../modals/AOPickupModal';
import { EnhancedShipmentAccessPointsInfo } from '../info';

interface AOShipmentDetailViewProps {
  shipment: Shipment;
  onBack: () => void;
  onScanArrival?: (shipment: Shipment) => void;
  onPickup?: (shipment: Shipment) => void;
}

// Enhanced helper functions
const getStatusColor = (status: string) => {
  switch (status) {
    case 'PENDING':
      return 'orange';
    case 'AWAITING_PICKUP':
      return 'blue';
    case 'IN_TRANSIT':
      return 'cyan';
    case 'ARRIVED_AT_DESTINATION':
      return 'grape';
    case 'DELIVERED':
      return 'green';
    case 'CANCELLED':
      return 'red';
    default:
      return 'gray';
  }
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'PENDING':
      return IconClock;
    case 'AWAITING_PICKUP':
      return IconPackage;
    case 'IN_TRANSIT':
      return IconTruck;
    case 'ARRIVED_AT_DESTINATION':
      return IconMapPin;
    case 'DELIVERED':
      return IconCheck;
    case 'CANCELLED':
      return IconX;
    default:
      return IconLoader;
  }
};

const getStatusLabel = (status: string, t: (key: string) => string) => {
  switch (status) {
    case 'PENDING':
      return t('statusPending');
    case 'AWAITING_PICKUP':
      return t('statusAwaitingPickup');
    case 'IN_TRANSIT':
      return t('statusInTransit');
    case 'ARRIVED_AT_DESTINATION':
      return t('statusArrivedAtDestination');
    case 'DELIVERED':
      return t('statusDelivered');
    case 'CANCELLED':
      return t('statusCancelled');
    default:
      return status;
  }
};

const getSizeLabel = (size: string, t: (key: string) => string) => {
  switch (size) {
    case 'SMALL':
      return t('sizeSmall');
    case 'MEDIUM':
      return t('sizeMedium');
    case 'LARGE':
      return t('sizeLarge');
    case 'EXTRA_LARGE':
      return t('sizeExtraLarge');
    default:
      return size;
  }
};

export default function AOShipmentDetailView({
  shipment,
  onBack,
  onScanArrival,
  onPickup,
}: AOShipmentDetailViewProps) {
  const isClient = useIsClient();
  const router = useRouter();
  const { t } = useTranslation('shipments');
  const { data: session } = useSession();
  const aoId = session?.user?.id;
  const [assignModalOpen, setAssignModalOpen] = useState(false);
  const [pickupModalOpen, setPickupModalOpen] = useState(false);

  const isRTL = router.locale === 'ar';
  const StatusIcon = getStatusIcon(shipment.status);

  // Safe date formatting function
  const formatDate = (dateString: string) => {
    if (!isClient) return 'Loading...';
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return 'Invalid date';
    }
  };

  const formatDateTime = (dateString: string) => {
    if (!isClient) return 'Loading...';
    try {
      return new Date(dateString).toLocaleString();
    } catch {
      return 'Invalid date';
    }
  };

  return (
    <Stack gap="xl" p="md">
      {/* Enhanced Header */}
      <Paper
        p="xl"
        withBorder
        radius="lg"
        style={{
          border: 'none',
          boxShadow: '0 10px 30px rgba(0,0,0,0.1)',
        }}
      >
        <Group justify="space-between" align="center" style={{ direction: isRTL ? 'rtl' : 'ltr' }}>
          <Group gap="lg" style={{ order: isRTL ? 2 : 1 }}>
            <Box style={{ textAlign: isRTL ? 'right' : 'left' }}>
              <Title
                order={1}
                mb={8}
                style={{
                  direction: isRTL ? 'rtl' : 'ltr',
                  textAlign: isRTL ? 'right' : 'left',
                  fontWeight: 700,
                  letterSpacing: '0.5px',
                }}
              >
                {t('shipmentNumber')}
                {' '}
                #
                {shipment.id.slice(-8).toUpperCase()}
              </Title>
              <Text
                size="lg"
                style={{
                  direction: isRTL ? 'rtl' : 'ltr',
                  textAlign: isRTL ? 'right' : 'left',
                  fontWeight: 500,
                }}
              >
                {t('assignedOn')}
                {' '}
                {formatDate(shipment.updatedAt)}
              </Text>
            </Box>
          </Group>
          <Box style={{ order: isRTL ? 1 : 2 }}>
            <Badge
              color={getStatusColor(shipment.status)}
              variant="white"
              size="xl"
              radius="md"
              leftSection={<StatusIcon size="1.2rem" />}
              style={{
                boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                fontWeight: 600,
              }}
            >
              {getStatusLabel(shipment.status, t)}
            </Badge>
          </Box>
        </Group>
      </Paper>

      {/* Enhanced Expiry Alert */}
      {shipment.expiresAt && shouldHighlightExpiry(shipment.expiresAt) && (
        <Transition mounted transition="slide-down" duration={300}>
          {(styles) => (
            <Alert
              icon={<IconAlertTriangle size="1.2rem" />}
              color={getExpiryInfo(shipment.expiresAt).color}
              variant="light"
              title={getExpiryInfo(shipment.expiresAt).isExpired ? t('shipmentExpired') : t('shipmentExpiresSoon')}
              style={{
                ...styles,
                direction: isRTL ? 'rtl' : 'ltr',
                textAlign: isRTL ? 'right' : 'left',
                borderRadius: '12px',
                border: `2px solid var(--mantine-color-${getExpiryInfo(shipment.expiresAt).color}-3)`,
                boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
              }}
            >
              <Text size="sm" fw={500}>
                {getExpiryInfo(shipment.expiresAt).isExpired
                  ? t('expiredMessage', { date: formatExpiryDate(shipment.expiresAt) })
                  : t('expiresInMessage', { timeRemaining: getExpiryInfo(shipment.expiresAt).timeRemaining })}
              </Text>
            </Alert>
          )}
        </Transition>
      )}

      {/* Enhanced Information Cards */}
      <Grid gutter="xl">
        <Grid.Col span={{ base: 12, md: 6 }}>
          {/* Enhanced Package Information */}
          <Card
            withBorder
            p="xl"
            radius="xl"
            style={{
              direction: isRTL ? 'rtl' : 'ltr',
              height: '100%',
              border: '1px solid #e1e8f5',
              boxShadow: '0 8px 25px rgba(0,0,0,0.08)',
              transition: 'all 0.3s ease',
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-4px)';
              e.currentTarget.style.boxShadow = '0 12px 35px rgba(0,0,0,0.12)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 8px 25px rgba(0,0,0,0.08)';
            }}
          >
            <Stack gap="lg">
              <Group gap="sm" style={{ justifyContent: isRTL ? 'flex-end' : 'flex-start' }}>
                <ThemeIcon size="xl" variant="light" color="blue" radius="xl">
                  <IconPackage size="1.4rem" />
                </ThemeIcon>
                <Text fw={700} size="xl" c="blue.8">{t('packageDetails')}</Text>
              </Group>

              <Divider />

              <Grid gutter="lg">
                <Grid.Col span={6}>
                  <Box
                    p="md"
                    style={{
                      borderRadius: '12px',
                      textAlign: isRTL ? 'right' : 'left',
                      border: '1px solid rgba(0,0,0,0.05)',
                    }}
                  >
                    <Group gap="xs" mb="xs" style={{ justifyContent: isRTL ? 'flex-end' : 'flex-start' }}>
                      <IconWeight size="1rem" color="var(--mantine-color-gray-6)" />
                      <Text size="sm" c="dimmed" fw={500}>{t('weight')}</Text>
                    </Group>
                    <Text fw={700} size="lg" c="dark">
                      {shipment.weight}
                      {t('kg')}
                    </Text>
                  </Box>
                </Grid.Col>
                <Grid.Col span={6}>
                  <Box
                    p="md"
                    style={{
                      borderRadius: '12px',
                      textAlign: isRTL ? 'right' : 'left',
                      border: '1px solid rgba(0,0,0,0.05)',
                    }}
                  >
                    <Group gap="xs" mb="xs" style={{ justifyContent: isRTL ? 'flex-end' : 'flex-start' }}>
                      <IconRuler size="1rem" color="var(--mantine-color-gray-6)" />
                      <Text size="sm" c="dimmed" fw={500}>{t('size')}</Text>
                    </Group>
                    <Text fw={700} size="lg" c="dark">{getSizeLabel(shipment.size, t)}</Text>
                  </Box>
                </Grid.Col>
                {shipment.expiresAt && (
                  <Grid.Col span={12}>
                    <Box
                      p="md"
                      style={{
                        borderRadius: '12px',
                        textAlign: isRTL ? 'right' : 'left',
                        border: `1px solid ${getExpiryInfo(shipment.expiresAt).isExpired ? 'rgba(255,0,0,0.2)' : 'rgba(255,193,7,0.2)'}`,
                      }}
                    >
                      <Group gap="xs" mb="xs" style={{ justifyContent: isRTL ? 'flex-end' : 'flex-start' }}>
                        <IconClock size="1rem" color={`var(--mantine-color-${getExpiryInfo(shipment.expiresAt).color}-6)`} />
                        <Text size="sm" c="dimmed" fw={500}>{t('expires')}</Text>
                      </Group>
                      <Group gap="sm" style={{ justifyContent: isRTL ? 'flex-end' : 'flex-start' }}>
                        <Text fw={700} size="lg" c={getExpiryInfo(shipment.expiresAt).color}>
                          {getExpiryInfo(shipment.expiresAt).timeRemaining}
                        </Text>
                        {getExpiryInfo(shipment.expiresAt).isExpired && (
                          <Badge size="sm" color="red" variant="filled" radius="md">
                            {t('expired')}
                          </Badge>
                        )}
                      </Group>
                    </Box>
                  </Grid.Col>
                )}
              </Grid>

              <Box
                p="md"
                style={{
                  borderRadius: '12px',
                  textAlign: isRTL ? 'right' : 'left',
                  border: '1px solid rgba(0,0,0,0.05)',
                }}
              >
                <Text size="sm" c="dimmed" fw={500} mb="xs">{t('description')}</Text>
                <Text size="md" fw={500}>{shipment.description}</Text>
              </Box>
            </Stack>
          </Card>
        </Grid.Col>

        {/* Enhanced Route Information */}
        <Grid.Col span={{ base: 12, md: 6 }}>
          <Card
            withBorder
            p="xl"
            radius="xl"
            style={{
              direction: isRTL ? 'rtl' : 'ltr',
              height: '100%',
              border: '1px solid #f5e6d3',
              boxShadow: '0 8px 25px rgba(0,0,0,0.08)',
              transition: 'all 0.3s ease',
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-4px)';
              e.currentTarget.style.boxShadow = '0 12px 35px rgba(0,0,0,0.12)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 8px 25px rgba(0,0,0,0.08)';
            }}
          >
            <Stack gap="lg">
              <Group gap="sm" style={{ justifyContent: isRTL ? 'flex-end' : 'flex-start' }}>
                <ThemeIcon size="xl" variant="light" color="orange" radius="xl">
                  <IconMapPin size="1.4rem" />
                </ThemeIcon>
                <Text fw={700} size="xl" c="orange.8">{t('routeInformation')}</Text>
              </Group>

              <Divider />

              <EnhancedShipmentAccessPointsInfo
                shipment={shipment}
                variant="compact"
                showRouteInfo
                showInstructions={false}
              />
            </Stack>
          </Card>
        </Grid.Col>

        {/* Enhanced Timeline */}
        <Grid.Col span={12}>
          <Card
            withBorder
            p="xl"
            radius="xl"
            style={{
              direction: isRTL ? 'rtl' : 'ltr',
              border: '1px solid #d4f5dd',
              boxShadow: '0 8px 25px rgba(0,0,0,0.08)',
              transition: 'all 0.3s ease',
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-4px)';
              e.currentTarget.style.boxShadow = '0 12px 35px rgba(0,0,0,0.12)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 8px 25px rgba(0,0,0,0.08)';
            }}
          >
            <Stack gap="lg">
              <Group gap="sm" style={{ justifyContent: isRTL ? 'flex-end' : 'flex-start' }}>
                <ThemeIcon size="xl" variant="light" color="teal" radius="xl">
                  <IconClock size="1.4rem" />
                </ThemeIcon>
                <Text fw={700} size="xl" c="teal.8">{t('statusTimeline')}</Text>
              </Group>

              <Divider />

              <Grid gutter="lg">
                <Grid.Col span={{ base: 12, md: 4 }}>
                  <Box
                    p="lg"
                    style={{
                      borderRadius: '16px',
                      textAlign: isRTL ? 'right' : 'left',
                      border: '1px solid rgba(0,0,0,0.05)',
                    }}
                  >
                    <Text size="sm" c="dimmed" fw={500} mb="sm">{t('created')}</Text>
                    <Text size="md" fw={600}>{formatDateTime(shipment.createdAt)}</Text>
                  </Box>
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 4 }}>
                  <Box
                    p="lg"
                    style={{
                      borderRadius: '16px',
                      textAlign: isRTL ? 'right' : 'left',
                      border: '1px solid rgba(0,0,0,0.05)',
                    }}
                  >
                    <Text size="sm" c="dimmed" fw={500} mb="sm">{t('lastUpdated')}</Text>
                    <Text size="md" fw={600}>{formatDateTime(shipment.updatedAt)}</Text>
                  </Box>
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 4 }}>
                  <Box
                    p="lg"
                    style={{
                      borderRadius: '16px',
                      textAlign: isRTL ? 'right' : 'left',
                      border: '1px solid rgba(0,0,0,0.05)',
                    }}
                  >
                    <Text size="sm" c="dimmed" fw={500} mb="sm">{t('currentStatus')}</Text>
                    <Badge
                      color={getStatusColor(shipment.status)}
                      variant="light"
                      size="lg"
                      radius="md"
                      leftSection={<StatusIcon size="1rem" />}
                    >
                      {getStatusLabel(shipment.status, t)}
                    </Badge>
                  </Box>
                </Grid.Col>
              </Grid>

              {shipment.trackingCode && (
                <Box
                  p="lg"
                  style={{
                    borderRadius: '16px',
                    textAlign: isRTL ? 'right' : 'left',
                    border: '1px solid rgba(0,0,0,0.05)',
                  }}
                >
                  <Text size="sm" c="dimmed" fw={500} mb="sm">{t('trackingCode')}</Text>
                  <Text
                    fw={700}
                    c="blue"
                    size="lg"
                    style={{
                      fontFamily: 'JetBrains Mono, Consolas, monospace',
                      backgroundColor: 'rgba(0,0,255,0.1)',
                      padding: '8px 12px',
                      borderRadius: '8px',
                      border: '1px solid rgba(0,0,255,0.2)',
                    }}
                  >
                    {shipment.trackingCode}
                  </Text>
                </Box>
              )}
            </Stack>
          </Card>
        </Grid.Col>
      </Grid>

      {/* Enhanced Action Buttons */}
      <Paper p="xl" withBorder radius="xl" style={{ boxShadow: '0 8px 25px rgba(0,0,0,0.08)' }}>
        <Group justify="center" gap="lg" style={{ direction: isRTL ? 'rtl' : 'ltr' }}>
          <Button
            variant="light"
            onClick={onBack}
            size="lg"
            radius="xl"
            style={{
              transition: 'all 0.2s ease',
              fontWeight: 600,
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-2px)';
              e.currentTarget.style.boxShadow = '0 6px 20px rgba(0,0,0,0.15)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = 'none';
            }}
          >
            {t('backToShipmentsList')}
          </Button>

          {shipment.status === 'IN_TRANSIT' && shipment.destAoId === aoId && onScanArrival && (
            <Button
              color="green"
              size="lg"
              radius="xl"
              leftSection={<IconQrcode size="1.2rem" />}
              onClick={() => onScanArrival(shipment)}
              style={{
                background: 'linear-gradient(135deg, #51cf66 0%, #40c057 100%)',
                fontWeight: 600,
                transition: 'all 0.2s ease',
                boxShadow: '0 4px 15px rgba(64, 192, 87, 0.4)',
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px)';
                e.currentTarget.style.boxShadow = '0 8px 25px rgba(64, 192, 87, 0.6)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 4px 15px rgba(64, 192, 87, 0.4)';
              }}
            >
              {t('scanArrival')}
            </Button>
          )}

          {shipment.status === 'ARRIVED_AT_DESTINATION' && shipment.destAoId === aoId && onPickup && (
            <Button
              color="blue"
              size="lg"
              radius="xl"
              leftSection={<IconPackage size="1.2rem" />}
              onClick={() => onPickup(shipment)}
              style={{
                fontWeight: 600,
                transition: 'all 0.2s ease',
                boxShadow: '0 4px 15px rgba(34, 139, 230, 0.4)',
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px)';
                e.currentTarget.style.boxShadow = '0 8px 25px rgba(34, 139, 230, 0.6)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 4px 15px rgba(34, 139, 230, 0.4)';
              }}
            >
              {t('processPickup')}
            </Button>
          )}
        </Group>
      </Paper>

      {/* Modals */}
      <AssignShipmentModal
        opened={assignModalOpen}
        onClose={() => setAssignModalOpen(false)}
        shipment={shipment}
        onSuccess={() => setAssignModalOpen(false)}
      />
      <AOPickupModal
        opened={pickupModalOpen}
        onClose={() => setPickupModalOpen(false)}
        shipment={shipment}
        onSuccess={() => setPickupModalOpen(false)}
      />
    </Stack>
  );
}
