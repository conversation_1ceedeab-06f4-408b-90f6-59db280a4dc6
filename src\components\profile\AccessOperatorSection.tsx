import {
  Text, TextInput, Stack, Textarea,
} from '@mantine/core';
import { IconBuilding } from '@tabler/icons-react';
import useTranslation from 'next-translate/useTranslation';
import dynamic from 'next/dynamic';
import type { AccessOperatorSectionProps } from './types';

// Dynamically import the map component to avoid SSR issues
const AccessPointMap = dynamic(() => import('../maps/AccessPointMap'), {
  ssr: false,
  loading: () => <div>Loading map...</div>,
});

export default function AccessOperatorSection({
  form,
  isEditing,
  geoLocation,
  onLocationChange,
}: AccessOperatorSectionProps) {
  const { t } = useTranslation('profile');

  return (
    <div>
      <Text fw={600} mb="sm">{t('businessInformation')}</Text>
      <Stack gap="sm">
        <TextInput
          label={t('businessName')}
          placeholder={t('enterBusinessName')}
          leftSection={<IconBuilding size="1rem" />}
          disabled={!isEditing}
          {...form.getInputProps('businessName')}
        />

        <Textarea
          label={t('address')}
          placeholder={t('enterAddress')}
          disabled={!isEditing}
          {...form.getInputProps('address')}
        />

        {/* Access Point Location Map */}
        <div>
          <Text fw={500} mb="sm">{t('accessPointLocation')}</Text>
          <AccessPointMap
            key={`${geoLocation.lat}-${geoLocation.lng}`}
            initialLat={geoLocation.lat ?? undefined}
            initialLng={geoLocation.lng ?? undefined}
            businessName={form.values.businessName}
            address={form.values.address}
            onLocationChange={onLocationChange}
            isEditable={isEditing}
          />
        </div>
      </Stack>
    </div>
  );
}
