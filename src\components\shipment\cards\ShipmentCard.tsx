/* eslint-disable no-nested-ternary */
/* eslint-disable max-lines */
/* eslint-disable complexity */
/* eslint-disable react/require-default-props */
import type { ReactNode } from 'react';
import {
  Card,
  Text,
  Badge,
  Group,
  Stack,
  ActionIcon,
  Tooltip,
  Box,
  ThemeIcon,
  useMantineTheme,
  useMantineColorScheme,
} from '@mantine/core';
import {
  IconPackage,
  IconMapPin,
  IconClock,
  IconWeight,
  IconUser,
  IconCancel,
  IconEye,
  IconTruck,
  IconCheck,
  IconAlertTriangle,
} from '@tabler/icons-react';
import { useIsClient } from '../../../hooks/useIsClient';
import { Shipment } from '../../../requests/shipment';
import { getExpiryInfo, shouldHighlightExpiry } from '../../../utils/shipmentExpiry';

interface ShipmentCardProps {
  shipment: Shipment;
  onView?: (shipment: Shipment) => void;
  onCancel?: (shipment: Shipment) => void;
  extraActions?: ReactNode;
  hideDefaultActions?: boolean;
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'PENDING':
      return 'orange';
    case 'AWAITING_PICKUP':
      return 'blue';
    case 'IN_TRANSIT':
      return 'cyan';
    case 'ARRIVED_AT_DESTINATION':
      return 'grape';
    case 'DELIVERED':
      return 'green';
    case 'CANCELLED':
      return 'red';
    default:
      return 'gray';
  }
};

const getStatusLabel = (status: string) => {
  switch (status) {
    case 'PENDING':
      return 'Pending';
    case 'AWAITING_PICKUP':
      return 'Awaiting Pickup';
    case 'IN_TRANSIT':
      return 'In Transit';
    case 'ARRIVED_AT_DESTINATION':
      return 'Arrived at Destination';
    case 'DELIVERED':
      return 'Delivered';
    case 'CANCELLED':
      return 'Cancelled';
    default:
      return status;
  }
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'PENDING':
      return IconClock;
    case 'AWAITING_PICKUP':
      return IconPackage;
    case 'IN_TRANSIT':
      return IconTruck;
    case 'ARRIVED_AT_DESTINATION':
      return IconMapPin;
    case 'DELIVERED':
      return IconCheck;
    case 'CANCELLED':
      return IconCancel;
    default:
      return IconPackage;
  }
};

const getSizeLabel = (size: string) => {
  switch (size) {
    case 'SMALL':
      return 'S';
    case 'MEDIUM':
      return 'M';
    case 'LARGE':
      return 'L';
    case 'EXTRA_LARGE':
      return 'XL';
    default:
      return size;
  }
};

const getSizeColor = (size: string) => {
  switch (size) {
    case 'SMALL':
      return 'green';
    case 'MEDIUM':
      return 'blue';
    case 'LARGE':
      return 'orange';
    case 'EXTRA_LARGE':
      return 'red';
    default:
      return 'gray';
  }
};

export default function ShipmentCard({
  shipment,
  onView,
  onCancel,
  extraActions,
  hideDefaultActions = false,
}: ShipmentCardProps) {
  const isClient = useIsClient();
  const theme = useMantineTheme();
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === 'dark';
  const canCancel = shipment.status === 'PENDING';
  const isDelivered = shipment.status === 'DELIVERED';
  const isCancelled = shipment.status === 'CANCELLED';
  const StatusIcon = getStatusIcon(shipment.status);

  const formatDate = (dateString: string) => {
    if (!isClient) return 'Loading...';
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
      });
    } catch {
      return 'Invalid date';
    }
  };

  const getCardBorderColor = () => {
    if (isCancelled) return theme.colors.red[isDark ? 4 : 3];
    if (isDelivered) return theme.colors.green[isDark ? 4 : 3];
    if (shipment.expiresAt && getExpiryInfo(shipment.expiresAt).isExpired) return theme.colors.red[isDark ? 5 : 4];
    if (shipment.expiresAt && shouldHighlightExpiry(shipment.expiresAt)) return theme.colors.orange[isDark ? 5 : 4];
    return undefined;
  };

  return (
    <Card
      shadow="sm"
      padding="sm"
      radius="md"
      withBorder
      style={{
        borderColor: getCardBorderColor(),
        borderWidth: getCardBorderColor() ? 2 : 1,
        transition: 'all 0.2s ease',
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.transform = 'translateY(-1px)';
        e.currentTarget.style.boxShadow = theme.shadows.md;
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.transform = 'translateY(0)';
        e.currentTarget.style.boxShadow = theme.shadows.sm;
      }}
    >
      <Stack gap="xs">
        {/* Compact Header */}
        <Group justify="space-between" align="center">
          <Group gap="xs">
            <ThemeIcon
              size="md"
              variant="light"
              color={getStatusColor(shipment.status)}
              radius="sm"
            >
              <StatusIcon size="0.9rem" />
            </ThemeIcon>
            <Box>
              <Text fw={600} size="xs" c={isDark ? 'gray.1' : 'dark.7'}>
                #
                {shipment.id.slice(-6).toUpperCase()}
              </Text>
            </Box>
          </Group>

          <Group gap={4}>
            <Badge
              color={getStatusColor(shipment.status)}
              variant="light"
              size="xs"
              radius="sm"
            >
              {getStatusLabel(shipment.status)}
            </Badge>

            {!hideDefaultActions && (
              <Group gap={2}>
                {onView && (
                  <Tooltip label="View" position="top">
                    <ActionIcon
                      variant="light"
                      color="blue"
                      size="sm"
                      radius="sm"
                      onClick={() => onView(shipment)}
                    >
                      <IconEye size="0.8rem" />
                    </ActionIcon>
                  </Tooltip>
                )}
                {canCancel && onCancel && (
                  <Tooltip label="Cancel" position="top">
                    <ActionIcon
                      variant="light"
                      color="red"
                      size="sm"
                      radius="sm"
                      onClick={() => onCancel(shipment)}
                    >
                      <IconCancel size="0.8rem" />
                    </ActionIcon>
                  </Tooltip>
                )}
              </Group>
            )}
          </Group>
        </Group>

        {/* Description - Single line with ellipsis */}
        <Text
          size="xs"
          fw={500}
          c={isDark ? 'gray.2' : 'dark.6'}
          lineClamp={1}
          title={shipment.description}
        >
          {shipment.description}
        </Text>

        {/* Compact Info Row */}
        <Group gap="xs" justify="space-between">
          <Group gap="xs">
            <Group gap={4}>
              <IconWeight size="0.7rem" color={theme.colors.gray[5]} />
              <Text size="xs" c="dimmed">
                {shipment.weight}
                kg
              </Text>
            </Group>

            <Badge
              size="xs"
              variant="light"
              color={getSizeColor(shipment.size)}
              radius="sm"
            >
              {getSizeLabel(shipment.size)}
            </Badge>
          </Group>

          <Group gap="xs">
            <IconUser size="0.7rem" color={theme.colors.gray[5]} />
            <Text size="xs" c="dimmed" lineClamp={1}>
              {shipment.receiverName}
            </Text>
          </Group>
        </Group>

        {/* Dates - Compact */}
        <Group gap="xs" justify="space-between">
          <Group gap={4}>
            <IconClock size="0.7rem" color={theme.colors.gray[5]} />
            <Text size="xs" c="dimmed">
              {formatDate(shipment.createdAt)}
            </Text>
          </Group>

          {shipment.estimatedDelivery && (
            <Group gap={4}>
              <IconTruck size="0.7rem" color={theme.colors.blue[5]} />
              <Text size="xs" c="blue">
                {formatDate(shipment.estimatedDelivery)}
              </Text>
            </Group>
          )}
        </Group>

        {/* Expiry Warning - Compact */}
        {shipment.expiresAt && shouldHighlightExpiry(shipment.expiresAt) && (
          <Group gap="xs" align="center">
            <IconAlertTriangle
              size="0.8rem"
              color={getExpiryInfo(shipment.expiresAt).isExpired ? theme.colors.red[6] : theme.colors.orange[6]}
            />
            <Text
              size="xs"
              fw={600}
              c={getExpiryInfo(shipment.expiresAt).isExpired ? 'red' : 'orange'}
            >
              {getExpiryInfo(shipment.expiresAt).timeRemaining}
            </Text>
            {getExpiryInfo(shipment.expiresAt).isExpired && (
              <Badge size="xs" color="red" variant="filled">EXPIRED</Badge>
            )}
          </Group>
        )}

        {/* Tracking Codes - Compact */}
        {(shipment.pickupCode || shipment.trackingCode) && !isDelivered && !isCancelled && (
          <Group gap="xs">
            {shipment.pickupCode && (
              <Badge variant="filled" color="teal" size="xs" radius="sm">
                P:
                {' '}
                {shipment.pickupCode}
              </Badge>
            )}
            {shipment.trackingCode && (
              <Badge variant="filled" color="green" size="xs" radius="sm">
                T:
                {' '}
                {shipment.trackingCode}
              </Badge>
            )}
          </Group>
        )}

        {/* Cancellation - Compact */}
        {isCancelled && shipment.cancellationReason && (
          <Group gap="xs" align="center">
            <IconCancel size="0.8rem" color={theme.colors.red[6]} />
            <Text size="xs" c="red" fw={500}>
              Cancelled:
              {' '}
              {shipment.cancellationReason.replace('_', ' ').toLowerCase()}
            </Text>
          </Group>
        )}

        {/* Extra actions */}
        {extraActions && (
          <Box pt="xs" style={{ borderTop: `1px solid ${isDark ? theme.colors.dark[4] : theme.colors.gray[2]}` }}>
            {extraActions}
          </Box>
        )}
      </Stack>
    </Card>
  );
}
