/* eslint-disable react/require-default-props */
import { Stack } from '@mantine/core';
import { UseFormReturnType } from '@mantine/form';
import { PackageDetailsForm } from './PackageDetailsForm';
import { ReceiverDetailsForm } from './ReceiverDetailsForm';
import { FormActions } from './FormActions';

interface FormValues {
  originAoId: string;
  destAoId: string;
  weight: number;
  size: string;
  description: string;
  receiverName: string;
  receiverPhone: string;
}

interface ShipmentFormProps {
  form: UseFormReturnType<FormValues, (values: FormValues) => FormValues>;
  onSubmit: (event: React.FormEvent) => void; // ← Changed to accept form event
  onCancel?: () => void;
  isLoading: boolean;
}

export function ShipmentForm({
  form, onSubmit, onCancel, isLoading,
}: ShipmentFormProps) {
  return (
    <form
      onSubmit={onSubmit}
      noValidate
    >
      <Stack gap="md">
        <PackageDetailsForm form={form} />
        <ReceiverDetailsForm form={form} />
        <FormActions onCancel={onCancel} isLoading={isLoading} />
      </Stack>
    </form>
  );
}
