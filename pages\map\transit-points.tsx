import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import {
  Container,
  Paper,
  Title,
  Text,
  Loader,
  Center,
  Alert,
  Stack,
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { IconRoute, IconAlertCircle, IconCheck } from '@tabler/icons-react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import dynamic from 'next/dynamic';
import { getProfileQuery } from '../../src/requests/profile/calls';
import { CLIENT_API } from '../../src/lib/axios';
import { API_ENDPOINT } from '../../src/data';

// Dynamically import the map component to avoid SSR issues
const TransitPointsMap = dynamic(
  () => import('../../src/components/maps/TransitPointsMap'),
  {
    ssr: false,
    loading: () => <Center style={{ height: '500px' }}><Loader /></Center>,
  },
);

// Access points query
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const getAccessPointsQuery = (props?: any) => ({
  queryKey: ['access-points', props?.filters],
  queryFn: () => CLIENT_API.get(API_ENDPOINT.accessPoints.list)
    .then((res) => {
      // Handle different possible response structures
      const data = res?.data;
      if (data?.data?.access_points) {
        return data.data.access_points;
      }
      if (data?.access_points) {
        return data.access_points;
      }
      if (Array.isArray(data?.data)) {
        return data.data;
      }
      if (Array.isArray(data)) {
        return data;
      }
      return [];
    })
    .catch((e) => {
      throw e.response?.data;
    }),
  refetchOnWindowFocus: false,
  enabled: props?.enabled,
});

export default function TransitPointsMapPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const queryClient = useQueryClient();
  const [selectedPickupId, setSelectedPickupId] = useState<string>('');
  const [selectedDropoffId, setSelectedDropoffId] = useState<string>('');

  // Redirect if not authenticated or not a car operator
  useEffect(() => {
    if (status === 'loading') return;

    if (!session) {
      router.push('/auth/login');
      return;
    }

    if (session.user?.user_type !== 'CAR_OPERATOR') {
      router.push('/dashboard');
    }
  }, [session, status, router]);

  // Get profile data
  const { data: profileData, isLoading, error } = useQuery(
    getProfileQuery({ enabled: !!session && session.user?.user_type === 'CAR_OPERATOR' }),
  );

  // Get access points data
  const accessPointsQueryProps = {
    filters: { approved: true, status: 'ACTIVE' },
    enabled: !!session && session.user?.user_type === 'CAR_OPERATOR',
  };

  const { data: accessPointsData, isLoading: accessPointsLoading, error: accessPointsError } = useQuery(
    getAccessPointsQuery(accessPointsQueryProps),
  );

  // Extract access points from the response
  const accessPoints = accessPointsData?.data?.accessOperators || [];

  // Set initial selected points from profile data
  useEffect(() => {
    if (profileData?.data?.user) {
      const { user } = profileData.data;
      if (user.pickup_access_point_id) {
        setSelectedPickupId(user.pickup_access_point_id);
      }
      if (user.dropoff_access_point_id) {
        setSelectedDropoffId(user.dropoff_access_point_id);
      }
    }
  }, [profileData]);

  // Update transit points mutation
  const updateTransitPointsMutation = useMutation({
    mutationFn: async (data: { pickup_access_point_id: string; dropoff_access_point_id: string }) => {
      const response = await CLIENT_API.put(API_ENDPOINT.users.updateProfile, data);
      return response.data;
    },
    onSuccess: () => {
      notifications.show({
        title: 'Success',
        message: 'Transit route updated successfully',
        color: 'green',
        icon: <IconCheck size="1rem" />,
      });
      queryClient.invalidateQueries({ queryKey: ['profile'] });
    },
    onError: (updateError: unknown) => {
      const errorMessage = (updateError as { response?: { data?: { message?: string } } })?.response?.data?.message || 'Failed to update transit route';
      notifications.show({
        title: 'Error',
        message: errorMessage,
        color: 'red',
        icon: <IconAlertCircle size="1rem" />,
      });
    },
  });

  const handlePickupSelect = (accessPoint: { id: string }) => {
    if (accessPoint.id === selectedDropoffId) {
      notifications.show({
        title: 'Invalid Selection',
        message: 'Pickup and dropoff points cannot be the same',
        color: 'orange',
        icon: <IconAlertCircle size="1rem" />,
      });
      return;
    }
    setSelectedPickupId(accessPoint.id);
  };

  const handleDropoffSelect = (accessPoint: { id: string }) => {
    if (accessPoint.id === selectedPickupId) {
      notifications.show({
        title: 'Invalid Selection',
        message: 'Pickup and dropoff points cannot be the same',
        color: 'orange',
        icon: <IconAlertCircle size="1rem" />,
      });
      return;
    }
    setSelectedDropoffId(accessPoint.id);
  };

  const handleSave = (pickupId: string, dropoffId: string) => {
    updateTransitPointsMutation.mutate({
      pickup_access_point_id: pickupId,
      dropoff_access_point_id: dropoffId,
    });
  };

  if (status === 'loading' || isLoading || accessPointsLoading) {
    return (
      <Center style={{ height: '100vh' }}>
        <Loader />
      </Center>
    );
  }

  if (error || accessPointsError) {
    return (
      <Container size="lg" p="xl">
        <Alert icon={<IconAlertCircle size="1rem" />} title="Error" color="red">
          Failed to load data. Please try again.
        </Alert>
      </Container>
    );
  }

  if (!session || session.user?.user_type !== 'CAR_OPERATOR') {
    return null;
  }

  if (!profileData?.data?.user) {
    return (
      <Container size="lg" p="xl">
        <Alert icon={<IconAlertCircle size="1rem" />} title="No Data" color="yellow">
          Unable to load your profile data.
        </Alert>
      </Container>
    );
  }

  const hasSelectedBothPoints = selectedPickupId && selectedDropoffId;

  return (
    <Container size="lg" p="xl">
      <Stack gap="lg">
        <Paper shadow="sm" p="xl" radius="md" withBorder>
          <div style={{ marginBottom: '1.5rem' }}>
            <Title order={2} mb="xs">
              <IconRoute size="1.5rem" style={{ marginRight: '0.5rem', display: 'inline' }} />
              Transit Points Selection
            </Title>
            <Text c="dimmed">
              Choose your pickup and dropoff access points. You will transit between these two points,
              picking up packages from one and delivering to the other.
            </Text>
          </div>

          {!hasSelectedBothPoints ? (
            <Alert icon={<IconAlertCircle size="1rem" />} color="orange" mb="lg">
              <Text fw={500}>Transit Route Not Complete</Text>
              <Text size="sm">
                Please select both pickup and dropoff points to establish your transit route.
                This is required to start accepting delivery jobs.
              </Text>
            </Alert>
          ) : (
            <Alert icon={<IconCheck size="1rem" />} color="green" mb="lg">
              <Text fw={500}>Transit Route Configured</Text>
              <Text size="sm">
                Your transit route is set. You can update it anytime by selecting different points on the map.
              </Text>
            </Alert>
          )}

          <TransitPointsMap
            accessPoints={accessPoints}
            selectedPickupId={selectedPickupId}
            selectedDropoffId={selectedDropoffId}
            onPickupSelect={handlePickupSelect}
            onDropoffSelect={handleDropoffSelect}
            onSave={handleSave}
            isEditable
          />
        </Paper>

        {/* Additional Information */}
        <Paper shadow="sm" p="lg" radius="md" withBorder>
          <Title order={4} mb="md">Important Information</Title>
          <Stack gap="sm">
            <Text size="sm">
              •
              {' '}
              <strong>Pickup Point:</strong>
              {' '}
              The access point where you will collect packages for delivery.
            </Text>
            <Text size="sm">
              •
              {' '}
              <strong>Dropoff Point:</strong>
              {' '}
              The access point where you will deliver the collected packages.
            </Text>
            <Text size="sm">
              • You can only operate between these two selected points.
            </Text>
            <Text size="sm">
              • Choose points that create an efficient route for your operations.
            </Text>
            <Text size="sm">
              • Both points must be different - you cannot select the same location for pickup and dropoff.
            </Text>
            <Text size="sm">
              • You can change your transit route at any time, but it may affect ongoing deliveries.
            </Text>
          </Stack>
        </Paper>
      </Stack>
    </Container>
  );
}
