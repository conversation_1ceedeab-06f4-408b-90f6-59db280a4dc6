import { createTheme, DEFAULT_THEME } from '@mantine/core';

// Base theme configuration
const baseTheme = {
  components: {
    Modal: {
      defaultProps: {
        zIndex: 10050,
      },
    },
    Tooltip: {
      defaultProps: {
        zIndex: 10600,
      },
    },
    Popover: {
      defaultProps: {
        zIndex: 10500,
      },
    },
  },
};

// Create theme with locale-specific font
export const createLocaleTheme = (locale: string = 'ar') => {
  const fontFamily = locale === 'ar'
    ? 'var(--font-cairo), Cairo, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif'
    : 'var(--font-roboto-condensed), "Roboto Condensed", -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif';

  return createTheme({
    ...baseTheme,
    fontFamily,
  });
};

// Default theme (Arabic)
export const theme = createLocaleTheme('ar');
