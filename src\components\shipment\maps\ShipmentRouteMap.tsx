import dynamic from 'next/dynamic';
import {
  Paper, Stack, Text, Group, Alert, Loader, Center,
} from '@mantine/core';
import {
  IconTruck, IconFlag, IconAlertCircle,
} from '@tabler/icons-react';
import useTranslation from 'next-translate/useTranslation';
import { useQuery } from '@tanstack/react-query';
import { getAccessPointQuery, AccessPoint } from '../../../requests';
import { Shipment } from '../../../requests/shipment';

const DynamicRouteMap = dynamic(() => import('./ShipmentRouteMapClient'), {
  ssr: false,
  loading: () => (
    <Paper withBorder style={{ height: '400px' }}>
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
      >
        Loading map...
      </div>
    </Paper>
  ),
});

interface ShipmentRouteMapProps {
  shipment: Shipment;
}

export default function ShipmentRouteMap({ shipment }: ShipmentRouteMapProps) {
  const { t } = useTranslation('shipments');

  // Fetch origin access point
  const {
    data: originData,
    isLoading: originLoading,
    error: originError,
  } = useQuery(
    getAccessPointQuery({
      id: shipment.originAoId,
      enabled: !!shipment.originAoId,
    }),
  );

  // Fetch destination access point
  const {
    data: destData,
    isLoading: destLoading,
    error: destError,
  } = useQuery(
    getAccessPointQuery({
      id: shipment.destAoId,
      enabled: !!shipment.destAoId,
    }),
  );

  // Extract access point data
  const originAccessPoint: AccessPoint | null = originData?.data?.accessOperator || null;
  const destAccessPoint: AccessPoint | null = destData?.data?.accessOperator || null;

  // Loading state
  if (originLoading || destLoading) {
    return (
      <Center py="xl">
        <Stack align="center" gap="md">
          <Loader size="md" />
          <Text size="sm" c="dimmed">Loading route map...</Text>
        </Stack>
      </Center>
    );
  }

  // Error state
  if (originError || destError) {
    return (
      <Alert color="red" variant="light" icon={<IconAlertCircle size="1rem" />}>
        <Text size="sm">
          Failed to load access point information for the route map.
        </Text>
      </Alert>
    );
  }

  // Check if both access points have valid coordinates
  const hasValidOrigin = originAccessPoint?.geoLatitude && originAccessPoint?.geoLongitude;
  const hasValidDest = destAccessPoint?.geoLatitude && destAccessPoint?.geoLongitude;

  if (!hasValidOrigin || !hasValidDest) {
    return (
      <Alert color="yellow" variant="light" icon={<IconAlertCircle size="1rem" />}>
        <Text size="sm">
          Route map is not available because one or both access points don&apos;t have location coordinates.
        </Text>
      </Alert>
    );
  }

  return (
    <Stack gap="md">
      {/* Access Points Info */}
      <Group justify="space-between">
        <Group gap="xs">
          <IconTruck size="1rem" color="green" />
          <div>
            <Text size="sm" fw={500}>Origin</Text>
            <Text size="xs" c="dimmed">
              {originAccessPoint.businessName || originAccessPoint.name}
            </Text>
            {originAccessPoint.address && (
              <Text size="xs" c="dimmed">{originAccessPoint.address}</Text>
            )}
          </div>
        </Group>
        <Group gap="xs">
          <IconFlag size="1rem" color="red" />
          <div>
            <Text size="sm" fw={500}>Destination</Text>
            <Text size="xs" c="dimmed">
              {destAccessPoint.businessName || destAccessPoint.name}
            </Text>
            {destAccessPoint.address && (
              <Text size="xs" c="dimmed">{destAccessPoint.address}</Text>
            )}
          </div>
        </Group>
      </Group>

      {/* Dynamic Map Component */}
      <DynamicRouteMap
        originAccessPoint={originAccessPoint}
        destAccessPoint={destAccessPoint}
      />

      {/* Instructions */}
      <Alert color="blue" variant="light">
        <Text size="sm">
          <strong>{t('routeInformationTitle')}</strong>
          <br />
          {t('greenMarkerInfo')}
          <br />
          {t('redMarkerInfo')}
        </Text>
      </Alert>
    </Stack>
  );
}
