/**
 * This functions for my shipments
 */

import { HTTP_CODE } from '../../data';
import { getMyShipmentsQueryProps } from './types';
import { CLIENT_API } from '../../lib/axios';
import { handleApiError } from '../../utils/handle-backend-error';

// React Query keys
enum queryKeys {
  myShipments = 'my-shipments',
  myShipment = 'my-shipment',
}

/**
 * @description This function calls to get list of my shipments.
 * There are params will passe are in request params to filter and paginate data response.
 * @param props
 * @returns List of my shipments with pagination
 */
const getMyShipmentsRequest = (props: getMyShipmentsQueryProps) => {
  const {
    pagination, filters, sort, search,
  } = props;

  // Format parameters to match backend expectations - using filter[key] format
  const params: Record<string, unknown> = {
    ...pagination,
    ...sort,
  };

  // Add filter parameters in the format expected by backend
  if (filters) {
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params[`filter[${key}]`] = value;
      }
    });
  }

  // Add search parameter as filter[search] if provided separately
  if (search && search.trim()) {
    params['filter[search]'] = search;
  }

  return CLIENT_API.get('/shipments/my', {
    params,
  })
    .then((res) => res?.data)
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

// get all my shipments query function
export const getMyShipmentsQuery = (props: getMyShipmentsQueryProps) => ({
  queryKey: [
    queryKeys.myShipments,
    props?.filters,
    props?.pagination,
    props?.sort,
    props?.search,
  ],
  queryFn: () => getMyShipmentsRequest({ ...props }),
  refetchOnWindowFocus: false,
  retry: (_failureCount: number, error: { code: HTTP_CODE }) => error?.code !== HTTP_CODE.UNAUTHORIZED,
});
