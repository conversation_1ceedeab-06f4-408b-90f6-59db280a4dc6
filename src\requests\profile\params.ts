/* eslint-disable sonarjs/cognitive-complexity */
/* eslint-disable complexity */
import { NextApiRequest } from 'next';

const toSnakeCase = (str: string) => str.replace(/([A-Z])/g, '_$1').toLowerCase();

type RelationshipConfig = {
  relationField: string;
  entityField: string;
  valueField: string;
};

export const buildProfileFilter = (filterParams: Record<string, string | string[]>) => {
  const relationships: Record<string, RelationshipConfig> = {
    // Add relationships if needed in the future
    // accessPoints: {
    //   relationField: 'AccessPoints',
    //   entityField: 'access_point',
    //   valueField: 'id',
    // },
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const filterConditions: Record<string, any>[] = [];

  Object.entries(filterParams).forEach(([key, value]) => {
    if (!value || value === '') return;

    if (key.endsWith('Gte')) {
      const fieldName = toSnakeCase(key.replace('Gte', ''));
      filterConditions.push({ [fieldName]: { gte: value } });
      return;
    }

    if (key.endsWith('Lte')) {
      const fieldName = toSnakeCase(key.replace('Lte', ''));
      filterConditions.push({ [fieldName]: { lte: value } });
      return;
    }

    if (key === 'hasLocation' && typeof value === 'string') {
      if (value === 'true') {
        filterConditions.push({
          AND: [
            { geo_latitude: { not: null } },
            { geo_longitude: { not: null } },
          ],
        });
      } else if (value === 'false') {
        filterConditions.push({
          OR: [
            { geo_latitude: null },
            { geo_longitude: null },
          ],
        });
      }
      return;
    }

    if (key === 'hasAccessPoints' && typeof value === 'string') {
      if (value === 'true') {
        filterConditions.push({
          OR: [
            { pickup_access_point_id: { not: null } },
            { dropoff_access_point_id: { not: null } },
          ],
        });
      } else if (value === 'false') {
        filterConditions.push({
          AND: [
            { pickup_access_point_id: null },
            { dropoff_access_point_id: null },
          ],
        });
      }
      return;
    }

    if (key === 'search' && typeof value === 'string') {
      filterConditions.push({
        OR: [
          { name: { contains: value, mode: 'insensitive' } },
          { email: { contains: value, mode: 'insensitive' } },
          { phone: { contains: value, mode: 'insensitive' } },
          { business_name: { contains: value, mode: 'insensitive' } },
          { license_number: { contains: value, mode: 'insensitive' } },
        ],
      });
      return;
    }

    if (relationships[key]) {
      const config = relationships[key];
      const values = Array.isArray(value) ? value : [value];

      values.forEach((val) => {
        filterConditions.push({
          [config.relationField]: {
            some: {
              [config.entityField]: {
                [config.valueField]: val,
              },
            },
          },
        });
      });
      return;
    }

    // Handle boolean fields
    if (key === 'approved' || key === 'emailVerified') {
      const fieldName = key === 'emailVerified' ? 'email_verified' : key;
      if (value === 'true') {
        filterConditions.push({ [fieldName]: true });
      } else if (value === 'false') {
        filterConditions.push({ [fieldName]: false });
      }
      return;
    }

    filterConditions.push({ [toSnakeCase(key)]: value });
  });

  return filterConditions.length ? { AND: filterConditions } : undefined;
};

export const getFilterParams = (query: Record<string, string | string[]>) => Object.entries(query).reduce((params: Record<string, string | string[]>, [key, value]) => {
  if (!key.startsWith('filter[') || !value || value === '') {
    return params;
  }

  const filterKey = key.match(/\[(.*?)\]/)?.[1];
  if (!filterKey) {
    return params;
  }

  return { ...params, [filterKey]: value };
}, {});

export const getPaginationParams = (query: Record<string, string | string[]>) => {
  const commonKeys = ['search', 'page', 'pageSize', 'sortBy', 'sortOrder'];

  return commonKeys.reduce((params, key) => {
    const value = query[key];
    if (!value || value === '') {
      return params;
    }

    // Convert page/pageSize to Prisma's skip/take
    if (key === 'page' || key === 'pageSize') {
      const numValue = parseInt(Array.isArray(value) ? value[0] : value, 10);
      if (key === 'page') {
        return { ...params, page: numValue };
      }
      if (key === 'pageSize') {
        return { ...params, pageSize: numValue };
      }
    }

    return {
      ...params,
      [key]: Array.isArray(value) ? value[0] : value,
    };
  }, {});
};

export const getRestParams = (query: Record<string, string | string[]>) => {
  const skipKeys = ['search', 'page', 'pageSize', 'sortBy', 'sortOrder'];

  return Object.entries(query).reduce((params, [key, value]) => {
    if (!value || value === ''
        || key.startsWith('filter[')
        || skipKeys.includes(key)) {
      return params;
    }

    return {
      ...params,
      [key]: Array.isArray(value) ? value[0] : value,
    };
  }, {});
};

// Convert page/pageSize to Prisma's skip/take
export const convertToPrismaPagination = (params: Record<string, unknown>) => {
  const { page, pageSize, ...rest } = params;

  if (typeof page === 'number' && typeof pageSize === 'number') {
    return {
      ...rest,
      skip: (page - 1) * pageSize,
      take: pageSize,
    };
  }

  return params;
};

export const returnParams = (req: NextApiRequest) => {
  const query = req.query as Record<string, string | string[]>;

  const filterParams = getFilterParams(query);
  const filter = buildProfileFilter(filterParams);
  const paginationParams = getPaginationParams(query);
  const restParams = getRestParams(query);

  const allParams = {
    ...paginationParams,
    ...restParams,
    ...(filter ? { filter: JSON.stringify(filter) } : {}),
  };

  // Convert to Prisma pagination format
  return convertToPrismaPagination(allParams);
};

// Legacy functions for backward compatibility
export const profileSortKeysMapping = new Map<string, string>([
  ['name', 'name'],
  ['email', 'email'],
  ['phone', 'phone'],
  ['userType', 'user_type'],
  ['status', 'status'],
  ['createdAt', 'created_at'],
  ['updatedAt', 'updated_at'],
  ['businessName', 'business_name'],
  ['address', 'address'],
  ['licenseNumber', 'license_number'],
  ['vehicleInfo', 'vehicle_info'],
  ['approved', 'approved'],
  ['emailVerified', 'email_verified'],
]);

export const buildProfileWhereClause = () => {
  const where: Record<string, unknown> = {};
  // Implementation moved to buildProfileFilter for consistency
  return where;
};

export const buildProfileOrderByClause = (sort: string | null, sortBy: string | null, sortOrder: string = 'asc') => {
  if (sort) {
    const [field, direction] = sort.split(':');
    const mappedField = profileSortKeysMapping.get(field) || field;
    return { [mappedField]: direction || 'asc' };
  }

  if (sortBy) {
    const mappedField = profileSortKeysMapping.get(sortBy) || sortBy;
    return { [mappedField]: sortOrder };
  }

  return { created_at: 'desc' };
};

// Keep the old function name for backward compatibility
export const returnProfileParams = returnParams;
