import { Group, TextInput } from '@mantine/core';
import { UseFormReturnType } from '@mantine/form';
import useTranslation from 'next-translate/useTranslation';
import { PhoneNumberInput } from '../../common/PhoneNumberInput';

interface FormValues {
  originAoId: string;
  destAoId: string;
  weight: number;
  size: string;
  description: string;
  receiverName: string;
  receiverPhone: string;
}

interface ReceiverDetailsFormProps {
  form: UseFormReturnType<FormValues>;
}

export function ReceiverDetailsForm({ form }: ReceiverDetailsFormProps) {
  const { t } = useTranslation('shipments');

  return (
    <Group grow>
      <TextInput
        label={t('receiverName')}
        placeholder={t('enterReceiverName')}
        required
        {...form.getInputProps('receiverName')}
      />
      <PhoneNumberInput
        form={form}
        field="receiverPhone"
        label={t('receiverPhone')}
        required
      />
    </Group>
  );
}
