/* eslint-disable camelcase */
import type { NextApiRequest, NextApiResponse } from 'next';
import { getJwt } from '../../../src/utils';
import { BACKEND_API } from '../../../src/lib/axios';
import { API_ENDPOINT, HTTP_CODE } from '../../../src/data';
import createApiError from '../../../src/utils/create-api-error';
import { transformGenerateForShipmentResponse } from '../../../src/requests/qr-labels/response-transformer';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(HTTP_CODE.METHOD_NOT_ALLOWED).json({
      success: false,
      message: 'Method not allowed',
    });
  }

  try {
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');

    const { token } = await getJwt(req);
    if (!token) {
      return res.status(HTTP_CODE.UNAUTHORIZED).json({
        success: false,
        message: 'Authentication required',
      });
    }

    const { shipment_id } = req.body;

    if (!shipment_id || typeof shipment_id !== 'string') {
      return res.status(HTTP_CODE.BAD_REQUEST).json({
        success: false,
        message: 'Shipment ID is required',
      });
    }

    const requestData = {
      shipment_id,
    };

    // Send to backend API
    const response = await BACKEND_API.post(API_ENDPOINT.qrLabels.generateForShipment, requestData, {
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });

    const transformed = transformGenerateForShipmentResponse(response.data);
    return res.status(HTTP_CODE.SUCCESS).json(transformed);
  } catch (error: unknown) {
    const apiError = createApiError({ error });
    return res.status(apiError.code).json(apiError);
  }
}
