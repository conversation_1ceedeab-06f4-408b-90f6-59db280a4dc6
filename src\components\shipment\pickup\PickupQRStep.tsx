import {
  Stack,
  Group,
  Text,
  Button,
  Alert,
  TextInput,
  Tabs,
} from '@mantine/core';
import {
  IconCamera,
  IconCheck,
  IconKeyboard,
} from '@tabler/icons-react';
import useTranslation from 'next-translate/useTranslation';
import QRScanner from '../../common/QRScanner';

interface PickupQRStepProps {
  pickupQR: string;
  setPickupQR: (value: string) => void;
  scanMethod: 'camera' | 'manual';
  setScanMethod: (method: 'camera' | 'manual') => void;
  error: string | null;
  setError: (error: string | null) => void;
  onBack: () => void;
  onNext: () => void;
  opened: boolean;
  activeStep: number;
  // eslint-disable-next-line react/require-default-props
  expectedActiveStep?: number;
}

export default function PickupQRStep({
  pickupQR,
  setPickupQR,
  scanMethod,
  setScanMethod,
  error,
  setError,
  onBack,
  onNext,
  opened,
  activeStep,
  expectedActiveStep = 1,
}: PickupQRStepProps) {
  const { t } = useTranslation('shipments');

  const clearAlerts = () => {
    setError(null);
  };

  const handlePickupQRScan = (value: string) => {
    clearAlerts();
    setPickupQR(value);
  };

  const validatePickupQR = () => {
    if (!pickupQR) {
      setError(t('pleaseScanEnterPickupCode'));
      return false;
    }
    return true;
  };

  const scanned = !!pickupQR && !error;

  return (
    <Stack gap="md">
      {scanned ? (
        <Alert icon={<IconCheck size="1rem" />} color="green" variant="light">
          <Text size="sm">{t('pickupCodeScanned')}</Text>
        </Alert>
      ) : (
        <>
          <Text fw={500}>
            {t('scanPickupCode')}
            :
          </Text>
          <Text size="sm" c="dimmed">
            {t('scanPickupCodeFromReceiver')}
          </Text>

          <Tabs value={scanMethod} onChange={(value) => setScanMethod(value as 'camera' | 'manual')}>
            <Tabs.List>
              <Tabs.Tab value="camera" leftSection={<IconCamera size="0.8rem" />}>
                {t('cameraScan')}
              </Tabs.Tab>
              <Tabs.Tab value="manual" leftSection={<IconKeyboard size="0.8rem" />}>
                {t('manualEntry')}
              </Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="camera" pt="md">
              <QRScanner
                onScan={handlePickupQRScan}
                onError={(e) => setError(e)}
                title={t('scanPickupCode')}
                description={t('pointCameraAtReceiverPickupCode')}
                isActive={scanMethod === 'camera' && opened && activeStep === expectedActiveStep && !pickupQR}
                debug
              />
            </Tabs.Panel>

            <Tabs.Panel value="manual" pt="md">
              <TextInput
                label={t('enterPickupCode')}
                placeholder={t('enterPickupCodePlaceholder')}
                value={pickupQR}
                onChange={(event) => {
                  clearAlerts();
                  setPickupQR(event.currentTarget.value);
                }}
                required
                description={t('enterPickupCodeFromReceiver')}
              />
            </Tabs.Panel>
          </Tabs>
        </>
      )}

      {/* Buttons always visible */}
      <Group justify="space-between" mt="md">
        <Button variant="light" onClick={onBack}>
          {t('back')}
        </Button>
        <Button
          onClick={() => {
            if (validatePickupQR()) {
              onNext();
            }
          }}
          disabled={!scanned && !pickupQR}
        >
          {t('nextStep')}
        </Button>
      </Group>
    </Stack>
  );
}
