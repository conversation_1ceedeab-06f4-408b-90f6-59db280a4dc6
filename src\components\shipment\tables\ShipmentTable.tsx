/* eslint-disable complexity */
/* eslint-disable max-lines */
/* eslint-disable react/require-default-props */
import React, {
  useState, useRef, useMemo, useEffect, useCallback,
} from 'react';
import { useRouter } from 'next/router';
import { useDebouncedCallback } from 'use-debounce';
import useTranslation from 'next-translate/useTranslation';
import {
  Stack,
  Group,
  TextInput,
  Select,
  Button,
  Text,
  Loader,
  Center,
  Alert,
  Paper,
  Modal,
  Grid,
  Pagination,
} from '@mantine/core';
import {
  IconSearch,
  IconFilter,
  IconAlertCircle,
  IconPackage,
  IconEye,
  IconRefresh,
  IconTruck,
  IconUserCheck,
  IconPlus,
  IconCancel,
} from '@tabler/icons-react';
import { useQuery } from '@tanstack/react-query';
import { useMediaQuery } from '@mantine/hooks';
import { useIsClient } from '../../../hooks/useIsClient';
import { getProfileQuery, SHIPMENT_STATUS } from '../../../requests';
import { Shipment, shipmentSortKeysMapping, getMyShipmentsQuery } from '../../../requests/shipment';
import { DataTableColumn, DataTable } from '../../common/DataTable';
import { StatusBadge } from '../../common/StatusBadge';
import { ShipmentCard, QRCodeDisplay } from '../cards';

interface ShipmentTableProps {
  onCreateNew?: () => void;
  onViewShipment?: (shipment: Shipment) => void;
  onCancelShipment?: (shipment: Shipment) => void;
  onAssignShipment?: (shipment: Shipment) => void;
  /**
   * Whether to display the "Customer" column. Default: true.
   * Hide it on pages where the viewer is always the customer (e.g. Customer > My Shipments).
   */
  showCustomerColumn?: boolean;
}

const ITEMS_PER_PAGE = 10;

// Define getSizeLabel utility if used in the file
const getSizeLabel = (size: string, t: (key: string) => string) => {
  switch (size) {
    case 'SMALL': return t('sizeSmall');
    case 'MEDIUM': return t('sizeMedium');
    case 'LARGE': return t('sizeLarge');
    case 'EXTRA_LARGE': return t('sizeExtraLarge');
    default: return size;
  }
};

// Helper function to get translated status label
const getStatusLabel = (status: string, t: (key: string) => string) => {
  switch (status) {
    case 'PENDING':
      return t('statusPending');
    case 'AWAITING_PICKUP':
      return t('statusAwaitingPickup');
    case 'IN_TRANSIT':
      return t('statusInTransit');
    case 'ARRIVED_AT_DESTINATION':
      return t('statusArrivedAtDestination');
    case 'DELIVERED':
      return t('statusDelivered');
    case 'CANCELLED':
      return t('statusCancelled');
    case 'EXPIRED':
      return t('statusExpired');
    default:
      return status.replace('_', ' ').toLowerCase().replace(/\b\w/g, (l) => l.toUpperCase());
  }
};

// eslint-disable-next-line sonarjs/cognitive-complexity
export default function ShipmentTable({
  onCreateNew,
  onViewShipment,
  onCancelShipment,
  onAssignShipment,
  showCustomerColumn = true,
}: ShipmentTableProps) {
  const router = useRouter();
  const isClient = useIsClient();
  const isDesktop = useMediaQuery('(min-width: 992px)');
  const { t } = useTranslation('shipments');
  const isRTL = router.locale === 'ar';
  const { data: profileData } = useQuery({
    ...getProfileQuery({}),
  });

  // Safe date formatting function
  const formatDate = (dateString: string) => {
    if (!isClient) return t('loading');
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return t('invalidDate');
    }
  };

  // State for pagination and filters
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(ITEMS_PER_PAGE);
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [searchInput, setSearchInput] = useState('');
  const [searchQuery, setSearchQuery] = useState(''); // Separate state for the actual search query
  const searchInputRef = useRef<HTMLInputElement>(null);
  const [qrModalOpen, setQrModalOpen] = useState(false);
  const [selectedShipmentForQR, setSelectedShipmentForQR] = useState<Shipment | null>(null);

  // Track initialization to prevent loops
  const isInitializedRef = useRef(false);
  const skipUrlUpdateRef = useRef(false);

  // Sort state - default to newest first (createdAt desc)
  const [sortStatus, setSortStatus] = useState({
    columnAccessor: 'createdAt',
    direction: 'desc' as 'asc' | 'desc',
  });

  // Get filters from URL query parameters
  const statusFilterFromUrl = `${router.query.status || ''}`;
  const searchFromUrl = `${router.query.query || ''}`;

  // Debounced search handler - only updates search query and URL
  const debouncedSearch = useDebouncedCallback((term: string) => {
    setPage(1);
    setSearchQuery(term);

    // Don't push a new URL if the query string is already the same
    const current = (router.query.query ?? '') as string;
    if (current === term.trim()) {
      return;
    }

    const newQuery = { ...router.query } as Record<string, string>;
    if (term.trim()) newQuery.query = term;
    else delete newQuery.query;

    // Set flag to skip the next URL sync
    skipUrlUpdateRef.current = true;

    router.replace(
      { pathname: router.pathname, query: newQuery },
      undefined,
      { shallow: true, scroll: false },
    );
  }, 300);

  // Immediate input handler - only updates UI input value
  const handleInputChange = useCallback((value: string) => {
    setSearchInput(value); // Update input display immediately
    debouncedSearch(value); // Debounce the actual search
  }, [debouncedSearch]);

  // Initialize state from URL on mount only
  useEffect(() => {
    if (isInitializedRef.current) return;
    isInitializedRef.current = true;

    // Initialize from URL
    const urlSearchValue = searchFromUrl !== 'undefined' && searchFromUrl !== '' ? searchFromUrl : '';
    const urlStatusValue = statusFilterFromUrl !== 'undefined' && statusFilterFromUrl !== '' ? statusFilterFromUrl : null;

    setSearchInput(urlSearchValue);
    setSearchQuery(urlSearchValue);
    setStatusFilter(urlStatusValue);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Empty dependency array - only run once on mount

  // Sync URL changes to state (but skip if we just updated the URL ourselves)
  useEffect(() => {
    // Skip the initial mount
    if (!isInitializedRef.current) return;

    // Skip if we just updated the URL from our component
    if (skipUrlUpdateRef.current) {
      skipUrlUpdateRef.current = false;
      return;
    }

    // Only update if URL params actually changed from external source (e.g., browser back/forward)
    const urlSearchValue = searchFromUrl !== 'undefined' && searchFromUrl !== '' ? searchFromUrl : '';
    const urlStatusValue = statusFilterFromUrl !== 'undefined' && statusFilterFromUrl !== '' ? statusFilterFromUrl : null;

    // Update status if different
    if (urlStatusValue !== statusFilter) {
      setStatusFilter(urlStatusValue);
    }

    // Update search if different (from external navigation)
    if (urlSearchValue !== searchQuery) {
      setSearchQuery(urlSearchValue);
      setSearchInput(urlSearchValue);
    }
  }, [statusFilterFromUrl, searchFromUrl, searchQuery, statusFilter]);

  // Build filters object for backend - memoized to prevent unnecessary re-renders
  const filters = useMemo(() => {
    const searchValue = searchQuery.trim();
    const baseFilters = { search: searchValue, status: statusFilter || undefined };
    const user = profileData?.data?.user;
    if (user?.accessPointId) {
      return { ...baseFilters, aoId: user.accessPointId };
    }
    return baseFilters;
  }, [searchQuery, statusFilter, profileData]);

  // Build sort object for backend - memoized to prevent unnecessary re-renders
  const sortObj = useMemo(() => ({
    sortBy: shipmentSortKeysMapping.has(sortStatus.columnAccessor)
      ? shipmentSortKeysMapping.get(sortStatus.columnAccessor)
      : sortStatus.columnAccessor,
    sortOrder: sortStatus.direction as 'asc' | 'desc',
  }), [sortStatus.columnAccessor, sortStatus.direction]);

  // Fetch shipments with backend filtering
  const {
    data: shipmentsData,
    isLoading,
    isFetching,
    error,
    refetch,
  } = useQuery({
    ...getMyShipmentsQuery({
      pagination: {
        page: page - 1,
        pageSize,
      },
      filters,
      sort: `${sortObj.sortBy}:${sortObj.sortOrder}`,
    }),
    refetchOnWindowFocus: false,
    staleTime: 30000, // Cache for 30 seconds to prevent excessive requests
    gcTime: 300000, // Keep in cache for 5 minutes
    refetchOnMount: false, // Don't always refetch on mount
    // Add retry configuration to prevent excessive requests
    retry: 1,
    retryDelay: 1000,
  });

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const shipments = shipmentsData?.data?.shipments || [];
  const totalPages = shipmentsData?.data?.pagination?.totalPages || Math.ceil((shipmentsData?.data?.pagination?.total || 0) / pageSize);
  const totalCount = shipmentsData?.data?.pagination?.total || 0;

  // Calculate stats from backend data (fallback to client-side calculation if not provided) - memoized
  const stats = useMemo(() => ({
    total: totalCount,
    pending: shipmentsData?.data?.stats?.pending || shipments.filter((s: Shipment) => s.status === 'PENDING').length,
    inTransit: shipmentsData?.data?.stats?.inTransit || shipments.filter((s: Shipment) => ['AWAITING_PICKUP', 'IN_TRANSIT', 'ARRIVED_AT_DESTINATION'].includes(s.status)).length,
    arrived: shipmentsData?.data?.stats?.arrived || shipments.filter((s: Shipment) => s.status === 'ARRIVED_AT_DESTINATION').length,
    delivered: shipmentsData?.data?.stats?.delivered || shipments.filter((s: Shipment) => s.status === 'DELIVERED').length,
    cancelled: shipmentsData?.data?.stats?.cancelled || shipments.filter((s: Shipment) => s.status === 'CANCELLED').length,
  }), [totalCount, shipmentsData?.data?.stats, shipments]);

  const handlePageSizeChange = (newPageSize: string | null) => {
    if (newPageSize) {
      setPageSize(parseInt(newPageSize, 10));
      setPage(1); // Reset to first page when changing page size
    }
  };

  const handleStatusChange = (value: string | null) => {
    setPage(1); // Reset to first page when filtering
    setStatusFilter(value);

    // Update URL efficiently
    const newQuery = { ...router.query };
    if (value) {
      newQuery.status = value;
    } else {
      delete newQuery.status;
    }

    skipUrlUpdateRef.current = true;
    router.replace({
      pathname: router.pathname,
      query: newQuery,
    }, undefined, { shallow: true });
  };

  const clearFilters = () => {
    // Clear all states immediately for smooth UI
    setPage(1);
    setStatusFilter(null);
    setSearchInput('');
    setSearchQuery('');

    // Clear URL params efficiently
    const newQuery = { ...router.query };
    delete newQuery.status;
    delete newQuery.query;

    skipUrlUpdateRef.current = true;
    router.replace({
      pathname: router.pathname,
      query: newQuery,
    }, undefined, { shallow: true });
  };

  const handleCloseQRModal = () => {
    setQrModalOpen(false);
    setSelectedShipmentForQR(null);
  };

  // Memoized columns definition to prevent re-renders
  const columns = useMemo(() => {
    const baseColumns: DataTableColumn<Shipment>[] = [
      {
        label: t('shipmentId'),
        accessor: 'id',
        render: (shipment) => (
          <Text size="sm" fw={500}>
            {`#${shipment.id.slice(-8).toUpperCase()}`}
          </Text>
        ),
        sortable: true,
      },
      {
        label: t('status'),
        accessor: 'status',
        render: (shipment) => <StatusBadge status={shipment.status} />,
        sortable: true,
      },
      {
        label: t('weightAndSize'),
        accessor: 'weight',
        render: (shipment) => (
          <Text size="sm">
            {shipment.weight}
            {'kg • '}
            {getSizeLabel(shipment.size, t)}
          </Text>
        ),
      },
    ];

    // Add receiver column
    const receiverColumn: DataTableColumn<Shipment> = {
      label: t('receiver'),
      accessor: 'receiverName',
      render: (shipment) => (
        <Text size="sm">{shipment.receiverName}</Text>
      ),
    };
    baseColumns.push(receiverColumn);

    // Optionally add customer column
    if (showCustomerColumn) {
      const customerColumn: DataTableColumn<Shipment> = {
        label: t('customer'),
        accessor: 'customerId',
        render: (shipment) => (
          <Text size="sm">{shipment.customerId}</Text>
        ),
      };
      baseColumns.push(customerColumn);
    }

    // Add last updated column at the end
    const lastUpdatedColumn: DataTableColumn<Shipment> = {
      label: t('lastUpdated'),
      accessor: 'updatedAt',
      render: (shipment) => (
        <Text size="sm">
          {formatDate(shipment.updatedAt)}
        </Text>
      ),
      sortable: true,
    };
    baseColumns.push(lastUpdatedColumn);

    return baseColumns;
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [t, showCustomerColumn]);

  const rowActions = useCallback((shipment: Shipment) => (
    <Group gap="xs" justify="center">
      <Button
        variant="outline"
        size="xs"
        leftSection={<IconEye size="1rem" />}
        onClick={() => onViewShipment?.(shipment)}
      >
        {t('view')}
      </Button>
      {shipment.status === 'AWAITING_PICKUP' && (
        <Button
          variant="light"
          color="blue"
          size="xs"
          leftSection={<IconUserCheck size="1rem" />}
          onClick={() => onAssignShipment?.(shipment)}
        >
          {t('assign')}
        </Button>
      )}
      {shipment.status === 'PENDING' && onCancelShipment && (
        <Button
          variant="light"
          color="red"
          size="xs"
          leftSection={<IconCancel size="1rem" />}
          onClick={() => onCancelShipment(shipment)}
        >
          {t('cancel')}
        </Button>
      )}
    </Group>
  ), [onViewShipment, onAssignShipment, onCancelShipment, t]);

  // Memoized pagination object to prevent re-renders
  const paginationProps = useMemo(() => ({
    page,
    totalPages,
    onPageChange: setPage,
  }), [page, totalPages]);

  // Memoized sort state to prevent re-renders
  const sortStateProps = useMemo(() => ({
    accessor: sortStatus.columnAccessor,
    direction: sortStatus.direction,
  }), [sortStatus.columnAccessor, sortStatus.direction]);

  // Memoized sort change handler
  const handleSortChange = useCallback((sort: { accessor: string; direction: 'asc' | 'desc' }) => {
    setSortStatus({ columnAccessor: sort.accessor, direction: sort.direction });
  }, []);

  // Show loading state during hydration or actual loading
  if (!isClient || (!shipmentsData && isLoading)) {
    return (
      <Stack
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: 'calc(100vh - 150px)',
          flexDirection: 'column',
        }}
      >
        <Loader />
        <Text>{!isClient ? t('initializing') : t('loadingShipments')}</Text>
      </Stack>
    );
  }

  if (error) {
    return (
      <Alert icon={<IconAlertCircle size="1rem" />} color="red" title="Error">
        Failed to load shipments. Please try again.
        <Button variant="light" size="xs" mt="sm" onClick={() => refetch()}>
          Retry
        </Button>
      </Alert>
    );
  }

  return (
    <Stack gap="lg">
      {/* Header with stats */}
      <Paper p="md" withBorder>
        <Group justify="space-between" mb="md">
          {/* For RTL: Buttons go on the left, title on the right */}
          {isRTL ? (
            <>
              <Group>
                {onCreateNew && (
                <Button
                  rightSection={<IconPlus size="1rem" />}
                  onClick={onCreateNew}
                >
                  {t('createNewShipment')}
                </Button>
                )}
                <Button
                  rightSection={<IconRefresh size="1rem" />}
                  variant="light"
                  onClick={() => refetch()}
                  loading={isFetching}
                >
                  {t('refresh')}
                </Button>
              </Group>
              <Group gap="md">
                <IconTruck size="1.5rem" color="cyan" />
                <div>
                  <Text size="lg" fw={600}>{t('allShipments')}</Text>
                  <Text size="sm" c="dimmed">{t('allShipmentsDescription')}</Text>
                </div>
              </Group>
            </>
          ) : (
            <>
              <Group gap="md">
                <IconTruck size="1.5rem" color="cyan" />
                <div>
                  <Text size="lg" fw={600}>{t('allShipments')}</Text>
                  <Text size="sm" c="dimmed">{t('allShipmentsDescription')}</Text>
                </div>
              </Group>
              <Group>
                {onCreateNew && (
                <Button
                  leftSection={<IconPlus size="1rem" />}
                  onClick={onCreateNew}
                >
                  {t('createNewShipment')}
                </Button>
                )}
                <Button
                  leftSection={<IconRefresh size="1rem" />}
                  variant="light"
                  onClick={() => refetch()}
                  loading={isFetching}
                >
                  {t('refresh')}
                </Button>
              </Group>
            </>
          )}
        </Group>

        {/* Stats */}
        <Group gap="xl">
          <div>
            <Text size="xl" fw={700} c="cyan">{stats.total}</Text>
            <Text size="sm" c="dimmed">{t('total')}</Text>
          </div>
          <div>
            <Text size="xl" fw={700} c="orange">{stats.pending}</Text>
            <Text size="sm" c="dimmed">{t('statusPending')}</Text>
          </div>
          <div>
            <Text size="xl" fw={700} c="cyan">{stats.inTransit}</Text>
            <Text size="sm" c="dimmed">{t('statusInTransit')}</Text>
          </div>
          <div>
            <Text size="xl" fw={700} c="blue">{stats.arrived}</Text>
            <Text size="sm" c="dimmed">{t('statusArrivedAtDestination')}</Text>
          </div>
          <div>
            <Text size="xl" fw={700} c="green">{stats.delivered}</Text>
            <Text size="sm" c="dimmed">{t('statusDelivered')}</Text>
          </div>
          <div>
            <Text size="xl" fw={700} c="red">{stats.cancelled}</Text>
            <Text size="sm" c="dimmed">{t('statusCancelled')}</Text>
          </div>
        </Group>
      </Paper>

      {/* Filters */}
      <Paper p="md" withBorder>
        <Group gap="md" align="flex-end">
          <TextInput
            ref={searchInputRef}
            placeholder={t('searchShipments')}
            leftSection={<IconSearch size="1rem" />}
            value={searchInput}
            onChange={(e) => handleInputChange(e.currentTarget.value)}
            style={{ flex: 1 }}
          />
          <Select
            placeholder={t('filterByStatus')}
            leftSection={<IconFilter size="1rem" />}
            data={[
              { value: '', label: t('allStatuses') },
              ...SHIPMENT_STATUS.map((status) => ({
                value: status,
                label: getStatusLabel(status, t),
              })),
            ]}
            value={statusFilter}
            onChange={handleStatusChange}
            clearable
            style={{ minWidth: 200 }}
          />
          <Select
            placeholder={t('itemsPerPage')}
            data={[
              { value: '10', label: t('perPageCount', { count: 10 }) },
              { value: '25', label: t('perPageCount', { count: 25 }) },
              { value: '50', label: t('perPageCount', { count: 50 }) },
            ]}
            value={pageSize.toString()}
            onChange={handlePageSizeChange}
            style={{ minWidth: 150 }}
          />
          {(searchQuery || statusFilter) && (
            <Button variant="light" onClick={clearFilters}>
              {t('clearFilters')}
            </Button>
          )}
        </Group>
      </Paper>

      {/* Results Info */}
      {totalCount > 0 && (
        <Group justify="space-between" align="center">
          <Text size="sm" c="dimmed">
            Showing
            {' '}
            {((page - 1) * pageSize) + 1}
            {' '}
            to
            {' '}
            {Math.min(page * pageSize, totalCount)}
            {' '}
            of
            {' '}
            {totalCount}
            {' '}
            shipments
          </Text>
          {(searchQuery || statusFilter) && (
            <Text size="sm" c="dimmed">
              {t('filteredResults')}
            </Text>
          )}
        </Group>
      )}

      {/* Shipments Table */}
      {shipments.length === 0 ? (
        <Center py="xl">
          <Stack align="center" gap="md">
            <IconPackage size="3rem" color="gray" />
            <div style={{ textAlign: 'center' }}>
              <Text size="lg" fw={500} c="dimmed">
                {totalCount === 0 ? t('noShipmentsYet') : t('noShipmentsMatchFilters')}
              </Text>
              <Text size="sm" c="dimmed" mt="xs">
                {totalCount === 0
                  ? t('createFirstShipment')
                  : t('adjustFilters')}
              </Text>
              {totalCount === 0 && onCreateNew && (
                <Button mt="md" onClick={onCreateNew}>
                  {t('createShipment')}
                </Button>
              )}
            </div>
          </Stack>
        </Center>
      ) : (
        <>
          {isDesktop ? (
            <DataTable<Shipment>
              columns={columns}
              data={shipments}
              loading={isLoading}
              error={error && typeof error === 'object' && 'message' in error ? (error as { message?: string }).message : undefined}
              emptyMessage={totalCount === 0 ? t('noShipmentsFound') : t('noShipmentsMatchFilters')}
              pagination={paginationProps}
              rowActions={rowActions}
              onSortChange={handleSortChange}
              sortState={sortStateProps}
            />
          ) : (
            <>
              <Grid>
                {shipments.map((shipment: Shipment) => (
                  <Grid.Col key={shipment.id} span={12}>
                    <ShipmentCard shipment={shipment} onView={onViewShipment} onCancel={onCancelShipment} />
                  </Grid.Col>
                ))}
              </Grid>
              {totalPages > 1 && (
                <Center>
                  <Pagination value={page} total={totalPages} onChange={setPage} size="sm" />
                </Center>
              )}
            </>
          )}

          {/* QR Code Modal */}
          <Modal
            opened={qrModalOpen}
            onClose={handleCloseQRModal}
            title="Pickup QR Code"
            size="md"
            centered
          >
            {selectedShipmentForQR && (
              <QRCodeDisplay
                value={selectedShipmentForQR.pickupCode || ''}
                title={`Pickup Code for Shipment #${selectedShipmentForQR.id.slice(-8).toUpperCase()}`}
                description="Show this QR code to the carrier for package pickup"
                size={200}
                downloadFileName={`pickup-code-${selectedShipmentForQR.id.slice(-8)}`}
              />
            )}
          </Modal>
        </>
      )}
    </Stack>
  );
}
