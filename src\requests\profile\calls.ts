import { API_ENDPOINT, HTTP_CODE } from '../../data';
import { CLIENT_API } from '../../lib/axios';
import { handleApiError } from '../../utils/handle-backend-error';
import { QueryClient } from '@tanstack/react-query';
import { CacheInvalidationManager, QUERY_KEYS } from '../cache-invalidation';
import {
  GetProfileQueryProps,
  GetProfilesQueryProps,
  UpdateProfileQueryProps,
} from './types';

// Use centralized query keys
const queryKeys = {
  profile: QUERY_KEYS.profile,
  profiles: 'profiles', // Keep this for admin functionality
  updateProfile: 'update-profile',
};

/**
 * @description This function calls to get user profile data.
 * @returns user profile data
 */
const getProfileRequest = () => CLIENT_API.get(API_ENDPOINT.users.profile)
  .then((res) => res?.data)
  .catch((e) => {
    handleApiError(e);
    throw e.response?.data;
  });

/**
 * @description This function calls to get multiple profiles (for future admin functionality).
 * @param props
 * @returns list of profiles with pagination
 */
const getProfilesRequest = (props: GetProfilesQueryProps) => {
  const {
    pagination, filters, sort,
  } = props;

  // Build query parameters following the same pattern as your other project
  const queryParams: Record<string, string | undefined> = {
    // Pagination
    page: pagination?.page?.toString(),
    pageSize: pagination?.pageSize?.toString(),

    // Search
    search: filters?.search || undefined,

    // Sorting
    sortBy: typeof sort === 'string' ? sort.split(':')[0] : undefined,
    sortOrder: typeof sort === 'string' ? sort.split(':')[1] || 'asc' : undefined,

    // Filters using filter[key] pattern
    'filter[userType]': filters?.userType || undefined,
    'filter[status]': filters?.status || undefined,
    'filter[approved]': filters?.approved?.toString(),
    'filter[emailVerified]': filters?.emailVerified?.toString(),
    'filter[createdAtGte]': filters?.createdAtGte || undefined,
    'filter[createdAtLte]': filters?.createdAtLte || undefined,
    'filter[updatedAtGte]': filters?.updatedAtGte || undefined,
    'filter[updatedAtLte]': filters?.updatedAtLte || undefined,
    'filter[hasLocation]': filters?.hasLocation?.toString(),
    'filter[hasAccessPoints]': filters?.hasAccessPoints?.toString(),
  };

  // Remove undefined values
  const cleanParams = Object.entries(queryParams).reduce((acc, [key, value]) => {
    if (value !== undefined) {
      acc[key] = value;
    }
    return acc;
  }, {} as Record<string, string>);

  return CLIENT_API.get('/api/users/profiles', {
    params: cleanParams,
  })
    .then((res) => res?.data)
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

/**
 * @description This function calls to update user profile data.
 * @param props
 * @returns updated user profile data
 */
const updateProfileRequest = (props: UpdateProfileQueryProps) => {
  const { data } = props;
  return CLIENT_API.put(API_ENDPOINT.users.updateProfile, data)
    .then((res) => res?.data)
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

// Get user profile query function
export const getProfileQuery = (props: GetProfileQueryProps) => ({
  queryKey: [queryKeys.profile],
  queryFn: () => getProfileRequest(),
  refetchOnWindowFocus: props?.refetchOnWindowFocus ?? false,
  enabled: props?.enabled,
  retry: (failureCount: number, error: { code: HTTP_CODE }) => error?.code !== HTTP_CODE.UNAUTHORIZED,
});

// Get multiple profiles query function (for future admin functionality)
export const getProfilesQuery = (props: GetProfilesQueryProps) => ({
  queryKey: [
    queryKeys.profiles,
    props?.filters,
    props?.sort,
    props?.pagination,
  ],
  queryFn: () => getProfilesRequest(props),
  refetchOnWindowFocus: false,
  enabled: props?.enabled,
  retry: (failureCount: number, error: { code: HTTP_CODE }) => error?.code !== HTTP_CODE.UNAUTHORIZED,
});

// Update profile query function (for React Query useMutation)
export const updateProfileQuery = (props: UpdateProfileQueryProps) => ({
  queryKey: [queryKeys.updateProfile],
  queryFn: () => updateProfileRequest(props),
  refetchOnWindowFocus: false,
  enabled: props?.enabled,
});

// Update profile mutation function
export const updateProfileMutation = () => ({
  mutationKey: [queryKeys.updateProfile],
  mutationFn: (props: UpdateProfileQueryProps) => updateProfileRequest(props),
});

/**
 * Enhanced mutation functions with built-in cache invalidation
 */

// Update profile mutation with auto-invalidation
export const updateProfileMutationWithInvalidation = (queryClient: QueryClient) => {
  const cacheManager = new CacheInvalidationManager(queryClient);
  return {
    ...updateProfileMutation(),
    onSuccess: async () => {
      await cacheManager.invalidateAfterProfileUpdate();
    },
  };
};

// Aliases for backward compatibility
export const getUserProfileQuery = getProfileQuery;
export const updateUserProfileQuery = updateProfileQuery;
