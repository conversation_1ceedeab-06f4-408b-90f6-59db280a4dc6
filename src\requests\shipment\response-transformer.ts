import { z } from 'zod';

// Shipment status and size enums
export const SHIPMENT_STATUS = [
  'PENDING',
  'AWAITING_PICKUP',
  'IN_TRANSIT',
  'ARRIVED_AT_DESTINATION',
  'DELIVERED',
  'CANCELLED',
] as const;

export const SHIPMENT_SIZE = ['SMALL', 'MEDIUM', 'LARGE', 'EXTRA_LARGE'] as const;

export const CANCELLATION_REASON = [
  'USER_CANCELLED',
  'SYSTEM_EXPIRED',
  'ADMIN_CANCELLED',
] as const;

// Backend shipment schema - what comes from Prisma/database
export const shipmentBackendSchema = z.object({
  id: z.string().uuid(),
  customer_id: z.string().uuid(),
  origin_ao_id: z.string().uuid(),
  dest_ao_id: z.string().uuid(),
  assigned_car_operator_id: z.string().uuid().nullable().optional(),
  status: z.enum(SHIPMENT_STATUS),
  weight: z.number(),
  size: z.enum(SHIPMENT_SIZE),
  description: z.string(),
  pickup_code: z.string(),
  tracking_code: z.string().nullable().optional(),
  receiver_name: z.string(),
  receiver_phone: z.string(),
  estimated_delivery: z.string().nullable().optional(),
  picked_up_at: z.string().nullable().optional(),
  cancellation_reason: z.enum(CANCELLATION_REASON).nullable().optional(),
  cancelled_at: z.string().nullable().optional(),
  expires_at: z.string().nullable().optional(),
  created_at: z.string(),
  updated_at: z.string(),
});

// QR codes schema
export const qrCodesSchema = z.object({
  pickup_qr: z.object({
    value: z.string(),
    description: z.string(),
    contains: z.object({
      shipment_id: z.string().uuid(),
      pickup_code: z.string(),
    }),
  }).optional(),
  ao_qr: z.object({
    value: z.string(),
    description: z.string(),
    contains: z.object({
      shipment_id: z.string().uuid(),
      ao_id: z.string().uuid(),
    }),
  }).optional(),
});

// Shipment API response transformer - converts backend to frontend format
export const shipmentApiResponseSchema = (item: z.infer<typeof shipmentBackendSchema>) => ({
  id: item.id,
  customerId: item.customer_id,
  originAoId: item.origin_ao_id,
  destAoId: item.dest_ao_id,
  assignedCarOperatorId: item.assigned_car_operator_id ?? null,
  status: item.status,
  weight: item.weight,
  size: item.size,
  description: item.description,
  pickupCode: item.pickup_code,
  trackingCode: item.tracking_code ?? null,
  receiverName: item.receiver_name,
  receiverPhone: item.receiver_phone,
  estimatedDelivery: item.estimated_delivery ?? null,
  pickedUpAt: item.picked_up_at ?? null,
  cancellationReason: item.cancellation_reason ?? null,
  cancelledAt: item.cancelled_at ?? null,
  expiresAt: item.expires_at ?? null,
  createdAt: item.created_at,
  updatedAt: item.updated_at,
});

// Transformed shipment schema using the transformer function
export const shipmentSchema = shipmentBackendSchema.transform(shipmentApiResponseSchema);

// Multiple shipments schema
export const shipmentsBackendSchema = z.array(shipmentBackendSchema);

// Get shipment API response schema
export const getShipmentApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string().optional(),
  data: z.object({
    shipment: shipmentBackendSchema,
  }),
}).transform(({ success, message, data }) => ({
  success,
  message,
  data: {
    shipment: shipmentApiResponseSchema(data.shipment),
  },
}));

// Create shipment API response schema
export const createShipmentApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    shipment: shipmentBackendSchema,
    qr_codes: qrCodesSchema,
  }),
}).transform(({ success, message, data }) => ({
  success,
  message,
  data: {
    shipment: shipmentApiResponseSchema(data.shipment),
    qrCodes: data.qr_codes,
  },
}));

// Cancel shipment API response schema
export const cancelShipmentApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    shipment: shipmentBackendSchema.pick({
      id: true,
      status: true,
      cancellation_reason: true,
      cancelled_at: true,
      updated_at: true,
    }),
  }),
}).transform(({ success, message, data }) => ({
  success,
  message,
  data: {
    shipment: {
      id: data.shipment.id,
      status: data.shipment.status,
      cancellationReason: data.shipment.cancellation_reason,
      cancelledAt: data.shipment.cancelled_at,
      updatedAt: data.shipment.updated_at,
    },
  },
}));

// Scan shipment API response schema
export const scanShipmentApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    shipment: shipmentBackendSchema.pick({
      id: true,
      status: true,
      updated_at: true,
    }),
    photo_url: z.string(),
  }),
}).transform(({ success, message, data }) => ({
  success,
  message,
  data: {
    shipment: {
      id: data.shipment.id,
      status: data.shipment.status,
      updatedAt: data.shipment.updated_at,
    },
    photoUrl: data.photo_url,
  },
}));

// Deliver shipment API response schema
export const deliverShipmentApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    shipment: shipmentBackendSchema.pick({
      id: true,
      status: true,
      updated_at: true,
      receiver_name: true,
    }),
    photo_url: z.string(),
  }),
}).transform(({ success, message, data }) => ({
  success,
  message,
  data: {
    shipment: {
      id: data.shipment.id,
      status: data.shipment.status,
      updatedAt: data.shipment.updated_at,
      receiverName: data.shipment.receiver_name,
    },
    photoUrl: data.photo_url,
  },
}));

// List shipments API response schema
export const listShipmentsApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string().optional(),
  data: z.object({
    shipments: shipmentsBackendSchema,
    pagination: z.object({
      page: z.number(),
      limit: z.number(),
      total: z.number(),
      totalPages: z.number(),
      hasNext: z.boolean(),
      hasPrev: z.boolean(),
    }).optional(),
  }),
}).transform(({ success, message, data }) => ({
  success,
  message,
  data: {
    shipments: data.shipments.map(shipmentApiResponseSchema),
    pagination: data.pagination,
  },
}));

// My shipments API response schema - specific for /shipments/my endpoint
export const myShipmentsApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string().optional(),
  data: z.object({
    shipments: shipmentsBackendSchema,
    pagination: z.object({
      page: z.number(),
      limit: z.number(),
      total: z.number(),
      totalPages: z.number(),
      hasNext: z.boolean(),
      hasPrev: z.boolean(),
    }).optional(),
  }),
}).transform(({ success, message, data }) => ({
  success,
  message: message ?? 'My shipments retrieved successfully',
  data: {
    shipments: data.shipments.map(shipmentApiResponseSchema),
    pagination: data.pagination,
  },
}));

// Export schemas collection
export const shipmentResponseSchemas = {
  shipmentBackend: shipmentBackendSchema,
  shipment: shipmentSchema,
  shipmentApiResponse: shipmentApiResponseSchema,
  getShipmentApiResponse: getShipmentApiResponseSchema,
  createShipmentApiResponse: createShipmentApiResponseSchema,
  cancelShipmentApiResponse: cancelShipmentApiResponseSchema,
  scanShipmentApiResponse: scanShipmentApiResponseSchema,
  deliverShipmentApiResponse: deliverShipmentApiResponseSchema,
  listShipmentsApiResponse: listShipmentsApiResponseSchema,
  myShipmentsApiResponse: myShipmentsApiResponseSchema,
  SHIPMENT_STATUS,
  SHIPMENT_SIZE,
  CANCELLATION_REASON,
};

export default shipmentResponseSchemas;
