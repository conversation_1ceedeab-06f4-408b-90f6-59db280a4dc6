/* eslint-disable complexity */
/* eslint-disable react/require-default-props */
import React from 'react';
import {
  Grid,
  Stack,
  Alert,
  Text,
  Group,
  Badge,
  Divider,
} from '@mantine/core';
import {
  IconRoute,
  IconInfoCircle,
} from '@tabler/icons-react';
import useTranslation from 'next-translate/useTranslation';
import { useRouter } from 'next/router';
import { useQuery } from '@tanstack/react-query';
import { AccessPoint, getAccessPointQuery } from '../../../requests';
import { Shipment } from '../../../requests/shipment';
import EnhancedAccessPointDisplay from './EnhancedAccessPointDisplay';

interface EnhancedShipmentAccessPointsInfoProps {
  shipment: Shipment;
  variant?: 'default' | 'compact' | 'detailed';
  showRouteInfo?: boolean;
  showInstructions?: boolean;
}

export default function EnhancedShipmentAccessPointsInfo({
  shipment,
  variant = 'default',
  showRouteInfo = true,
  showInstructions = true,
}: EnhancedShipmentAccessPointsInfoProps) {
  const { t } = useTranslation('shipments');
  const router = useRouter();
  const isRTL = router.locale === 'ar';

  // Fetch origin access point
  const {
    data: originData,
    isLoading: originLoading,
    error: originError,
  } = useQuery(
    getAccessPointQuery({
      id: shipment.originAoId,
      enabled: !!shipment.originAoId,
    }),
  );

  // Fetch destination access point
  const {
    data: destData,
    isLoading: destLoading,
    error: destError,
  } = useQuery(
    getAccessPointQuery({
      id: shipment.destAoId,
      enabled: !!shipment.destAoId,
    }),
  );

  // Extract access point data
  const originAccessPoint: AccessPoint | null = originData?.data?.accessOperator || null;
  const destAccessPoint: AccessPoint | null = destData?.data?.accessOperator || null;

  return (
    <Stack gap="md">
      {/* Route Header */}
      {showRouteInfo && (
        <Group gap="xs" justify="center">
          <IconRoute size="1.2rem" color="blue" />
          <Text fw={600} size="md" c="blue">
            {t('shipmentRoute')}
          </Text>
          <Badge variant="light" color="blue" size="sm">
            {shipment.trackingCode || `#${shipment.id.slice(-8).toUpperCase()}`}
          </Badge>
        </Group>
      )}

      {/* Access Points Grid */}
      <Grid gutter="md">
        <Grid.Col span={{ base: 12, md: 6 }}>
          <EnhancedAccessPointDisplay
            accessPoint={originAccessPoint}
            isLoading={originLoading}
            error={originError}
            type="origin"
            variant={variant}
          />
        </Grid.Col>
        <Grid.Col span={{ base: 12, md: 6 }}>
          <EnhancedAccessPointDisplay
            accessPoint={destAccessPoint}
            isLoading={destLoading}
            error={destError}
            type="destination"
            variant={variant}
          />
        </Grid.Col>
      </Grid>

      {/* Route Arrow (Visual Indicator) */}
      {variant !== 'compact' && (originAccessPoint && destAccessPoint) && (
      <Group justify="center" my="xs">
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          padding: '8px 16px',
          backgroundColor: '#f8f9fa',
          borderRadius: '20px',
          border: '1px solid #e9ecef',
          direction: isRTL ? 'rtl' : 'ltr',
        }}
        >
          {isRTL ? (
            <>
              <Badge size="xs" color="green" variant="filled">{t('from')}</Badge>
              <Text size="xs" c="dimmed" fw={500}>
                {originAccessPoint.businessName || originAccessPoint.name}
              </Text>
              <Text size="lg" c="blue">←</Text>
              <Badge size="xs" color="red" variant="filled">{t('to')}</Badge>
              <Text size="xs" c="dimmed" fw={500}>
                {destAccessPoint.businessName || destAccessPoint.name}
              </Text>
            </>
          ) : (
            <>
              <Badge size="xs" color="green" variant="filled">{t('from')}</Badge>
              <Text size="xs" c="dimmed" fw={500}>
                {originAccessPoint.businessName || originAccessPoint.name}
              </Text>
              <Text size="lg" c="blue">→</Text>
              <Badge size="xs" color="red" variant="filled">{t('to')}</Badge>
              <Text size="xs" c="dimmed" fw={500}>
                {destAccessPoint.businessName || destAccessPoint.name}
              </Text>
            </>
          )}
        </div>
      </Group>
      )}

      {/* Instructions */}
      {showInstructions && (originAccessPoint && destAccessPoint) && (
        <>
          <Divider />
          <Alert color="blue" variant="light" icon={<IconInfoCircle size="1rem" />}>
            <Stack gap="xs">
              <Text size="sm" fw={600}>
                {t('importantInstructions')}
              </Text>
              <Text size="xs">
                <strong>
                  1.
                  {t('dropoffInstruction', { location: originAccessPoint.businessName || originAccessPoint.name })}
                </strong>
              </Text>
              <Text size="xs">
                <strong>
                  2.
                  {t('pickupInstruction', { location: destAccessPoint.businessName || destAccessPoint.name })}
                </strong>
              </Text>
              <Text size="xs">
                <strong>
                  3.
                  {t('contactInstruction')}
                </strong>
              </Text>
            </Stack>
          </Alert>
        </>
      )}

      {/* Loading or Error State for Both */}
      {(originLoading || destLoading) && !(originAccessPoint || destAccessPoint) && (
        <Alert color="blue" variant="light">
          <Text size="sm">Loading access point information...</Text>
        </Alert>
      )}

      {(originError && destError) && (
        <Alert color="red" variant="light">
          <Text size="sm">Unable to load access point information. Please try again later.</Text>
        </Alert>
      )}
    </Stack>
  );
}
