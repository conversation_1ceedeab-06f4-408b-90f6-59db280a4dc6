/* eslint-disable sonarjs/cognitive-complexity */
/* eslint-disable complexity */
import { NextApiRequest } from 'next';
import dayjs from 'dayjs';
import { DATE_FORMAT } from '../../data/constants';

const toSnakeCase = (str: string) => str.replace(/([A-Z])/g, '_$1').toLowerCase();

type RelationshipConfig = {
  relationField: string;
  entityField: string;
  valueField: string;
};

const buildShipmentFilter = (filterParams: Record<string, string | string[]>) => {
  const relationships: Record<string, RelationshipConfig> = {
    // Add relationships if needed in the future
    // customer: {
    //   relationField: 'Customer',
    //   entityField: 'user',
    //   valueField: 'id',
    // },
    // originAo: {
    //   relationField: 'OriginAo',
    //   entityField: 'access_operator',
    //   valueField: 'id',
    // },
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const filterConditions: Record<string, any>[] = [];

  Object.entries(filterParams).forEach(([key, value]) => {
    if (!value || value === '') return;

    if (key.endsWith('Gte')) {
      const fieldName = toSnakeCase(key.replace('Gte', ''));
      if (key.startsWith('weight')) {
        filterConditions.push({ [fieldName]: { gte: parseFloat(value as string) } });
      } else {
        filterConditions.push({ [fieldName]: { gte: value } });
      }
      return;
    }

    if (key.endsWith('Lte')) {
      const fieldName = toSnakeCase(key.replace('Lte', ''));
      if (key.startsWith('weight')) {
        filterConditions.push({ [fieldName]: { lte: parseFloat(value as string) } });
      } else {
        filterConditions.push({ [fieldName]: { lte: value } });
      }
      return;
    }

    if (key === 'hasTrackingCode' && typeof value === 'string') {
      if (value === 'true') {
        filterConditions.push({ tracking_code: { not: null } });
      } else if (value === 'false') {
        filterConditions.push({ tracking_code: null });
      }
      return;
    }

    if (key === 'hasPickupCode' && typeof value === 'string') {
      if (value === 'true') {
        filterConditions.push({ pickup_code: { not: null } });
      } else if (value === 'false') {
        filterConditions.push({ pickup_code: null });
      }
      return;
    }

    if (key === 'isExpired' && typeof value === 'string') {
      const now = dayjs().format(DATE_FORMAT);
      if (value === 'true') {
        filterConditions.push({
          AND: [
            { expires_at: { not: null } },
            { expires_at: { lt: now } },
            { status: { not: 'DELIVERED' } },
            { status: { not: 'CANCELLED' } },
          ],
        });
      } else if (value === 'false') {
        filterConditions.push({
          OR: [
            { expires_at: null },
            { expires_at: { gte: now } },
            { status: 'DELIVERED' },
            { status: 'CANCELLED' },
          ],
        });
      }
      return;
    }

    if (key === 'search' && typeof value === 'string') {
      filterConditions.push({
        OR: [
          { description: { contains: value, mode: 'insensitive' } },
          { receiver_name: { contains: value, mode: 'insensitive' } },
          { receiver_phone: { contains: value, mode: 'insensitive' } },
          { pickup_code: { contains: value, mode: 'insensitive' } },
          { tracking_code: { contains: value, mode: 'insensitive' } },
        ],
      });
      return;
    }

    if (relationships[key]) {
      const config = relationships[key];
      const values = Array.isArray(value) ? value : [value];

      values.forEach((val) => {
        filterConditions.push({
          [config.relationField]: {
            some: {
              [config.entityField]: {
                [config.valueField]: val,
              },
            },
          },
        });
      });
      return;
    }

    // Handle specific field mappings
    const fieldMappings: Record<string, string> = {
      customerId: 'customer_id',
      originAoId: 'origin_ao_id',
      destAoId: 'dest_ao_id',
      assignedCarOperatorId: 'assigned_car_operator_id',
      cancellationReason: 'cancellation_reason',
    };

    const mappedField = fieldMappings[key] || toSnakeCase(key);
    filterConditions.push({ [mappedField]: value });
  });

  return filterConditions.length ? { AND: filterConditions } : undefined;
};

const getFilterParams = (query: Record<string, string | string[]>) => Object.entries(query).reduce((params: Record<string, string | string[]>, [key, value]) => {
  if (!key.startsWith('filter[') || !value || value === '') {
    return params;
  }

  const filterKey = key.match(/\[(.*?)\]/)?.[1];
  if (!filterKey) {
    return params;
  }

  return { ...params, [filterKey]: value };
}, {});

const getPaginationParams = (query: Record<string, string | string[]>) => {
  const commonKeys = ['search', 'page', 'pageSize', 'limit', 'sortBy', 'sortOrder'];

  return commonKeys.reduce((params, key) => {
    const value = query[key];
    if (!value || value === '') {
      return params;
    }

    // Convert page/pageSize/limit to consistent format
    if (key === 'page' || key === 'pageSize' || key === 'limit') {
      const numValue = parseInt(Array.isArray(value) ? value[0] : value, 10);
      if (key === 'page') {
        return { ...params, page: numValue };
      }
      if (key === 'pageSize' || key === 'limit') {
        return { ...params, pageSize: numValue };
      }
    }

    return {
      ...params,
      [key]: Array.isArray(value) ? value[0] : value,
    };
  }, {});
};

const getRestParams = (query: Record<string, string | string[]>) => {
  const skipKeys = [
    'search',
    'page',
    'pageSize',
    'limit',
    'sortBy',
    'sortOrder',
    // Filter-related parameters that should be processed by buildShipmentFilter
    'status',
    'originAoId',
    'destAoId',
    'customerId',
    'assignedCarOperatorId',
    'cancellationReason',
  ];

  return Object.entries(query).reduce((params, [key, value]) => {
    if (!value || value === ''
        || key.startsWith('filter[')
        || skipKeys.includes(key)) {
      return params;
    }

    return {
      ...params,
      [key]: Array.isArray(value) ? value[0] : value,
    };
  }, {});
};

// Convert page/pageSize to Prisma's skip/take
const convertToPrismaPagination = (params: Record<string, unknown>) => {
  const { page, pageSize, ...rest } = params;

  if (typeof page === 'number' && typeof pageSize === 'number') {
    return {
      ...rest,
      skip: (page - 1) * pageSize,
      take: pageSize,
    };
  }

  return params;
};

const returnParams = (req: NextApiRequest) => {
  const query = req.query as Record<string, string | string[]>;

  // Get filter params from both filter[key] format and direct query parameters
  const filterParams = getFilterParams(query);

  // Also include direct filter-related parameters
  const directFilterKeys = ['status', 'originAoId', 'destAoId', 'customerId', 'assignedCarOperatorId', 'cancellationReason'];
  directFilterKeys.forEach((key) => {
    const value = query[key];
    if (value && value !== '') {
      filterParams[key] = Array.isArray(value) ? value[0] : value;
    }
  });

  const filter = buildShipmentFilter(filterParams);
  const paginationParams = getPaginationParams(query);
  const restParams = getRestParams(query);

  const allParams = {
    ...paginationParams,
    ...restParams,
    ...(filter ? { filter: JSON.stringify(filter) } : {}),
  };

  // Convert to Prisma pagination format
  return convertToPrismaPagination(allParams);
};

// Shipment sort keys mapping
export const shipmentSortKeysMapping = new Map<string, string>([
  ['id', 'id'],
  ['customerId', 'customer_id'],
  ['originAoId', 'origin_ao_id'],
  ['destAoId', 'dest_ao_id'],
  ['assignedCarOperatorId', 'assigned_car_operator_id'],
  ['status', 'status'],
  ['weight', 'weight'],
  ['size', 'size'],
  ['description', 'description'],
  ['receiverName', 'receiver_name'],
  ['receiverPhone', 'receiver_phone'],
  ['pickupCode', 'pickup_code'],
  ['trackingCode', 'tracking_code'],
  ['estimatedDelivery', 'estimated_delivery'],
  ['pickedUpAt', 'picked_up_at'],
  ['cancellationReason', 'cancellation_reason'],
  ['cancelledAt', 'cancelled_at'],
  ['expiresAt', 'expires_at'],
  ['createdAt', 'created_at'],
  ['updatedAt', 'updated_at'],
]);

// Export only the functions that are actually used
export const returnShipmentParams = returnParams;
