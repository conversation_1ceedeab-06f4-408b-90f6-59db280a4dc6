import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import useTranslation from 'next-translate/useTranslation';
import {
  TextInput,
  Button,
  Paper,
  Title,
  Text,
  Container,
  Group,
  Anchor,
  Stack,
  LoadingOverlay,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { z } from 'zod';
import { forgotPasswordRequest } from '../../src/requests/auth/calls';
import { notifications } from '@mantine/notifications';
import { forgotPasswordSchema } from '../../src/requests';

type ForgotPasswordFormValues = z.infer<typeof forgotPasswordSchema>;

export default function ForgotPasswordPage() {
  const router = useRouter();
  const { error } = router.query;

  const [isLoading, setIsLoading] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const { t } = useTranslation('auth');
  const { t: tCommon } = useTranslation('common');

  const form = useForm<ForgotPasswordFormValues>({
    validate: (values) => {
      const parsed = forgotPasswordSchema.safeParse(values);
      const errors: Partial<Record<keyof ForgotPasswordFormValues, string | null>> = {};

      if (!parsed.success) {
        parsed.error.errors.forEach((issue) => {
          const field = issue.path[0] as keyof ForgotPasswordFormValues;
          errors[field] = issue.message;
        });
      }

      return errors as Record<keyof ForgotPasswordFormValues, string | null>;
    },
    initialValues: {
      email: '',
    },
  });

  const handleSubmit = async (values: ForgotPasswordFormValues) => {
    setIsLoading(true);
    setSubmitted(false);
    try {
      const response = await forgotPasswordRequest({ data: values });
      if (response.success) {
        setSubmitted(true); // To show a confirmation message or different UI state
        form.reset();
      } else {
        notifications.show({
          title: tCommon('error'),
          message: response.message || tCommon('somethingWentWrong'),
          color: 'red',
        });
      }
    } catch (err) {
      // error shape is already handled in calls.ts, just show generic message
      notifications.show({
        title: tCommon('error'),
        message: tCommon('somethingWentWrong'),
        color: 'red',
      });
    }
    setIsLoading(false);
  };

  return (
    <Container size={420} my={40}>
      <Title ta="center">
        {t('forgotPasswordTitle')}
      </Title>
      <Text c="dimmed" size="sm" ta="center" mt={5}>
        {t('forgotPasswordSubtitle')}
      </Text>

      <Paper withBorder shadow="md" p={30} mt={30} radius="md">
        <LoadingOverlay visible={isLoading} />
        {error === 'invalid_token' && (
          <Text c="red" ta="center" mb="md">
            {t('invalidOrExpiredToken')}
          </Text>
        )}
        {error === 'invalid_link' && (
          <Text c="red" ta="center" mb="md">
            {t('invalidResetLink')}
          </Text>
        )}
        {!submitted ? (
          <form onSubmit={form.onSubmit(handleSubmit)}>
            <Stack>
              <TextInput
                required
                label={t('emailAddress')}
                placeholder={t('enterEmail')}
                {...form.getInputProps('email')}
              />
              <Button type="submit" fullWidth mt="xl" loading={isLoading}>
                {t('sendResetLink')}
              </Button>
            </Stack>
          </form>
        ) : (
          <div>
            <div
              style={{
                border: '1px solid #bbf7d0',
                borderRadius: '0.375rem',
                padding: '1rem',
                marginBottom: '1rem',
              }}
            >
              <Text c="green" fw={600} mb="xs">
                {t('resetLinkSent')}
              </Text>
              <Text size="sm" c="green" mb="xs">
                {t('resetLinkSentDescription', { email: form.values.email })}
              </Text>
              <Text size="sm" c="green">
                ⏰
                {' '}
                {t('resetLinkExpiration')}
              </Text>
            </div>
            <Group justify="center">
              <Link href="/auth/login" passHref legacyBehavior>
                <Anchor component="a" c="dimmed" size="sm">
                  {t('backToSignIn')}
                </Anchor>
              </Link>
            </Group>
          </div>
        )}
        {submitted === false && (
          <Group justify="center" mt="lg">
            <Link href="/auth/login" passHref legacyBehavior>
              <Anchor component="a" c="dimmed" size="sm">
                {t('backToSignIn')}
              </Anchor>
            </Link>
          </Group>
        )}
      </Paper>
    </Container>
  );
}
