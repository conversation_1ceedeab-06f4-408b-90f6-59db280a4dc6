/* eslint-disable no-console */
/* eslint-disable max-lines */
/* eslint-disable no-nested-ternary */
/* eslint-disable react/require-default-props */
import { useState } from 'react';
import {
  Modal,
  Stack,
  Text,
  Button,
  Stepper,
  Paper,
  TextInput,
  Textarea,
  Alert,
  Loader,
  Image,
  Tabs,
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import {
  IconQrcode,
  IconCamera,
  IconCheck,
  IconAlertTriangle,
  IconTruck,
  IconKeyboard,
} from '@tabler/icons-react';
import { Shipment } from '../../../requests/shipment';
import { useIsClient } from '../../../hooks/useIsClient';
import QRScanner from '../../common/QRScanner';
import ErrorBoundary from '../../common/ErrorBoundary';
import { useMutation } from '@tanstack/react-query';
import { useScanShipmentMutation } from '../../../requests/hooks/enhanced-mutations';
import useTranslation from 'next-translate/useTranslation';

interface COPickupModalProps {
  opened: boolean;
  onClose: () => void;
  shipment: Shipment | null;
  onSuccess?: () => void;
}

export default function COPickupModal({
  opened,
  onClose,
  shipment,
  onSuccess,
}: COPickupModalProps) {
  const isClient = useIsClient();

  // State management
  const [activeStep, setActiveStep] = useState(0);
  const [scannedQR, setScannedQR] = useState('');
  const [photoPreview, setPhotoPreview] = useState<string | null>(null);
  const [photoUrl, setPhotoUrl] = useState<string | null>(null);
  const [notes, setNotes] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [scanMethod, setScanMethod] = useState<'camera' | 'manual'>('camera');
  const { t } = useTranslation('shipment');

  const handleClose = () => {
    setActiveStep(0);
    setScannedQR('');
    setPhotoPreview(null);
    setPhotoUrl(null);
    setNotes('');
    setError(null);
    setScanMethod('camera');
    onClose();
  };

  // Upload photo mutation
  const uploadPhotoMutation = useMutation({
    mutationFn: async (data: { photoBase64: string; geo_latitude?: number; geo_longitude?: number }) => {
      const response = await fetch('/api/uploads/photo', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          photoBase64: data.photoBase64,
          folder: 'shipment-scans',
          geo_latitude: data.geo_latitude || null,
          geo_longitude: data.geo_longitude || null,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to upload photo');
      }

      return response.json();
    },
    onError: (uploadError: Error) => {
      notifications.show({
        title: 'Upload Failed',
        message: uploadError.message || 'Failed to upload photo. Please try again.',
        color: 'red',
        icon: <IconAlertTriangle size="1rem" />,
        autoClose: 5000,
      });
    },
  });

  // Scan shipment mutation (PICKUP action) - using enhanced hook with auto-invalidation
  const scanShipmentMutationInstance = useScanShipmentMutation({
    successMessage: 'Package has been successfully picked up and is now in transit.',
    showNotifications: false,
    onSuccess: () => {
      setActiveStep(2);
      setError(null);
      notifications.show({
        title: 'Pickup Successful',
        message: 'Package has been successfully picked up and is now in transit.',
        color: 'green',
        icon: <IconCheck size="1rem" />,
        autoClose: 5000,
      });

      if (onSuccess) {
        setTimeout(() => {
          onSuccess();
          handleClose();
        }, 2000);
      }
    },
  });

  const handleQRScan = (value: string) => {
    setScannedQR(value);
    setError(null);
  };

  const handlePhotoChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = async (e) => {
        const base64Data = e.target?.result as string;
        setPhotoPreview(base64Data);

        // Upload photo immediately when user selects it (no geolocation)
        try {
          const photoResponse = await uploadPhotoMutation.mutateAsync({
            photoBase64: base64Data,
          });
          setPhotoUrl(photoResponse.data.photo_url);
          setError(null);
        } catch (er) {
          console.error('Error uploading photo:', er);
          setError('Failed to upload photo. Please try again.');
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handlePickupScan = async () => {
    if (!photoUrl || !scannedQR) {
      setError('Please complete all required fields');
      return;
    }

    try {
      // Call scan API with PICKUP action
      await scanShipmentMutationInstance.mutateAsync({
        data: {
          shipmentId: shipment?.id as string,
          qrValue: scannedQR,
          photoUrl,
          action: 'PICKUP',
          notes: notes || 'Package picked up by Car Operator',
        },
      });
    } catch (e) {
      // Error is already handled by the mutation's onError callback
      // Just log it for debugging purposes
      console.error('Error in pickup scan:', e);

      // Don't re-throw the error as it's already handled by React Query
      // The onError callback will show the appropriate notification and set the error state
    }
  };

  if (!isClient) {
    return null;
  }

  return (
    <Modal
      opened={opened}
      onClose={handleClose}
      title="Pick Up Package"
      size="lg"
      centered
    >
      <ErrorBoundary>
        <Stack gap="md">
          {error && (
          <Alert
            icon={<IconAlertTriangle size="1rem" />}
            color="red"
            variant="light"
            onClose={() => setError(null)}
            withCloseButton
          >
            {error}
          </Alert>
          )}

          <Stepper active={activeStep}>
            <Stepper.Step
              label="Scan QR Code"
              description="Scan the AO's QR code on the package"
              icon={<IconQrcode size="1.1rem" />}
            >
              <Paper p="md" withBorder>
                <Stack gap="md" align="center">
                  <IconTruck size="3rem" color="var(--mantine-color-blue-6)" />
                  <Text ta="center">
                    Scan the QR code attached to shipment
                    {' '}
                    <strong>{shipment?.trackingCode || shipment?.id?.slice(-8)}</strong>
                  </Text>
                  <Text size="sm" c="dimmed" ta="center">
                    This QR code was generated by the Access Operator when the package was dropped off
                  </Text>

                  <Alert color="blue" variant="light" mb="md">
                    <Text size="sm">
                      <strong>Note:</strong>
                      {' '}
                      Scan the QR code that was attached to the package by the origin Access Operator.
                    </Text>
                  </Alert>

                  <Tabs value={scanMethod} onChange={(value) => setScanMethod(value as 'camera' | 'manual')}>
                    <Tabs.List>
                      <Tabs.Tab value="camera" leftSection={<IconCamera size="0.8rem" />}>
                        Camera Scan
                      </Tabs.Tab>
                      <Tabs.Tab value="manual" leftSection={<IconKeyboard size="0.8rem" />}>
                        Manual Entry
                      </Tabs.Tab>
                    </Tabs.List>

                    <Tabs.Panel value="camera" pt="md">
                      <QRScanner
                        onScan={handleQRScan}
                        onError={(e) => setError(e)}
                        isActive={scanMethod === 'camera' && opened && activeStep === 0}
                        expectedFormat="AO_"
                        title="Scan Package QR Code"
                        description="Point your camera at the QR code attached to the package"
                        debug
                      />
                      {scannedQR && (
                      <Alert
                        icon={<IconCheck size="1rem" />}
                        color="green"
                        variant="light"
                        mt="md"
                      >
                        <Text size="sm">
                          Successfully scanned:
                          {' '}
                          <strong>{scannedQR}</strong>
                        </Text>
                      </Alert>
                      )}
                    </Tabs.Panel>

                    <Tabs.Panel value="manual" pt="md">
                      <TextInput
                        label="Enter QR Code"
                        placeholder="Enter the QR code value (e.g., AO_x7k9m2p1)"
                        value={scannedQR}
                        onChange={(event) => setScannedQR(event.currentTarget.value)}
                        required
                        style={{ width: '100%' }}
                        description="This QR code should be attached to the package by the Access Operator"
                      />
                    </Tabs.Panel>
                  </Tabs>

                  <Button
                    onClick={() => setActiveStep(1)}
                    disabled={!scannedQR}
                    leftSection={<IconCamera size="1rem" />}
                  >
                    Next: Take Photo
                  </Button>
                </Stack>
              </Paper>
            </Stepper.Step>

            <Stepper.Step
              label="Take Photo"
              description="Photo of package pickup"
              icon={<IconCamera size="1.1rem" />}
            >
              <Paper p="md" withBorder>
                <Stack gap="md">
                  <Text size="sm" c="dimmed">
                    Take a photo of yourself picking up the package for documentation
                  </Text>

                  <input
                    type="file"
                    accept="image/*"
                    capture="environment"
                    onChange={handlePhotoChange}
                    style={{ display: 'none' }}
                    id="pickup-photo-input"
                  />
                  <Button
                    component="label"
                    htmlFor="pickup-photo-input"
                    leftSection={<IconCamera size="1rem" />}
                    variant="light"
                    fullWidth
                    loading={uploadPhotoMutation.isPending}
                    disabled={uploadPhotoMutation.isPending}
                  >
                    {uploadPhotoMutation.isPending ? 'Uploading Photo...' : 'Take Pickup Photo'}
                  </Button>

                  {photoPreview && (
                  <div>
                    <Text size="sm" c="dimmed" mb="xs">Pickup Photo:</Text>
                    <Image
                      src={photoPreview}
                      alt="Pickup photo"
                      height={200}
                      fit="contain"
                      radius="md"
                    />
                    {photoUrl ? (
                      <Alert
                        icon={<IconCheck size="1rem" />}
                        color="green"
                        variant="light"
                        mt="md"
                      >
                        Photo uploaded successfully!
                      </Alert>
                    ) : uploadPhotoMutation.isPending ? (
                      <Alert
                        icon={<Loader size="1rem" />}
                        color="blue"
                        variant="light"
                        mt="md"
                      >
                        Uploading photo...
                      </Alert>
                    ) : null}
                  </div>
                  )}

                  <Textarea
                    label="Notes (Optional)"
                    placeholder="Add any notes about the pickup..."
                    value={notes}
                    onChange={(event) => setNotes(event.currentTarget.value)}
                    rows={3}
                  />

                  <Button
                    onClick={handlePickupScan}
                    loading={scanShipmentMutationInstance.isPending}
                    leftSection={<IconCheck size="1rem" />}
                    color="green"
                    disabled={!scannedQR || !photoUrl}
                    fullWidth
                  >
                    {t('completePickup')}
                  </Button>
                </Stack>
              </Paper>
            </Stepper.Step>

            <Stepper.Completed>
              <Paper p="md" withBorder>
                <Stack gap="md" align="center">
                  <IconCheck size="3rem" color="var(--mantine-color-green-6)" />
                  <Text ta="center" size="lg" fw={600}>
                    {t('packageSuccessfullyPickedUp')}
                  </Text>
                  <Text ta="center" c="dimmed">
                    {t('shipmentStatusUpdatedInTransit')}
                  </Text>
                </Stack>
              </Paper>
            </Stepper.Completed>
          </Stepper>
        </Stack>
      </ErrorBoundary>
    </Modal>
  );
}
