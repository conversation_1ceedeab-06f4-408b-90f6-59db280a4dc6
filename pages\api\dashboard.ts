import type { NextApiRequest, NextApiResponse } from 'next'; // Dashboard API route
import { getJwt } from '../../src/utils';
import { BACKEND_API } from '../../src/lib/axios';
import { API_ENDPOINT, HTTP_CODE } from '../../src/data';
import { DashboardResponseSchema } from '../../src/requests/dashboard/respons-transformer';
import createApiError from '../../src/utils/create-api-error';

const apiMethods = {
  GET: 'GET',
} as const;

async function handleGet(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Obtain JWT from NextAuth session (if available)
    const { token } = await getJwt(req);

    const response = await BACKEND_API.get(API_ENDPOINT.dashboard.base, {
      headers: {
        Authorization: token,
      },
    });

    // Validate & transform backend response (snake_case -> camelCase)
    const validatedResponse = DashboardResponseSchema.parse(response.data);
    return res.status(HTTP_CODE.SUCCESS).json(validatedResponse);
  } catch (e) {
    const error = createApiError({ error: e });
    return res.status(error.code).json(error);
  }
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === apiMethods.GET) {
    return handleGet(req, res);
  }

  res.setHeader('Allow', ['GET']);
  return res.status(HTTP_CODE.METHOD_NOT_ALLOWED).json({
    success: false,
    message: `Method ${req.method} not allowed`,
  });
}
