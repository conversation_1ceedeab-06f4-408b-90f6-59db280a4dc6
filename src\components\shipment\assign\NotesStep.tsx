/* eslint-disable linebreak-style */
import {
  Stack, Textarea, Group, Button,
} from '@mantine/core';
import { IconCheck } from '@tabler/icons-react';

interface NotesStepProps {
  notes: string;
  setNotes: (val: string) => void;
  onBack: () => void;
  onComplete: () => void;
  loading: boolean;
}

export default function NotesStep({
  notes,
  setNotes,
  onBack,
  onComplete,
  loading,
}: NotesStepProps) {
  return (
    <Stack gap="md">
      <Textarea
        label="Notes"
        placeholder="Add any notes about the package..."
        value={notes}
        onChange={(event) => setNotes(event.currentTarget.value)}
        rows={3}
      />

      <Group justify="flex-end" mt="md">
        <Button variant="light" onClick={onBack}>
          Back
        </Button>
        <Button
          onClick={onComplete}
          loading={loading}
          leftSection={<IconCheck size="1rem" />}
          color="green"
        >
          Complete Process
        </Button>
      </Group>
    </Stack>
  );
}
