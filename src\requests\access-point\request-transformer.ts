/* eslint-disable sonarjs/no-duplicate-string */
import { z } from 'zod';

// Common validation patterns
const VALIDATION = {
  BUSINESS_NAME: {
    MIN_LENGTH: 2,
    MESSAGE: 'Business name must be at least 2 characters',
  },
  ADDRESS: {
    MIN_LENGTH: 5,
    MESSAGE: 'Address must be at least 5 characters',
  },
};

// Create access point schema - frontend camelCase
export const createAccessPointApiRequestSchema = z.object({
  businessName: z.string().min(VALIDATION.BUSINESS_NAME.MIN_LENGTH, {
    message: VALIDATION.BUSINESS_NAME.MESSAGE,
  }),
  address: z.string().min(VALIDATION.ADDRESS.MIN_LENGTH, {
    message: VALIDATION.ADDRESS.MESSAGE,
  }),
  geoLatitude: z.number()
    .min(-90, 'Latitude must be between -90 and 90')
    .max(90, 'Latitude must be between -90 and 90'),
  geoLongitude: z.number()
    .min(-180, 'Longitude must be between -180 and 180')
    .max(180, 'Longitude must be between -180 and 180'),
});

// Update access point schema - frontend camelCase
export const updateAccessPointApiRequestSchema = z.object({
  businessName: z.string().min(VALIDATION.BUSINESS_NAME.MIN_LENGTH, {
    message: VALIDATION.BUSINESS_NAME.MESSAGE,
  }).optional(),
  address: z.string().min(VALIDATION.ADDRESS.MIN_LENGTH, {
    message: VALIDATION.ADDRESS.MESSAGE,
  }).optional(),
  geoLatitude: z.number()
    .min(-90, 'Latitude must be between -90 and 90')
    .max(90, 'Latitude must be between -90 and 90')
    .optional(),
  geoLongitude: z.number()
    .min(-180, 'Longitude must be between -180 and 180')
    .max(180, 'Longitude must be between -180 and 180')
    .optional(),
}).refine(
  (data) =>
  // If any field is provided, at least one field must be present
    // eslint-disable-next-line implicit-arrow-linebreak
    Object.values(data).some((value) => value !== undefined),
  {
    message: 'At least one field must be provided for update',
  },
);

// Backend request schemas - transform camelCase to snake_case
export const createAccessPointBackendRequestSchema = createAccessPointApiRequestSchema.transform((data) => ({
  business_name: data.businessName,
  address: data.address,
  geo_latitude: data.geoLatitude,
  geo_longitude: data.geoLongitude,
}));

export const updateAccessPointBackendRequestSchema = updateAccessPointApiRequestSchema.transform((data) => ({
  business_name: data.businessName,
  address: data.address,
  geo_latitude: data.geoLatitude,
  geo_longitude: data.geoLongitude,
}));

// Legacy exports for backward compatibility
export const createAccessPointSchema = createAccessPointApiRequestSchema;
export const updateAccessPointSchema = updateAccessPointApiRequestSchema;

// Request schemas collection
export const accessPointRequestSchemas = {
  createAccessPoint: createAccessPointApiRequestSchema,
  updateAccessPoint: updateAccessPointApiRequestSchema,
  createAccessPointBackend: createAccessPointBackendRequestSchema,
  updateAccessPointBackend: updateAccessPointBackendRequestSchema,
};

export default accessPointRequestSchemas;
