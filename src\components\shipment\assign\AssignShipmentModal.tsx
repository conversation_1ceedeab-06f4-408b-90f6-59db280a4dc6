/* eslint-disable max-lines */
import { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import {
  IconCamera,
  IconCheck,
  IconAlertTriangle,
} from '@tabler/icons-react';
import { useRouter } from 'next/router';
import useTranslation from 'next-translate/useTranslation';
import { Shipment } from '../../../requests/shipment';
import { useMutation } from '@tanstack/react-query';
import { useIsClient } from '../../../hooks/useIsClient';
import { useSession } from 'next-auth/react';

// Extracted step UIs
import ShipmentQRStep from './ShipmentQRStep';
import PhotoStep from './PhotoStep';
import NotesStep from './NotesStep';

import { useScanShipmentMutation } from '../../../requests/hooks/enhanced-mutations';

interface AssignShipmentModalProps {
  opened: boolean;
  onClose: () => void;
  shipment: Shipment | null;
  onSuccess?: () => void;
}

interface QRLabel {
  id: string;
  qr_value: string;
  status: string;
  shipment_id: string;
  assigned_at: string;
}

export default function AssignShipmentModal({
  opened,
  onClose,
  shipment,
  onSuccess,
}: AssignShipmentModalProps) {
  const isClient = useIsClient();
  const router = useRouter();
  const { t } = useTranslation('shipments');
  const { data: sessionData } = useSession();
  const [activeStep, setActiveStep] = useState(0);
  const [qrLabel, setQrLabel] = useState<QRLabel | null>(null);
  const [scannedQR, setScannedQR] = useState('');
  const [photoPreview, setPhotoPreview] = useState<string | null>(null);
  const [photoUrl, setPhotoUrl] = useState<string | null>(null);
  const [notes, setNotes] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [scanMethod, setScanMethod] = useState<'camera' | 'manual'>('camera');

  const isRTL = router.locale === 'ar';

  // This modal is specifically for DROPOFF action (customer assignment)
  const totalSteps = 3; // QR Code -> Photo -> Notes
  const completedIndex = totalSteps;

  const handleClose = () => {
    setActiveStep(0);
    setQrLabel(null);
    setScannedQR('');
    setPhotoPreview(null);
    setPhotoUrl(null);
    setNotes('');
    setError(null);
    setScanMethod('camera');
    onClose();
  };

  const clearAlerts = () => {
    setError(null);
  };

  const handleStepChange = (step: number) => {
    clearAlerts();
    setActiveStep(step);
  };

  const validatePhoto = () => {
    if (!photoUrl) {
      setError('Please take a photo');
      return false;
    }
    return true;
  };

  // Generate QR Label mutation
  const generateQRMutation = useMutation({
    mutationFn: async (shipmentId: string) => {
      const response = await fetch('/api/qr-labels/generate-for-shipment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ shipment_id: shipmentId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to generate QR label');
      }

      return response.json();
    },
    onSuccess: (data) => {
      const qrLabelDataRaw = data.data?.qr_label || data.data?.qrLabel;

      // Normalize to legacy snake_case expected by the rest of this component
      const qrLabelData = qrLabelDataRaw && 'qrValue' in qrLabelDataRaw ? {
        id: qrLabelDataRaw.id,
        qr_value: qrLabelDataRaw.qrValue,
        status: qrLabelDataRaw.status,
        shipment_id: qrLabelDataRaw.shipmentId,
        assigned_at: qrLabelDataRaw.assignedAt,
      } : qrLabelDataRaw;

      setQrLabel(qrLabelData as unknown as QRLabel);
      setError(null);

      // Show success notification
      notifications.show({
        title: t('success'),
        message: t('packageSuccessfullyProcessed'),
        color: 'green',
        icon: <IconCheck size="1rem" />,
        autoClose: 3000,
      });
    },
  });

  // Upload photo mutation
  const uploadPhotoMutation = useMutation({
    mutationFn: async (data: { photoBase64: string; folder: string; geo_latitude?: number; geo_longitude?: number }) => {
      const response = await fetch('/api/uploads/photo', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          photoBase64: data.photoBase64,
          folder: data.folder,
          geo_latitude: data.geo_latitude || null,
          geo_longitude: data.geo_longitude || null,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to upload photo');
      }

      return response.json();
    },
  });

  // Scan shipment mutation (DROPOFF action) - using enhanced hook with auto-invalidation
  const scanShipmentMutationInstance = useScanShipmentMutation({
    successMessage: 'Package has been successfully assigned to the shipment.',
    showNotifications: false,
    onSuccess: () => {
      setActiveStep(completedIndex);
      setError(null);
      if (onSuccess) {
        setTimeout(() => {
          onSuccess();
          handleClose();
        }, 2000);
      }
    },
  });

  const handleGenerateQR = () => {
    if (shipment?.id) {
      generateQRMutation.mutate(shipment.id);
    }
  };

  const handlePhotoUpload = async (base64Data: string) => {
    try {
      const photoResponse = await uploadPhotoMutation.mutateAsync({
        photoBase64: base64Data,
        folder: 'shipment-scans',
      });

      // eslint-disable-next-line @typescript-eslint/no-shadow
      const photoUrl = photoResponse.data.photo_url;
      setPhotoUrl(photoUrl);
      setError(null);
    } catch (uploadErr) {
      if (uploadErr instanceof Error) {
        setError(uploadErr.message);
      } else {
        setError('Failed to upload photo. Please try again.');
      }
      setPhotoUrl(null);
      setPhotoPreview(null);
    }
  };

  const handlePhotoChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = async (e) => {
        const base64Data = e.target?.result as string;
        setPhotoPreview(base64Data);
        await handlePhotoUpload(base64Data);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleScanAndAssign = async () => {
    if (!photoUrl || !scannedQR) {
      setError('Please complete all required fields (QR code and photo)');
      return;
    }

    const userId = sessionData?.user?.id;
    if (!userId) {
      setError('User session not found. Please refresh and try again.');
      return;
    }

    if (!shipment) {
      setError('Shipment data not found. Please refresh and try again.');
      return;
    }

    // Validate that user is the origin AO and shipment is PENDING
    if (shipment.originAoId !== userId) {
      setError('You are not the origin Access Operator for this shipment.');
      return;
    }

    if (shipment.status !== 'PENDING') {
      setError(`This shipment is in ${shipment.status} status. DROPOFF action is only valid for PENDING shipments.`);
      return;
    }

    try {
      const scanData = {
        shipmentId: shipment.id,
        qrValue: scannedQR,
        photoUrl,
        action: 'DROPOFF' as const,
        notes: notes || undefined,
      };
      await scanShipmentMutationInstance.mutateAsync({ data: scanData });
      notifications.show({
        title: 'Success',
        message: 'Package has been successfully assigned to the shipment',
        color: 'green',
        icon: <IconCheck size="1rem" />,
      });

      if (onSuccess) {
        onSuccess();
      }
      handleClose();
    } catch (assignErr) {
      const errorMessage = assignErr instanceof Error ? assignErr.message : 'Failed to process assignment. Please try again.';
      setError(errorMessage);
    }
  };

  if (!isClient) {
    return null;
  }

  return (
    <Modal
      opened={opened}
      onClose={handleClose}
      title={t('assignPackageToShipment')}
      size="lg"
      centered
      style={{ direction: isRTL ? 'rtl' : 'ltr' }}
    >
      <Stack gap="md">
        {error && (
          <Alert
            icon={<IconAlertTriangle size="1rem" />}
            color="red"
            variant="light"
            onClose={() => setError(null)}
            withCloseButton
            style={{ textAlign: isRTL ? 'right' : 'left' }}
          >
            {error}
          </Alert>
        )}

        <Stepper
          active={activeStep}
          onStepClick={handleStepChange}
          allowNextStepsSelect={false}
        >
          <Stepper.Step label={t('shipmentQR')} description={t('scanShipmentQRCode')}>
            <ShipmentQRStep
              scannedQR={scannedQR}
              setScannedQR={setScannedQR}
              qrLabel={qrLabel}
              handleGenerateQR={handleGenerateQR}
              generateQRPending={generateQRMutation.isPending}
              scanMethod={scanMethod}
              setScanMethod={setScanMethod}
              error={error}
              setError={setError}
              onNext={() => setActiveStep((prev) => prev + 1)}
              opened={opened}
              activeStep={activeStep}
            />
          </Stepper.Step>

          <Stepper.Step label={t('photo')} description={t('takePackagePhoto')} icon={<IconCamera size="1.1rem" />}>
            <PhotoStep
              photoPreview={photoPreview}
              photoUrl={photoUrl}
              onFileChange={handlePhotoChange}
              uploadPending={uploadPhotoMutation.isPending}
              validatePhoto={validatePhoto}
              onBack={() => setActiveStep((prev) => prev - 1)}
              onNext={() => setActiveStep((prev) => prev + 1)}
              error={error}
            />
          </Stepper.Step>

          <Stepper.Step label={t('notes')} description={t('addNotes')}>
            <NotesStep
              notes={notes}
              setNotes={setNotes}
              onBack={() => setActiveStep((prev) => prev - 1)}
              onComplete={handleScanAndAssign}
              loading={scanShipmentMutationInstance.isPending}
            />
          </Stepper.Step>

          <Stepper.Completed>
            <Stack align="center" style={{ textAlign: isRTL ? 'right' : 'left' }}>
              <IconCheck size="3rem" color="green" />
              <Text fw={600} size="lg">
                {t('processComplete')}
              </Text>
              <Text c="dimmed" size="sm" ta="center">
                {t('packageSuccessfullyProcessed')}
              </Text>
              <Button mt="md" onClick={handleClose}>{t('close')}</Button>
            </Stack>
          </Stepper.Completed>
        </Stepper>
      </Stack>
    </Modal>
  );
}

AssignShipmentModal.defaultProps = {
  onSuccess: undefined,
};
