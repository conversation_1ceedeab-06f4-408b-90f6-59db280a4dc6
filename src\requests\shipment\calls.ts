/* eslint-disable max-lines */
import { API_ENDPOINT, HTTP_CODE } from '../../data';
import { CLIENT_API } from '../../lib/axios';
import { handleApiError } from '../../utils/handle-backend-error';
import { QueryClient } from '@tanstack/react-query';
import { CacheInvalidationManager, QUERY_KEYS } from '../cache-invalidation';
import {
  GetShipmentQueryProps,
  GetShipmentsQueryProps,
  GetMyShipmentsQueryProps,
  GetPendingShipmentsQueryProps,
  CreateShipmentQueryProps,
  CancelShipmentQueryProps,
  ScanShipmentQueryProps,
  DeliverShipmentQueryProps,
  ShipmentFilter,
} from './types';
import { deliverShipmentBackendRequestSchema } from './request-transformer';

// Use centralized query keys
const queryKeys = QUERY_KEYS.shipments;

/**
 * @description This function calls to get shipment data by ID.
 * @param id - Shipment ID
 * @returns shipment data
 */
const getShipmentRequest = (id: string) => CLIENT_API.get(`${API_ENDPOINT.shipments.base}/${id}`)
  .then((res) => res?.data)
  .catch((e) => {
    handleApiError(e);
    throw e.response?.data;
  });

/**
 * @description This function calls to get multiple shipments with filtering and pagination.
 * @param props
 * @returns list of shipments with pagination
 */
const getShipmentsRequest = (props: GetShipmentsQueryProps) => {
  const { pagination, filters, sort } = props;

  const queryParams: Record<string, string | undefined> = {
    page: pagination?.page?.toString(),
    limit: pagination?.pageSize?.toString(),
    sort,
  };

  // Forward selected filter keys that the backend understands
  const allowedFilterKeys: (keyof ShipmentFilter)[] = [
    'search',
    'status',
    'size',
    'customerId',
    'originAoId',
    'destAoId',
    'assignedCarOperatorId',
    'cancellationReason',
    'weightGte',
    'weightLte',
    'createdAtGte',
    'createdAtLte',
    'updatedAtGte',
    'updatedAtLte',
    'pickedUpAtGte',
    'pickedUpAtLte',
    'cancelledAtGte',
    'cancelledAtLte',
    'expiresAtGte',
    'expiresAtLte',
    'hasTrackingCode',
    'hasPickupCode',
    'isExpired',
  ];

  allowedFilterKeys.forEach((key) => {
    const value = filters?.[key];
    if (value !== undefined && value !== null && value !== '') {
      // Convert boolean and number values to strings
      queryParams[key] = typeof value === 'boolean' || typeof value === 'number'
        ? value.toString()
        : String(value);
    }
  });

  // Remove any params that are still undefined
  const cleanParams = Object.entries(queryParams).reduce((acc, [key, value]) => {
    if (value !== undefined) {
      acc[key] = value;
    }
    return acc;
  }, {} as Record<string, string>);

  return CLIENT_API.get(API_ENDPOINT.shipments.base, {
    params: cleanParams,
  })
    .then((res) => res?.data)
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

/**
 * @description This function calls to get current user's shipments.
 * @param props
 * @returns list of user's shipments with pagination
 */
const getMyShipmentsRequest = (props: GetMyShipmentsQueryProps) => {
  const { pagination, filters, sort } = props;

  const queryParams: Record<string, string | undefined> = {
    page: pagination?.page?.toString(),
    limit: pagination?.pageSize?.toString(),
    sort,
  };

  // Forward selected filter keys that the backend understands
  const allowedFilterKeys: (keyof ShipmentFilter)[] = [
    'search',
    'status',
    'originAoId',
    'destAoId',
  ];

  allowedFilterKeys.forEach((key) => {
    const value = filters?.[key];
    if (value !== undefined && value !== null && value !== '') {
      // Note: keep camelCase because backend expects camelCase query params (e.g. destAoId)
      queryParams[key] = String(value);
    }
  });

  // Remove any params that are still undefined
  const cleanParams = Object.entries(queryParams).reduce((acc, [key, value]) => {
    if (value !== undefined) {
      acc[key] = value;
    }
    return acc;
  }, {} as Record<string, string>);

  return CLIENT_API.get(API_ENDPOINT.shipments.my, {
    params: cleanParams,
  })
    .then((res) => res?.data)
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

/**
 * @description This function calls to get pending shipments.
 * @param props
 * @returns list of pending shipments with pagination
 */
const getPendingShipmentsRequest = (props: GetPendingShipmentsQueryProps) => {
  const { pagination, filters, sort } = props;

  const queryParams: Record<string, string | undefined> = {
    page: pagination?.page?.toString(),
    limit: pagination?.pageSize?.toString(),
    sort,
  };

  // Forward selected filter keys that the backend understands
  const allowedFilterKeys: (keyof ShipmentFilter)[] = [
    'search',
    'status',
    'originAoId',
    'destAoId',
    'role',
  ];

  allowedFilterKeys.forEach((key) => {
    const value = filters?.[key];
    if (value !== undefined && value !== null && value !== '') {
      queryParams[key] = String(value);
    }
  });

  // Remove undefined values
  const cleanParams = Object.entries(queryParams).reduce((acc, [key, value]) => {
    if (value !== undefined) {
      acc[key] = value;
    }
    return acc;
  }, {} as Record<string, string>);

  return CLIENT_API.get(API_ENDPOINT.shipments.pending, {
    params: cleanParams,
  })
    .then((res) => res?.data)
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

/**
 * @description This function calls to get AO assigned shipments with filtering and pagination.
 * Uses the same /api/shipments/my endpoint - backend filters based on user role from token.
 * @param props
 * @returns list of AO assigned shipments with pagination
 */
// eslint-disable-next-line sonarjs/no-identical-functions
const getMyAssignedShipmentsRequest = (props: GetMyShipmentsQueryProps) => {
  const { pagination, filters, sort } = props;

  const queryParams: Record<string, string | undefined> = {
    page: pagination?.page?.toString(),
    limit: pagination?.pageSize?.toString(),
    search: filters?.search,
    status: filters?.status || undefined,
    sort,
    // Add any AO-specific filters here if needed
  };

  // Remove undefined values
  const cleanParams = Object.entries(queryParams).reduce((acc, [key, value]) => {
    if (value !== undefined && value !== '') {
      acc[key] = value;
    }
    return acc;
  }, {} as Record<string, string>);

  return CLIENT_API.get(API_ENDPOINT.shipments.my, {
    params: cleanParams,
  })
    .then((res) => res?.data)
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

/**
 * @description This function calls to get CO available shipments with filtering and pagination.
 * Uses the same /api/shipments/my endpoint - backend filters based on user role from token.
 * For CO users, typically filters for AWAITING_PICKUP status.
 * @param props
 * @returns list of CO available shipments with pagination
 */
const getMyTransportedShipmentsRequest = (props: GetMyShipmentsQueryProps) => {
  const { pagination, filters, sort } = props;

  const queryParams: Record<string, string | undefined> = {
    page: pagination?.page?.toString(),
    limit: pagination?.pageSize?.toString(),
    search: filters?.search,
    // For CO users, default to AWAITING_PICKUP status if no status filter provided
    status: filters?.status || 'AWAITING_PICKUP',
    sort,
    // Add CO access point filters
    originAoId: filters?.originAoId,
    destAoId: filters?.destAoId,
  };

  // Remove undefined values
  const cleanParams = Object.entries(queryParams).reduce((acc, [key, value]) => {
    if (value !== undefined && value !== '') {
      acc[key] = value;
    }
    return acc;
  }, {} as Record<string, string>);

  return CLIENT_API.get(API_ENDPOINT.shipments.my, {
    params: cleanParams,
    headers: {
      // Add cache control headers to prevent caching
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      Pragma: 'no-cache',
      Expires: '0',
    },
  })
    .then((res) => res?.data)
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

/**
 * @description This function calls to create a new shipment.
 * @param props
 * @returns created shipment data with QR codes
 */
const createShipmentRequest = (props: CreateShipmentQueryProps) => {
  const { data } = props;
  return CLIENT_API.post(API_ENDPOINT.shipments.base, data)
    .then((res) => res?.data)
    .catch((e) => {
      // Don't call handleApiError here to avoid duplicate notifications
      // The mutation's onError callback will handle the error display
      throw e.response?.data;
    });
};

/**
 * @description This function calls to cancel a shipment.
 * @param props
 * @returns cancelled shipment data
 */
const cancelShipmentRequest = (props: CancelShipmentQueryProps) => {
  const { id, data } = props;
  return CLIENT_API.post(`${API_ENDPOINT.shipments.base}/${id}/cancel`, data)
    .then((res) => res?.data)
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

/**
 * @description This function calls to scan a shipment QR code.
 * @param props
 * @returns scan result data
 */
const scanShipmentRequest = (props: ScanShipmentQueryProps) => {
  const { data } = props;
  return CLIENT_API.post(API_ENDPOINT.shipments.scan, data)
    .then((res) => res?.data)
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

/**
 * @description This function calls to deliver a shipment.
 * @param props
 * @returns delivery result data
 */
const deliverShipmentRequest = (props: DeliverShipmentQueryProps) => {
  const { data } = props;
  const backendData = deliverShipmentBackendRequestSchema.parse(data);
  return CLIENT_API.post(API_ENDPOINT.shipments.deliver, backendData)
    .then((res) => res?.data)
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

// Get shipment query function
export const getShipmentQuery = (props: GetShipmentQueryProps) => ({
  queryKey: [queryKeys.single, props.id],
  queryFn: () => getShipmentRequest(props.id),
  refetchOnWindowFocus: props?.refetchOnWindowFocus ?? false,
  enabled: props?.enabled,
  retry: (failureCount: number, error: { code: HTTP_CODE }) => error?.code !== HTTP_CODE.UNAUTHORIZED,
});

// Get multiple shipments query function
export const getShipmentsQuery = (props: GetShipmentsQueryProps) => ({
  queryKey: [
    queryKeys.list,
    props?.filters,
    props?.sort,
    props?.pagination,
  ],
  queryFn: () => getShipmentsRequest(props),
  refetchOnWindowFocus: false,
  enabled: props?.enabled,
  retry: (failureCount: number, error: { code: HTTP_CODE }) => error?.code !== HTTP_CODE.UNAUTHORIZED,
});

// Get my assigned shipments query function for AO users
export const getMyAssignedShipmentsQuery = (props: GetMyShipmentsQueryProps) => ({
  queryKey: [
    queryKeys.myAssignedShipments,
    props?.filters,
    props?.sort,
    props?.pagination,
  ],
  queryFn: () => getMyAssignedShipmentsRequest(props),
  refetchOnWindowFocus: false,
  enabled: props?.enabled,
  retry: (failureCount: number, error: { code: HTTP_CODE }) => error?.code !== HTTP_CODE.UNAUTHORIZED,
});

// Get my transported shipments query function for CO users
export const getMyTransportedShipmentsQuery = (props: GetMyShipmentsQueryProps) => ({
  queryKey: [
    queryKeys.myTransportedShipments,
    props?.filters,
    props?.sort,
    props?.pagination,
  ],
  queryFn: () => getMyTransportedShipmentsRequest(props),
  refetchOnWindowFocus: false,
  enabled: props?.enabled,
  retry: (failureCount: number, error: { code: HTTP_CODE }) => error?.code !== HTTP_CODE.UNAUTHORIZED,
});

// Get my shipments query function
export const getMyShipmentsQuery = (props: GetMyShipmentsQueryProps) => ({
  queryKey: [
    queryKeys.myShipments,
    props?.filters,
    props?.sort,
    props?.pagination,
  ],
  queryFn: () => getMyShipmentsRequest(props),
  refetchOnWindowFocus: false,
  enabled: props?.enabled,
  retry: (failureCount: number, error: { code: HTTP_CODE }) => error?.code !== HTTP_CODE.UNAUTHORIZED,
});

// Get pending shipments query function
export const getPendingShipmentsQuery = (props: GetPendingShipmentsQueryProps) => ({
  queryKey: [queryKeys.pendingShipments, props?.filters, props?.pagination],
  queryFn: () => getPendingShipmentsRequest(props),
  refetchOnWindowFocus: false,
  enabled: props?.enabled,
  retry: (failureCount: number, error: { code: HTTP_CODE }) => error?.code !== HTTP_CODE.UNAUTHORIZED,
});

// Create shipment mutation function
export const createShipmentMutation = () => ({
  mutationKey: [queryKeys.create],
  mutationFn: (props: CreateShipmentQueryProps) => createShipmentRequest(props),
});

// Cancel shipment mutation function
export const cancelShipmentMutation = () => ({
  mutationKey: [queryKeys.cancel],
  mutationFn: (props: CancelShipmentQueryProps) => cancelShipmentRequest(props),
});

// Scan shipment mutation function
export const scanShipmentMutation = () => ({
  mutationKey: [queryKeys.scan],
  mutationFn: (props: ScanShipmentQueryProps) => scanShipmentRequest(props),
});

// Deliver shipment mutation function
export const deliverShipmentMutation = () => ({
  mutationKey: [queryKeys.deliver],
  mutationFn: (props: DeliverShipmentQueryProps) => deliverShipmentRequest(props),
});

/**
 * Enhanced mutation functions with built-in cache invalidation
 * These functions return mutation configurations that automatically handle cache invalidation
 */

// Create shipment mutation with auto-invalidation
export const createShipmentMutationWithInvalidation = (queryClient: QueryClient) => {
  const cacheManager = new CacheInvalidationManager(queryClient);
  return {
    ...createShipmentMutation(),
    onSuccess: async () => {
      await cacheManager.invalidateAfterShipmentCreate();
    },
  };
};

// Cancel shipment mutation with auto-invalidation
export const cancelShipmentMutationWithInvalidation = (queryClient: QueryClient) => {
  const cacheManager = new CacheInvalidationManager(queryClient);
  return {
    ...cancelShipmentMutation(),
    onSuccess: async () => {
      await cacheManager.invalidateAfterShipmentCancel();
    },
  };
};

// Scan shipment mutation with auto-invalidation
export const scanShipmentMutationWithInvalidation = (queryClient: QueryClient) => {
  const cacheManager = new CacheInvalidationManager(queryClient);
  return {
    ...scanShipmentMutation(),
    onSuccess: async () => {
      await cacheManager.invalidateAfterShipmentScan();
    },
  };
};

// Deliver shipment mutation with auto-invalidation
export const deliverShipmentMutationWithInvalidation = (queryClient: QueryClient) => {
  const cacheManager = new CacheInvalidationManager(queryClient);
  return {
    ...deliverShipmentMutation(),
    onSuccess: async () => {
      await cacheManager.invalidateAfterShipmentDelivery();
    },
  };
};
