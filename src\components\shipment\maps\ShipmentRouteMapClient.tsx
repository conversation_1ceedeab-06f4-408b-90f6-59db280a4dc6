/* eslint-disable react/require-default-props */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>,
} from 'react-leaflet';
import { LatLngExpression, Icon } from 'leaflet';
import { Paper, Text } from '@mantine/core';
import useTranslation from 'next-translate/useTranslation';
import { AccessPoint } from '../../../requests';

interface ShipmentRouteMapClientProps {
  originAccessPoint: AccessPoint;
  destAccessPoint: AccessPoint;
}

// Create custom icons for different marker types
const createIcon = (color: string, symbol: string) => {
  if (typeof window === 'undefined') return undefined;

  const svgString = `<svg width="25" height="41" viewBox="0 0 25 41" xmlns="http://www.w3.org/2000/svg">
    <path d="M12.5 0C5.6 0 0 5.6 0 12.5c0 12.5 12.5 28.5 12.5 28.5s12.5-16 12.5-28.5C25 5.6 19.4 0 12.5 0z" fill="${color}"/>
    <circle cx="12.5" cy="12.5" r="8" fill="white"/>
    <text x="12.5" y="17" text-anchor="middle" font-size="12" font-weight="bold" fill="${color}">${symbol}</text>
  </svg>`;

  return new Icon({
    iconUrl: `data:image/svg+xml;charset=utf-8,${encodeURIComponent(svgString)}`,
    iconSize: [25, 41],
    iconAnchor: [12, 41],
    popupAnchor: [1, -34],
  });
};

export default function ShipmentRouteMapClient({
  originAccessPoint,
  destAccessPoint,
}: ShipmentRouteMapClientProps) {
  const { t } = useTranslation('shipments');
  const [isClient, setIsClient] = useState(false);

  // Ensure this only renders on client side
  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) {
    return (
      <Paper withBorder style={{ height: '400px' }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100%',
        }}
        >
          Loading map...
        </div>
      </Paper>
    );
  }

  // Create icons
  const originIcon = createIcon('#10b981', 'O');
  const destinationIcon = createIcon('#ef4444', 'D');

  // Calculate positions
  const originPos: LatLngExpression = [originAccessPoint.geoLatitude!, originAccessPoint.geoLongitude!];
  const destPos: LatLngExpression = [destAccessPoint.geoLatitude!, destAccessPoint.geoLongitude!];

  const mapCenter: LatLngExpression = [
    (originAccessPoint.geoLatitude! + destAccessPoint.geoLatitude!) / 2,
    (originAccessPoint.geoLongitude! + destAccessPoint.geoLongitude!) / 2,
  ];

  // Create route polyline
  const routePositions = [originPos, destPos];

  return (
    <Paper withBorder style={{ height: '400px', overflow: 'hidden' }}>
      <MapContainer
        {...({ center: mapCenter } as any)}
        zoom={10}
        style={{ height: '100%', width: '100%' }}
      >
        <TileLayer
          {...({ attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors' } as any)}
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />

        {/* Origin Marker */}
        <Marker
          position={originPos}
          {...(originIcon ? { icon: originIcon } : {})}
        >
          <Popup>
            <div style={{ minWidth: '200px' }}>
              <Text fw={600} size="sm" c="green">
                🚚
                {t('originAccessPoint')}
              </Text>
              <Text size="xs" fw={500} mb="xs">{originAccessPoint.businessName || originAccessPoint.name}</Text>
              {originAccessPoint.address && (
                <Text size="xs" c="dimmed" mb="xs">
                  📍
                  {originAccessPoint.address}
                </Text>
              )}
              {originAccessPoint.phone && (
                <Text size="xs" c="dimmed" mb="xs">
                  📞
                  {originAccessPoint.phone}
                </Text>
              )}
              {originAccessPoint.email && (
                <Text size="xs" c="dimmed" mb="xs">
                  ✉️
                  {originAccessPoint.email}
                </Text>
              )}
              <Text size="xs" c={originAccessPoint.status === 'ACTIVE' ? 'green' : 'orange'} fw={500}>
                Status:
                {' '}
                {originAccessPoint.status}
              </Text>
            </div>
          </Popup>
        </Marker>

        {/* Destination Marker */}
        <Marker
          position={destPos}
          {...(destinationIcon ? { icon: destinationIcon } : {})}
        >
          <Popup>
            <div style={{ minWidth: '200px' }}>
              <Text fw={600} size="sm" c="red">
                🏁
                {t('destinationAccessPoint')}
              </Text>
              <Text size="xs" fw={500} mb="xs">{destAccessPoint.businessName || destAccessPoint.name}</Text>
              {destAccessPoint.address && (
                <Text size="xs" c="dimmed" mb="xs">
                  📍
                  {destAccessPoint.address}
                </Text>
              )}
              {destAccessPoint.phone && (
                <Text size="xs" c="dimmed" mb="xs">
                  📞
                  {destAccessPoint.phone}
                </Text>
              )}
              {destAccessPoint.email && (
                <Text size="xs" c="dimmed" mb="xs">
                  ✉️
                  {destAccessPoint.email}
                </Text>
              )}
              <Text size="xs" c={destAccessPoint.status === 'ACTIVE' ? 'green' : 'orange'} fw={500}>
                Status:
                {' '}
                {destAccessPoint.status}
              </Text>
            </div>
          </Popup>
        </Marker>

        {/* Route Line */}
        <Polyline
          positions={routePositions}
          pathOptions={{
            color: '#3b82f6',
            weight: 3,
            opacity: 0.8,
            dashArray: '10, 5',
          }}
        />
      </MapContainer>
    </Paper>
  );
}
