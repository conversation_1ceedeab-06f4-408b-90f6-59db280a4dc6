import type { NextApiRequest, NextApiResponse } from 'next';
import { z } from 'zod';
import { BACKEND_API } from '../../../src/lib/axios';
import { API_ENDPOINT, HTTP_CODE } from '../../../src/data';
import { createApiError } from '../../../src/utils';
import { resendVerificationOtpApiResponseSchema } from '../../../src/requests/auth/response-transform';

const emailSchema = z.object({
  email: z.string().email({ message: 'Invalid email address' }),
});

async function handlePost(req: NextApiRequest, res: NextApiResponse) {
  try {
    const validatedData = emailSchema.parse(req.body);

    const response = await BACKEND_API.post(
      API_ENDPOINT.auth.resendVerificationOtp,
      validatedData,
    );
    const validatedResponse = resendVerificationOtpApiResponseSchema.parse(response.data);
    return res.status(HTTP_CODE.SUCCESS).json(validatedResponse);
  } catch (error: unknown) {
    const apiError = createApiError({ error });
    return res.status(apiError.code).json(apiError);
  }
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  if (req.method === 'POST') {
    return handlePost(req, res);
  }

  res.setHeader('Allow', ['POST']);
  return res.status(HTTP_CODE.METHOD_NOT_ALLOWED).json({
    success: false,
    message: `Method ${req.method} not allowed`,
    error: {
      type: 'METHOD_NOT_ALLOWED',
      details: `Method ${req.method} is not allowed for this endpoint.`,
    },
  });
}
