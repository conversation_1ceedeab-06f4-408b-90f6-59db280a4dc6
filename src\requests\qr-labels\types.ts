// QR Label Types
export interface QRLabel {
  id: string;
  qrValue: string;
  status: 'AVAILABLE' | 'ASSIGNED' | 'USED';
  shipmentId?: string;
  assignedAt?: string;
  usedAt?: string;
  createdAt: string;
  updatedAt: string;
}

// Request Types
export interface GenerateForShipmentRequest {
  shipmentId: string;
}

export interface GenerateBulkRequest {
  quantity: number;
  prefix?: string;
}

export interface GetQRLabelsRequest {
  pagination?: {
    page?: number;
    limit?: number;
  };
  filters?: {
    status?: 'AVAILABLE' | 'ASSIGNED' | 'USED';
    shipmentId?: string;
    createdAtGte?: string;
    createdAtLte?: string;
  };
  sort?: {
    field?: 'createdAt' | 'updatedAt' | 'assignedAt' | 'usedAt';
    order?: 'asc' | 'desc';
  };
}

// Response Types
export interface GenerateForShipmentResponse {
  success: boolean;
  message: string;
  data: {
    qrLabel: QRLabel;
  };
}

export interface GenerateBulkResponse {
  success: boolean;
  message: string;
  data: {
    qrLabels: QRLabel[];
    count: number;
  };
}

export interface GetQRLabelsResponse {
  success: boolean;
  message: string;
  data: {
    qrLabels: QRLabel[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
}

export interface GetQRLabelResponse {
  success: boolean;
  message: string;
  data: {
    qrLabel: QRLabel;
  };
}

// Query Props Types
export interface GenerateForShipmentQueryProps {
  data: GenerateForShipmentRequest;
  enabled?: boolean;
}

export interface GenerateBulkQueryProps {
  data: GenerateBulkRequest;
  enabled?: boolean;
}

export interface GetQRLabelQueryProps {
  id: string;
  enabled?: boolean;
}

export interface GeneratePDFRequest {
  count: number;
}

export interface GeneratePDFResponse {
  success: boolean;
  message: string;
  data: {
    pdfUrl: string;
    blob: Blob;
  };
}

export interface GeneratePDFQueryProps {
  data: GeneratePDFRequest;
  enabled?: boolean;
}
