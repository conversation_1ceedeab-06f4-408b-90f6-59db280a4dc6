import { NextApiRequest, NextApiResponse } from 'next';
import { getJwt } from '../../../src/utils';
import { BACKEND_API } from '../../../src/lib/axios';
import { HTTP_CODE } from '../../../src/data';
import createApiError from '../../../src/utils/create-api-error';

/**
 * @description Handle POST request to upload photo
 */
async function handleUploadPhoto(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  try {
    // Set cache control headers to prevent caching
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');

    // Verify authentication
    const { token } = await getJwt(req);
    if (!token) {
      return res.status(HTTP_CODE.UNAUTHORIZED).json({
        success: false,
        message: 'Authentication required',
      });
    }

    // Check if request is multipart/form-data or JSON
    const contentType = req.headers['content-type'] || '';

    if (contentType.includes('multipart/form-data')) {
      // Handle FormData upload (file upload)
      // For now, we'll convert the file to base64 and send to backend
      // In a real implementation, you might want to use a library like multer
      return res.status(HTTP_CODE.BAD_REQUEST).json({
        success: false,
        message: 'FormData upload not implemented. Please use base64 format.',
      });
    }

    // Handle JSON upload (base64)
    const {
      photoBase64,
      folder,
    } = req.body;

    if (!photoBase64 || !folder) {
      return res.status(HTTP_CODE.BAD_REQUEST).json({
        success: false,
        message: 'Photo data and folder are required',
      });
    }

    // Validate photoBase64 format
    if (typeof photoBase64 !== 'string' || !photoBase64.startsWith('data:image')) {
      return res.status(HTTP_CODE.BAD_REQUEST).json({
        success: false,
        message: 'Invalid photo format. Expected data URL format.',
      });
    }

    const uploadData = {
      photoBase64,
      folder,
    };

    // Send to backend API
    const response = await BACKEND_API.post('/uploads/photo', uploadData, {
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });

    // Return response from backend
    return res.status(HTTP_CODE.SUCCESS).json(response.data);
  } catch (error: unknown) {
    const apiError = createApiError({ error });
    return res.status(apiError.code).json(apiError);
  }
}

/**
 * @description API endpoint to upload photos for shipment operations
 * @param req NextApiRequest
 * @param res NextApiResponse
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { method } = req;

  if (method === 'POST') {
    return handleUploadPhoto(req, res);
  }

  // Method not allowed
  res.setHeader('Allow', ['POST']);
  return res.status(HTTP_CODE.METHOD_NOT_ALLOWED).json({
    success: false,
    message: `Method ${method} not allowed`,
    code: HTTP_CODE.METHOD_NOT_ALLOWED,
  });
}
