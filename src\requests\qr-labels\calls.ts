import { useMutation, useQueryClient, QueryClient } from '@tanstack/react-query';
import { CLIENT_API } from '../../lib/axios';
import { API_ENDPOINT } from '../../data/api-endpoints';
import { handleApiError } from '../../utils/handle-backend-error';
import { CacheInvalidationManager, QUERY_KEYS } from '../cache-invalidation';
import {
  GenerateForShipmentQueryProps,
  GenerateForShipmentResponse,
  GeneratePDFQueryProps,
  GeneratePDFResponse,
} from './types';
import { transformGenerateForShipmentResponse } from './response-transformer';

// Use centralized query keys
const queryKeys = QUERY_KEYS.qrLabels;

/**
 * @description Generate QR label for a specific shipment
 * @param props
 * @returns generated QR label data
 */
const generateForShipmentRequest = (props: GenerateForShipmentQueryProps) => {
  const { data } = props;
  return CLIENT_API.post(API_ENDPOINT.qrLabels.generateForShipment, data)
    .then((res) => transformGenerateForShipmentResponse(res?.data))
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

/**
 * @description Generate QR labels PDF (without shipment)
 * @param props
 * @returns PDF URL data
 */
const generatePDFRequest = (props: GeneratePDFQueryProps) => {
  const { data } = props;
  return CLIENT_API.post(API_ENDPOINT.qrLabels.generatePDF, data, { responseType: 'arraybuffer' })
    .then((res) => {
      // Convert arraybuffer to Blob URL for download or preview
      const blob = new Blob([res.data], { type: 'application/pdf' });
      const url = URL.createObjectURL(blob);
      return {
        success: true,
        message: 'PDF generated',
        data: { pdfUrl: url, blob },
      } as GeneratePDFResponse;
    })
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

// Generate for shipment mutation function
export const generateForShipmentMutation = () => ({
  mutationKey: [queryKeys.generateForShipment],
  mutationFn: (props: GenerateForShipmentQueryProps) => generateForShipmentRequest(props),
});

// Generate PDF mutation function
export const generatePDFMutation = () => ({
  mutationKey: [queryKeys.generatePDF],
  mutationFn: (props: GeneratePDFQueryProps) => generatePDFRequest(props),
});

/**
 * Enhanced mutation functions with built-in cache invalidation
 */

// Generate for shipment mutation with auto-invalidation
export const generateForShipmentMutationWithInvalidation = (queryClient: QueryClient) => {
  const cacheManager = new CacheInvalidationManager(queryClient);
  return {
    ...generateForShipmentMutation(),
    onSuccess: async () => {
      await cacheManager.invalidateAfterQRLabelGeneration();
    },
  };
};

// Legacy hook for backward compatibility
export const useGenerateForShipmentMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<GenerateForShipmentResponse, Error, GenerateForShipmentQueryProps>({
    mutationFn: generateForShipmentRequest,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [queryKeys.list] });
    },
  });
};

// Legacy hook for generatePDF
export const useGeneratePDFMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<GeneratePDFResponse, Error, GeneratePDFQueryProps>({
    mutationFn: generatePDFRequest,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [queryKeys.list] });
    },
  });
};

export {
  generateForShipmentRequest,
  generatePDFRequest,
};
