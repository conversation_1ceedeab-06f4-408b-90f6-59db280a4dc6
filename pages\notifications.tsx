import React, { useEffect } from 'react';
import {
  Container,
  Title,
  Group,
  ThemeIcon,
  Stack,
  Box,
} from '@mantine/core';
import { IconBell } from '@tabler/icons-react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { NotificationList } from '../src/components/notifications';
import useTranslation from 'next-translate/useTranslation';

export default function NotificationsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { t } = useTranslation();

  useEffect(() => {
    if (status === 'loading') return;
    if (!session) {
      router.push('/auth/login');
    }
  }, [session, status, router]);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleNotificationClick = (notification: any) => {
    if (notification.shipment_id) {
      router.push(`/shipments/${notification.shipment_id}`);
    }
  };

  if (status === 'loading') {
    return null;
  }

  if (!session) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Notifications - NAQALAT</title>
        <meta name="description" content="View and manage your notifications" />
      </Head>

      <Container size="lg" py="xl">
        <Stack gap="xl">
          {/* Header */}
          <Box>
            <Group gap="sm" mb="xs">
              <ThemeIcon
                size="lg"
                radius="md"
                variant="light"
                color="blue"
              >
                <IconBell size="1.2rem" />
              </ThemeIcon>
              <Title order={2}>
                {t('notifications:notifications')}
              </Title>
            </Group>
          </Box>

          <NotificationList
            pageSize={25}
            showFilters
            showPagination
            onNotificationClick={handleNotificationClick}
          />
        </Stack>
      </Container>
    </>
  );
}
