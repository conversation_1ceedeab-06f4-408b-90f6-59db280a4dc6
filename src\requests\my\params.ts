/* eslint-disable sonarjs/cognitive-complexity */
/* eslint-disable complexity */
import { NextApiRequest } from 'next';

const toSnakeCase = (str: string) => str.replace(/([A-Z])/g, '_$1').toLowerCase();

type RelationshipConfig = {
  relationField: string;
  entityField: string;
  valueField: string;
};

export const buildMyShipmentsFilter = (filterParams: Record<string, string | string[]>) => {
  const relationships: Record<string, RelationshipConfig> = {
    // Add relationships if needed in the future
    // customer: {
    //   relationField: 'Customer',
    //   entityField: 'user',
    //   valueField: 'id',
    // },
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const filterConditions: Record<string, any>[] = [];

  Object.entries(filterParams).forEach(([key, value]) => {
    if (!value || value === '') return;

    if (key.endsWith('Gte')) {
      const fieldName = toSnakeCase(key.replace('Gte', ''));
      filterConditions.push({ [fieldName]: { gte: value } });
      return;
    }

    if (key.endsWith('Lte')) {
      const fieldName = toSnakeCase(key.replace('Lte', ''));
      filterConditions.push({ [fieldName]: { lte: value } });
      return;
    }

    if (key === 'search') {
      filterConditions.push({
        OR: [
          { id: { contains: value, mode: 'insensitive' } },
          { receiver_name: { contains: value, mode: 'insensitive' } },
          { receiver_phone: { contains: value, mode: 'insensitive' } },
          { description: { contains: value, mode: 'insensitive' } },
        ],
      });
      return;
    }

    if (relationships[key]) {
      const config = relationships[key];
      const values = Array.isArray(value) ? value : [value];

      values.forEach((val) => {
        filterConditions.push({
          [config.relationField]: {
            some: {
              [config.entityField]: {
                [config.valueField]: val,
              },
            },
          },
        });
      });
      return;
    }

    filterConditions.push({ [toSnakeCase(key)]: value });
  });

  return filterConditions.length ? { AND: filterConditions } : undefined;
};

// Sort keys mapping for my shipments
export const sortKeysMapping = new Map([
  ['id', 'id'],
  ['status', 'status'],
  ['weight', 'weight'],
  ['size', 'size'],
  ['receiverName', 'receiver_name'],
  ['receiverPhone', 'receiver_phone'],
  ['createdAt', 'created_at'],
  ['updatedAt', 'updated_at'],
  ['pickedUpAt', 'picked_up_at'],
  ['cancelledAt', 'cancelled_at'],
  ['expiresAt', 'expires_at'],
]);

export const getFilterParams = (query: Record<string, string | string[]>) => Object.entries(query).reduce((params: Record<string, string | string[]>, [key, value]) => {
  if (!key.startsWith('filter[') || !value || value === '') {
    return params;
  }

  const filterKey = key.match(/\[(.*?)\]/)?.[1];
  if (!filterKey) {
    return params;
  }

  return { ...params, [filterKey]: value };
}, {});

export const getPaginationParams = (query: Record<string, string | string[]>) => {
  const commonKeys = ['search', 'page', 'pageSize', 'sortBy', 'sortOrder'];

  return commonKeys.reduce((params, key) => {
    const value = query[key];
    if (!value || value === '') {
      return params;
    }

    return {
      ...params,
      [key]: Array.isArray(value) ? value[0] : value,
    };
  }, {});
};

export const getRestParams = (query: Record<string, string | string[]>) => {
  const skipKeys = ['search', 'page', 'pageSize', 'sortBy', 'sortOrder'];

  return Object.entries(query).reduce((params, [key, value]) => {
    if (!value || value === ''
        || key.startsWith('filter[')
        || skipKeys.includes(key)) {
      return params;
    }

    return {
      ...params,
      [key]: Array.isArray(value) ? value[0] : value,
    };
  }, {});
};

export const returnParams = (req: NextApiRequest) => {
  const query = req.query as Record<string, string | string[]>;

  const filterParams = getFilterParams(query);
  const paginationParams = getPaginationParams(query);
  const restParams = getRestParams(query);

  // Start with basic parameters
  const result: Record<string, unknown> = {
    ...paginationParams,
    ...restParams,
  };

  // Add individual filter parameters that the backend service expects
  // These are simple parameters, not complex filter objects
  if (filterParams.search) {
    result.search = filterParams.search;
  }
  if (filterParams.status) {
    result.status = filterParams.status;
  }
  // Add CO access point filters for filtering shipments by origin/destination AO
  if (filterParams.originAoId) {
    result.originAoId = filterParams.originAoId;
  }
  if (filterParams.destAoId) {
    result.destAoId = filterParams.destAoId;
  }

  return result;
};
