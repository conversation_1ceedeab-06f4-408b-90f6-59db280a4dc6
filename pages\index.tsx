/* eslint-disable react/require-default-props */
/* eslint-disable max-len */
/* eslint-disable complexity */
/* eslint-disable jsx-a11y/aria-role */
/* eslint-disable max-lines */

import React from 'react';
import {
  Button,
  Container,
  Title,
  Text,
  Group,
  Loader,
  Center,
  SimpleGrid,
  Card,
  ThemeIcon,
  Stack,
  Box,
  Badge,
  Divider,
  Paper,
  Anchor,
  useMantineTheme,
  useMantineColorScheme,
} from '@mantine/core';
import {
  IconTruck,
  IconMapPin,
  IconShieldCheck,
  IconClock,
  IconUsers,
  IconBarcode,
  IconBell,
  IconChartLine,
  IconGlobe,
  IconPackage,
  IconArrowRight,
  IconStar,
  IconQuote,
  IconPhone,
  IconMail,
  IconMapPinFilled,
} from '@tabler/icons-react';
import { useSession, signOut } from 'next-auth/react';
import { useRouter } from 'next/router';
import useTranslation from 'next-translate/useTranslation';
import Link from 'next/link';
import LanguageSwitcher from '../src/components/common/LanguageSwitcher';

// Helper function to determine dashboard path based on user type
const getUserDashboardPath = (userType?: string | null): string => {
  switch (userType) {
    case 'CUSTOMER':
      return '/dashboard/customer';
    case 'CAR_OPERATOR':
      return '/dashboard/car-operator';
    case 'ACCESS_OPERATOR':
      return '/dashboard/access-operator';
    default:
      return '/';
  }
};

// Enhanced feature card component
type IconComponent = React.ComponentType<{ size?: string | number; color?: string }>;

function FeatureCard({
  icon: Icon,
  title,
  description,
  badge,
  gradient,
}: {
  icon: IconComponent;
  title: string;
  description: string;
  badge?: string;
  gradient?: [string, string];
}) {
  const theme = useMantineTheme();
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === 'dark';

  // Safe gradient color access with fallbacks
  const getGradientBackground = () => {
    if (!gradient || isDark) return isDark ? theme.colors.dark[6] : theme.white;

    try {
      const color1 = theme.colors[gradient[0]]?.[0] || theme.colors.blue[0];
      const color2 = theme.colors[gradient[1]]?.[1] || theme.colors.cyan[1];
      return `linear-gradient(135deg, ${color1} 0%, ${color2} 100%)`;
    } catch {
      return isDark ? theme.colors.dark[6] : theme.white;
    }
  };

  return (
    <Card
      withBorder
      padding="xl"
      radius="xl"
      shadow="md"
      h="100%"
      style={{
        background: getGradientBackground(),
        borderColor: isDark ? theme.colors.dark[4] : theme.colors.gray[2],
        transition: 'all 0.3s ease',
        cursor: 'pointer',
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.transform = 'translateY(-4px)';
        e.currentTarget.style.boxShadow = theme.shadows.lg;
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.transform = 'translateY(0)';
        e.currentTarget.style.boxShadow = theme.shadows.md;
      }}
    >
      <Stack gap="lg" align="flex-start">
        <Group justify="space-between" w="100%">
          <ThemeIcon
            variant="gradient"
            size="xl"
            radius="xl"
            gradient={gradient ? { from: gradient[0], to: gradient[1] } : { from: 'blue', to: 'cyan' }}
          >
            <Icon size="1.8rem" />
          </ThemeIcon>
          {badge && (
            <Badge variant="light" color={gradient?.[0] || 'blue'} size="sm" radius="md">
              {badge}
            </Badge>
          )}
        </Group>
        <Box>
          <Title order={3} mb="xs" c={isDark ? 'gray.1' : 'dark.8'}>
            {title}
          </Title>
          <Text c="dimmed" size="sm" lh={1.6}>
            {description}
          </Text>
        </Box>
      </Stack>
    </Card>
  );
}

// Stats component
function StatsSection() {
  const theme = useMantineTheme();
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === 'dark';

  const stats = [
    { value: '10K+', label: 'Shipments Delivered', icon: IconPackage },
    { value: '500+', label: 'Active Clients', icon: IconUsers },
    { value: '99.9%', label: 'Delivery Success Rate', icon: IconChartLine },
    { value: '24/7', label: 'Customer Support', icon: IconClock },
  ];

  return (
    <Paper
      p="xl"
      radius="xl"
      style={{
        background: isDark
          ? `linear-gradient(135deg, ${theme.colors.dark[7]} 0%, ${theme.colors.dark[6]} 100%)`
          : `linear-gradient(135deg, ${theme.colors.blue[6]} 0%, ${theme.colors.cyan[5]} 100%)`,
        border: `1px solid ${isDark ? theme.colors.dark[4] : 'transparent'}`,
      }}
    >
      <SimpleGrid cols={{ base: 2, sm: 4 }} spacing="xl">
        {stats.map((stat, index) => (
          // eslint-disable-next-line react/no-array-index-key
          <Stack key={index} align="center" gap="xs">
            <ThemeIcon
              size="lg"
              variant="light"
              color={isDark ? 'blue' : 'white'}
              radius="xl"
              style={{ background: isDark ? theme.colors.blue[9] : 'rgba(255,255,255,0.2)' }}
            >
              <stat.icon size="1.2rem" />
            </ThemeIcon>
            <Text
              size="xl"
              fw={700}
              c={isDark ? 'blue.3' : 'white'}
            >
              {stat.value}
            </Text>
            <Text
              size="sm"
              c={isDark ? 'gray.4' : 'blue.0'}
              ta="center"
            >
              {stat.label}
            </Text>
          </Stack>
        ))}
      </SimpleGrid>
    </Paper>
  );
}

// Testimonial component
function TestimonialCard({
  name, role, company, testimonial, rating,
}: {
  name: string;
  role: string;
  company: string;
  testimonial: string;
  rating: number;
}) {
  const theme = useMantineTheme();
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === 'dark';
  const router = useRouter();
  const isRTL = router.locale === 'ar';

  return (
    <Card
      withBorder
      padding="xl"
      radius="lg"
      shadow="sm"
      style={{
        background: isDark ? theme.colors.dark[6] : theme.white,
        borderColor: isDark ? theme.colors.dark[4] : theme.colors.gray[2],
      }}
    >
      <Stack gap="md">
        <Group gap="xs">
          {Array.from({ length: rating }).map((_, i) => (
            // eslint-disable-next-line react/no-array-index-key
            <IconStar key={i} size="1rem" color={theme.colors.yellow[6]} fill={theme.colors.yellow[6]} />
          ))}
        </Group>
        <Text size="sm" style={{ fontStyle: 'italic' }} c={isDark ? 'gray.3' : 'dark.6'}>
          <IconQuote
            size="1rem"
            style={{
              verticalAlign: 'top',
              [isRTL ? 'marginLeft' : 'marginRight']: 4,
            }}
          />
          {testimonial}
        </Text>
        <Divider />
        <Box>
          <Text fw={600} size="sm" c={isDark ? 'gray.1' : 'dark.8'}>
            {name}
          </Text>
          <Text size="xs" c="dimmed">
            {role}
            {' '}
            at
            {company}
          </Text>
        </Box>
      </Stack>
    </Card>
  );
}

// eslint-disable-next-line sonarjs/cognitive-complexity
export default function IndexPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const theme = useMantineTheme();
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === 'dark';
  const { t } = useTranslation('common');

  const handleGetStarted = () => {
    if (session) {
      const dashboardPath = getUserDashboardPath(session.user?.user_type);
      if (!session.user?.user_type) {
        router.push('/dashboard/customer');
        return;
      }
      router.push(dashboardPath);
    } else {
      router.push('/auth/login');
    }
  };

  if (status === 'loading') {
    return (
      <Center style={{ height: '100vh' }}>
        <Stack align="center" gap="md">
          <Loader size="xl" />
          <Text c="dimmed">{t('welcomeNaqalat')}</Text>
        </Stack>
      </Center>
    );
  }

  return (
    <Box>
      {/* HERO SECTION */}
      <Box
        style={{
          background: isDark
            ? `linear-gradient(135deg, ${theme.colors.dark[8]} 0%, ${theme.colors.dark[7]} 50%, ${theme.colors.blue[9]} 100%)`
            : `linear-gradient(135deg, ${theme.colors.blue[6]} 0%, ${theme.colors.cyan[5]} 50%, ${theme.colors.teal[4]} 100%)`,
          position: 'relative',
          overflow: 'hidden',
          minHeight: '100vh',
        }}
      >
        {/* Background Pattern */}
        <Box
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundImage: isDark
              ? 'radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%)'
              : 'radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%)',
            backgroundSize: '100px 100px',
          }}
        />

        {/* Header for non-authenticated users */}
        {!session && (
          <Container size="lg" style={{ position: 'relative', zIndex: 2 }}>
            <Group justify="space-between" py="md">
              <Title order={3} c={isDark ? 'gray.0' : 'white'}>
                NAQALAT
              </Title>
              <Group>
                <LanguageSwitcher />
              </Group>
            </Group>
          </Container>
        )}

        <Container size="lg" py="6rem" style={{ position: 'relative', zIndex: 1 }}>
          <Stack gap="xl" align="center">
            {/* Main Headline */}
            <Stack gap="md" align="center">
              <Badge
                size="lg"
                variant="light"
                color={isDark ? 'blue' : 'white'}
                radius="xl"
                style={{
                  background: isDark ? theme.colors.blue[9] : 'rgba(255,255,255,0.2)',
                  color: isDark ? theme.colors.blue[3] : theme.white,
                }}
              >
                {t('nextGenerationLogistics')}
              </Badge>

              <Title
                order={1}
                fw={900}
                fz={{ base: 36, sm: 48, lg: 56 }}
                ta="center"
                c={isDark ? 'gray.0' : 'white'}
                style={{
                  lineHeight: 1.2,
                }}
              >
                {session
                  ? t('welcomeBackUser', { email: session.user?.email })
                  : t('naqalatModernLogistics')}
              </Title>

              <Text
                size="xl"
                c={isDark ? 'gray.3' : 'blue.0'}
                maw={700}
                ta="center"
                lh={1.6}
              >
                {session
                  ? t('jumpBackToDashboard')
                  : t('firstMileToLastMile')}
              </Text>
            </Stack>

            {/* CTA Buttons */}
            <Group justify="center" gap="md" mt="xl">
              {session ? (
                <>
                  <Button
                    size="xl"
                    radius="xl"
                    onClick={handleGetStarted}
                    gradient={{ from: 'blue', to: 'cyan' }}
                    variant="gradient"
                    rightSection={<IconArrowRight size="1.2rem" />}
                    style={{ fontWeight: 600 }}
                  >
                    {t('goToDashboard')}
                  </Button>
                  <Button
                    variant="white"
                    size="xl"
                    radius="xl"
                    onClick={() => signOut({ callbackUrl: '/' })}
                    c={isDark ? 'dark.8' : 'blue.7'}
                    style={{ fontWeight: 600 }}
                  >
                    {t('logout')}
                  </Button>
                </>
              ) : (
                <>
                  <Button
                    size="xl"
                    radius="xl"
                    onClick={handleGetStarted}
                    gradient={{ from: 'blue', to: 'cyan' }}
                    variant="gradient"
                    rightSection={<IconArrowRight size="1.2rem" />}
                    style={{ fontWeight: 600 }}
                  >
                    {t('getStarted')}
                  </Button>
                  <Button
                    component={Link}
                    href="/auth/register"
                    variant="white"
                    size="xl"
                    radius="xl"
                    c={isDark ? 'dark.8' : 'blue.7'}
                    style={{ fontWeight: 600 }}
                  >
                    {t('register')}
                  </Button>
                </>
              )}
            </Group>

            {/* Stats Section */}
            <Box mt="xl" w="100%">
              <StatsSection />
            </Box>
          </Stack>
        </Container>
      </Box>

      {/* FEATURES SECTION */}
      <Container size="lg" py="6rem">
        <Stack gap="xl">
          <Box ta="center">
            <Badge size="lg" variant="light" color="blue" radius="xl" mb="md">
              ✨ Platform Features
            </Badge>
            <Title order={2} mb="md" c={isDark ? 'gray.1' : 'dark.8'}>
              Why Choose NAQALAT?
            </Title>
            <Text size="lg" c="dimmed" maw={600} mx="auto">
              Discover the powerful features that make NAQALAT the preferred choice for modern logistics operations.
            </Text>
          </Box>

          <SimpleGrid cols={{ base: 1, sm: 2, lg: 3 }} spacing="xl">
            <FeatureCard
              icon={IconTruck}
              title="Smart Fleet Management"
              description="Leverage our AI-optimized fleet and routing algorithms to ensure your shipments arrive on time with maximum efficiency and minimum cost."
              badge="AI-Powered"
              gradient={['blue', 'cyan']}
            />
            <FeatureCard
              icon={IconMapPin}
              title="Real-time Tracking"
              description="Stay informed with live GPS tracking, milestone notifications, and predictive delivery updates at every stage of the journey."
              badge="Live Updates"
              gradient={['green', 'teal']}
            />
            <FeatureCard
              icon={IconShieldCheck}
              title="Secure & Reliable"
              description="Your cargo's safety is our top priority with end-to-end insurance, blockchain verification, and robust quality assurance."
              badge="Blockchain Secured"
              gradient={['orange', 'red']}
            />
            <FeatureCard
              icon={IconBarcode}
              title="Smart Scanning"
              description="Streamline operations with QR code scanning, automated inventory management, and digital proof of delivery systems."
              badge="Digital First"
              gradient={['purple', 'grape']}
            />
            <FeatureCard
              icon={IconBell}
              title="Smart Notifications"
              description="Receive intelligent alerts, automated reports, and proactive notifications to stay ahead of potential issues."
              badge="Proactive"
              gradient={['yellow', 'orange']}
            />
            <FeatureCard
              icon={IconChartLine}
              title="Analytics Dashboard"
              description="Make data-driven decisions with comprehensive analytics, performance metrics, and predictive insights for your logistics operations."
              badge="Business Intelligence"
              gradient={['indigo', 'blue']}
            />
          </SimpleGrid>
        </Stack>
      </Container>

      {/* TESTIMONIALS SECTION */}
      <Box
        py="6rem"
      >
        <Container size="lg">
          <Stack gap="xl">
            <Box ta="center">
              <Badge size="lg" variant="light" color="yellow" radius="xl" mb="md">
                ⭐ Customer Success Stories
              </Badge>
              <Title order={2} mb="md" c={isDark ? 'gray.1' : 'red.8'}>
                Trusted by Industry Leaders
              </Title>
              <Text size="lg" c="dimmed" maw={600} mx="auto">
                See what our customers are saying about their experience with NAQALAT.
              </Text>
            </Box>

            <SimpleGrid cols={{ base: 1, md: 3 }} spacing="xl">
              <TestimonialCard
                name="Ahmed Al-Rashid"
                role="Supply Chain Manager"
                company="Saudi Retail Co."
                testimonial="NAQALAT transformed our logistics operations. The real-time tracking and AI-powered routing reduced our delivery times by 40% and significantly improved customer satisfaction."
                rating={5}
              />
              <TestimonialCard
                name="Fatima Hassan"
                role="Operations Director"
                company="Gulf Manufacturing"
                testimonial="The platform's reliability and customer support are exceptional. We've processed over 5,000 shipments without a single major issue. The analytics help us optimize our supply chain continuously."
                rating={5}
              />
              <TestimonialCard
                name="Mohammed bin Salman"
                role="CEO"
                company="Tech Logistics KSA"
                testimonial="As a logistics company ourselves, we appreciate NAQALAT's technical excellence. The API integration was seamless, and the blockchain security features give our clients peace of mind."
                rating={5}
              />
            </SimpleGrid>
          </Stack>
        </Container>
      </Box>

      {/* CTA SECTION */}
      <Container size="lg" py="6rem">
        <Paper
          p="4rem"
          radius="xl"
          style={{
            background: isDark
              ? `linear-gradient(135deg, ${theme.colors.blue[9]} 0%, ${theme.colors.cyan[9]} 100%)`
              : `linear-gradient(135deg, ${theme.colors.blue[6]} 0%, ${theme.colors.cyan[5]} 100%)`,
            textAlign: 'center',
            position: 'relative',
            overflow: 'hidden',
          }}
        >
          {/* Background decoration */}
          <Box
            style={{
              position: 'absolute',
              top: '-50%',
              right: '-25%',
              width: '150%',
              height: '150%',
              background: 'radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%)',
              borderRadius: '50%',
            }}
          />

          <Stack gap="xl" align="center" style={{ position: 'relative', zIndex: 1 }}>
            <ThemeIcon size={80} radius="xl" variant="white" c="blue.6">
              <IconGlobe size="2.5rem" />
            </ThemeIcon>

            <Box>
              <Title order={2} c="white" mb="md">
                Ready to Transform Your Logistics?
              </Title>
              <Text size="xl" c="blue.0" maw={600} mx="auto">
                Join thousands of businesses that trust NAQALAT for their shipping and logistics needs. Start your journey today.
              </Text>
            </Box>

            <Group gap="md">
              <Button
                size="xl"
                variant="white"
                radius="xl"
                onClick={handleGetStarted}
                c="blue.7"
                rightSection={<IconArrowRight size="1.2rem" />}
                style={{ fontWeight: 600 }}
              >
                Start Free Trial
              </Button>
              <Button
                size="xl"
                variant="outline"
                radius="xl"
                c="white"
                style={{
                  borderColor: 'rgba(255,255,255,0.3)',
                  fontWeight: 600,
                }}
                leftSection={<IconPhone size="1.2rem" />}
              >
                Contact Sales
              </Button>
            </Group>
          </Stack>
        </Paper>
      </Container>

      {/* FOOTER */}
      <Box
        py="4rem"
        style={{
          background: isDark ? theme.colors.dark[9] : theme.colors.gray[9],
          borderTop: `1px solid ${isDark ? theme.colors.dark[6] : theme.colors.gray[7]}`,
        }}
      >
        <Container size="lg">
          <SimpleGrid cols={{ base: 1, sm: 4 }} spacing="xl">
            {/* Company Info */}
            <Stack gap="md">
              <Group gap="xs">
                <ThemeIcon size="lg" variant="gradient" gradient={{ from: 'blue', to: 'cyan' }} radius="md">
                  <IconTruck size="1.5rem" />
                </ThemeIcon>
                <Text fw={700} size="xl" c={isDark ? 'gray.1' : 'gray.0'}>
                  NAQALAT
                </Text>
              </Group>
              <Text size="sm" c="dimmed" lh={1.6}>
                Your trusted partner in efficient transport solutions. Connecting businesses across the region with reliable, technology-driven logistics.
              </Text>
            </Stack>

            {/* Quick Links */}
            <Stack gap="md">
              <Text fw={600} c={isDark ? 'gray.2' : 'gray.1'}>
                Quick Links
              </Text>
              <Stack gap="xs">
                <Anchor size="sm" c="dimmed" component={Link} href="/features">Features</Anchor>
                <Anchor size="sm" c="dimmed" component={Link} href="/pricing">Pricing</Anchor>
                <Anchor size="sm" c="dimmed" component={Link} href="/about">About Us</Anchor>
                <Anchor size="sm" c="dimmed" component={Link} href="/contact">Contact</Anchor>
              </Stack>
            </Stack>

            {/* Support */}
            <Stack gap="md">
              <Text fw={600} c={isDark ? 'gray.2' : 'gray.1'}>
                Support
              </Text>
              <Stack gap="xs">
                <Anchor size="sm" c="dimmed" component={Link} href="/help">Help Center</Anchor>
                <Anchor size="sm" c="dimmed" component={Link} href="/docs">Documentation</Anchor>
                <Anchor size="sm" c="dimmed" component={Link} href="/status">System Status</Anchor>
                <Anchor size="sm" c="dimmed" component={Link} href="/api">API Reference</Anchor>
              </Stack>
            </Stack>

            {/* Contact Info */}
            <Stack gap="md">
              <Text fw={600} c={isDark ? 'gray.2' : 'gray.1'}>
                Contact Info
              </Text>
              <Stack gap="xs">
                <Group gap="xs">
                  <IconMapPinFilled size="1rem" style={{ color: theme.colors.gray[6] }} />
                  <Text size="sm" c="dimmed">Riyadh, Saudi Arabia</Text>
                </Group>
                <Group gap="xs">
                  <IconPhone size="1rem" style={{ color: theme.colors.gray[6] }} />
                  <Text size="sm" c="dimmed">+966 11 234 5678</Text>
                </Group>
                <Group gap="xs">
                  <IconMail size="1rem" style={{ color: theme.colors.gray[6] }} />
                  <Text size="sm" c="dimmed"><EMAIL></Text>
                </Group>
              </Stack>
            </Stack>
          </SimpleGrid>

          <Divider my="xl" color={isDark ? 'dark.6' : 'gray.7'} />

          <Group justify="space-between" align="center">
            <Text c="dimmed" size="sm">
              NAQALAT ©
              {' '}
              {new Date().getFullYear()}
              {' '}
              – All rights reserved.
            </Text>
            <Group gap="md">
              <Anchor size="sm" c="dimmed" component={Link} href="/privacy">Privacy Policy</Anchor>
              <Anchor size="sm" c="dimmed" component={Link} href="/terms">Terms of Service</Anchor>
            </Group>
          </Group>
        </Container>
      </Box>
    </Box>
  );
}
