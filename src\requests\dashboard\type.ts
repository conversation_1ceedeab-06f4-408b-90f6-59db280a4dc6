import { z } from 'zod';
import { DashboardResponseSchema } from './respons-transformer';

export type DashboardApiResponse = z.infer<typeof DashboardResponseSchema>;

// Recent shipment common structure with optional fields for different roles
export interface RecentShipment {
  id: string;
  trackingCode: string;
  status: string;
  createdAt: string;
  description?: string | null;
  originAO?: { businessName: string; address: string };
  destAO?: { businessName: string; address: string };
}

// Shipment stats per role
export interface ShipmentStatsCustomer {
  total?: number;
  pending?: number;
  awaitingPickup?: number;
  inTransit?: number;
  arrivedAtDestination?: number;
  delivered?: number;
  cancelled?: number;
}

export interface ShipmentStatsAccessOperator {
  pending?: number;
  awaitingPickup?: number;
  inTransit?: number;
  delivered?: number;
}

export interface ShipmentStatsCarOperator {
  assigned?: number;
  completed?: number;
  completionRate?: number; // 0–100
}

// Dashboard data objects by role
export interface CustomerDashboardData {
  shipmentStats?: ShipmentStatsCustomer;
  recentShipments?: RecentShipment[];
}

export interface AccessOperatorDashboardData {
  shipmentStats?: ShipmentStatsAccessOperator;
  recentShipments?: RecentShipment[];
}

export interface CarOperatorDashboardData {
  shipmentStats?: ShipmentStatsCarOperator;
  recentShipments?: RecentShipment[];
}
