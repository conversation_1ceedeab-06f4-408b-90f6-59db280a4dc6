import { Session } from 'next-auth';
import { UseFormReturnType } from '@mantine/form';
import type { ProfileFormValues, GeoLocation } from '../types';
import { useUpdateProfileMutation } from '../../../requests/hooks/enhanced-mutations';

interface UseProfileSubmitProps {
  session?: Session | null;
  geoLocation: GeoLocation;
  transitPointIds: string[];
  onSuccess?: () => void;
  form: UseFormReturnType<ProfileFormValues>; // ← Added form reference
  successMessage?: string;
}

// Helper function to add transit points data
const addTransitPointsData = (cleanedValues: Record<string, unknown>, transitPointIds: string[]) => {
  const updatedValues = { ...cleanedValues };

  if (transitPointIds.length === 0) {
    updatedValues.pickupAccessPointId = '';
    updatedValues.dropoffAccessPointId = '';
  } else if (transitPointIds.length === 1) {
    const pointId = transitPointIds[0];
    if (pointId?.trim()) {
      updatedValues.pickupAccessPointId = pointId;
      updatedValues.dropoffAccessPointId = pointId;
    } else {
      updatedValues.pickupAccessPointId = '';
      updatedValues.dropoffAccessPointId = '';
    }
  } else {
    const [pickupId, dropoffId] = transitPointIds;
    updatedValues.pickupAccessPointId = pickupId?.trim() || '';
    updatedValues.dropoffAccessPointId = dropoffId?.trim() || '';
  }

  return updatedValues;
};

// Helper function to prepare form data
const prepareFormData = (
  values: ProfileFormValues,
  session?: Session | null,
  geoLocation?: GeoLocation,
  transitPointIds?: string[],
) => {
  // Filter out undefined values and empty strings (except for transit point fields)
  const cleanedValues = Object.entries(values).reduce((acc, [key, value]) => {
    const isTransitPointField = key === 'pickupAccessPointId' || key === 'dropoffAccessPointId';
    if (value !== undefined && (value !== '' || isTransitPointField)) {
      acc[key] = value;
    }
    return acc;
  }, {} as Record<string, unknown>);

  let result = { ...cleanedValues };

  // Get user type from session or form values
  const userType = (session?.user as { user_type?: string; userType?: string })?.user_type
                   || (session?.user as { user_type?: string; userType?: string })?.userType;

  // Add geo-location data for ACCESS_OPERATOR users
  if (userType === 'ACCESS_OPERATOR' && geoLocation?.lat && geoLocation?.lng) {
    result = {
      ...result,
      geoLatitude: geoLocation.lat,
      geoLongitude: geoLocation.lng,
    };
  }

  // Add transit points data for CAR_OPERATOR users
  if (userType === 'CAR_OPERATOR' && transitPointIds) {
    result = addTransitPointsData(result, transitPointIds);
  }

  return result;
};

export const useProfileSubmit = ({
  session,
  geoLocation,
  transitPointIds,
  onSuccess,
  form, // ← Added form parameter
  successMessage,
}: UseProfileSubmitProps) => {
  // Update profile mutation with auto-invalidation
  const updateProfileMutation = useUpdateProfileMutation({
    successMessage,
    onSuccess: () => {
      onSuccess?.();
    },
  });

  // Enhanced form submission handler that prevents default behavior
  const handleFormSubmit = (event: React.FormEvent) => {
    event.preventDefault(); // Prevent default form submission
    event.stopPropagation(); // Stop event bubbling

    // Trigger form validation
    const validation = form.validate();

    if (!validation.hasErrors) {
      // If validation passes, prepare and submit data
      const cleanedValues = prepareFormData(form.values, session, geoLocation, transitPointIds);
      updateProfileMutation.mutate({ data: cleanedValues });
    } else {
      //
    }
  };

  // Keep the original handleSubmit for backward compatibility if needed
  const handleSubmit = (values: ProfileFormValues) => {
    const cleanedValues = prepareFormData(values, session, geoLocation, transitPointIds);
    updateProfileMutation.mutate({ data: cleanedValues });
  };

  return {
    handleSubmit, // ← Original handler for direct use
    handleFormSubmit, // ← New event handler for form onSubmit
    isLoading: updateProfileMutation.isPending,
  };
};
