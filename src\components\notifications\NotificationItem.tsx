/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable no-nested-ternary */
/* eslint-disable complexity */
/* eslint-disable react/require-default-props */
import {
  Group,
  Text,
  ActionIcon,
  Box,
  Badge,
  UnstyledButton,
  useMantineTheme,
  useMantineColorScheme,
  Tooltip,
} from '@mantine/core';
import {
  IconCheck,
  IconAlertCircle,
  IconInfoCircle,
  IconCircleCheck,
} from '@tabler/icons-react';
import { useIsClient } from '../../hooks/useIsClient';
import type { Notification } from '../../requests/notifications';
import useTranslation from 'next-translate/useTranslation';

interface NotificationItemProps {
  notification: Notification;
  onMarkAsRead?: (id: string) => void;
  onClick?: () => void;
  compact?: boolean;
}

const getNotificationIcon = (type: string) => {
  switch (type) {
    case 'SUCCESS':
      return IconCircleCheck;
    case 'WARNING':
      return IconAlertCircle;
    case 'ERROR':
      return IconAlertCircle;
    case 'INFO':
    default:
      return IconInfoCircle;
  }
};

const getNotificationColor = (type: string) => {
  switch (type) {
    case 'SUCCESS':
      return 'green';
    case 'WARNING':
      return 'orange';
    case 'ERROR':
      return 'red';
    case 'INFO':
    default:
      return 'blue';
  }
};

export default function NotificationItem({
  notification,
  onMarkAsRead,
  onClick,
  compact = false,
}: NotificationItemProps) {
  const { t } = useTranslation('notifications');
  const isClient = useIsClient();
  const theme = useMantineTheme();
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === 'dark';

  // Title mappings for specific notification titles
  const titleMappings: Record<string, string> = {
    'Account Deactivated': 'types.account_deactivated.title',
    'Account Activated': 'types.account_activated.title',
    'Account Suspended': 'types.account_suspended.title',
    'Shipment Created': 'types.shipment_created.title',
    'QR Code Assigned': 'types.qr_code_assigned.title',
    'Package Dropped Off': 'types.shipment_dropped_off.title',
    'Package Picked Up': 'types.shipment_picked_up.title',
    'Package Arrived': 'types.shipment_arrived.title',
    'Package Delivered': 'types.shipment_delivered.title',
    'Shipment Cancelled': 'types.shipment_cancelled.title',
    'Shipment Expired': 'types.shipment_expired.title',
    'Ready for Pickup': 'types.shipment_ready_for_delivery.title',
    'Package In Transit': 'types.shipment_in_transit.title',
    'Status Updated': 'types.shipment_status_changed.title',
  };

  // Function to get translated title
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const getTranslatedTitle = (notification: any) => {
    // Try specific title mapping first
    if (notification.title && titleMappings[notification.title]) {
      const mappedTranslation = t(titleMappings[notification.title]);
      if (mappedTranslation && mappedTranslation !== titleMappings[notification.title]) {
        return mappedTranslation;
      }
    }

    // Fallback to type-based translation
    const typeKey = notification.notification_type?.toLowerCase() || 'default';
    const titleKey = `types.${typeKey}.title`;
    const translatedTitle = t(titleKey);

    if (translatedTitle && translatedTitle !== titleKey) {
      return translatedTitle;
    }

    return notification.title || t('types.default.title');
  };

  // Function to get notification content with smart translation
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const getTranslatedContent = (notification: any) => {
    const finalTitle = getTranslatedTitle(notification);

    // For message: always prefer backend message (contains dynamic content)
    let finalMessage = notification.message;
    if (!finalMessage) {
      const typeKey = notification.notification_type?.toLowerCase() || 'default';
      const messageKey = `types.${typeKey}.message`;
      const translatedMessage = t(messageKey);

      finalMessage = (translatedMessage && translatedMessage !== messageKey)
        ? translatedMessage
        : t('types.default.message');
    }

    return {
      title: finalTitle,
      message: finalMessage,
    };
  };

  const { title, message } = getTranslatedContent(notification);

  const IconComponent = getNotificationIcon(notification.notification_type);
  const iconColor = getNotificationColor(notification.notification_type);

  const formatDate = (dateString: string) => {
    if (!isClient) return 'Loading...';
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch {
      return 'Invalid date';
    }
  };

  const handleClick = () => {
    if (!notification.read && onMarkAsRead) {
      onMarkAsRead(notification.id);
    }
    if (onClick) {
      onClick();
    }
  };

  const handleMarkAsRead = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onMarkAsRead) {
      onMarkAsRead(notification.id);
    }
  };

  return (
    <UnstyledButton
      onClick={handleClick}
      style={{
        width: '100%',
        padding: compact ? theme.spacing.xs : theme.spacing.md,
        backgroundColor: notification.read
          ? 'transparent'
          : isDark
            ? theme.colors.dark[6]
            : theme.colors.gray[0],
        borderBottom: `1px solid ${isDark ? theme.colors.dark[4] : theme.colors.gray[2]}`,
        transition: 'background-color 0.2s ease',
      }}
      __vars={{
        '--button-hover': isDark ? theme.colors.dark[5] : theme.colors.gray[1],
      }}
    >
      <Group gap="sm" align="flex-start" wrap="nowrap">
        <IconComponent
          size={compact ? '1rem' : '1.2rem'}
          color={theme.colors[iconColor][6]}
          style={{ marginTop: '2px', flexShrink: 0 }}
        />

        <Box style={{ flex: 1, minWidth: 0 }}>
          <Group gap="xs" justify="space-between" align="flex-start" wrap="nowrap">
            <Box style={{ flex: 1, minWidth: 0 }}>
              <Text
                fw={notification.read ? 400 : 600}
                size={compact ? 'xs' : 'sm'}
                lineClamp={compact ? 1 : 2}
                c={isDark ? 'gray.1' : 'dark.7'}
              >
                {title}
              </Text>
              {!compact && message && (
                <Text
                  size="xs"
                  c="dimmed"
                  lineClamp={2}
                  mt={2}
                >
                  {message}
                </Text>
              )}
            </Box>

            <Group gap="xs" align="center" style={{ flexShrink: 0 }}>
              {!notification.read && (
                <Badge
                  size="xs"
                  variant="filled"
                  color="blue"
                  style={{ flexShrink: 0 }}
                >
                  {t('new')}
                </Badge>
              )}
              {!notification.read && onMarkAsRead && (
                <Tooltip label={t('markAsRead')} position="top">
                  <ActionIcon
                    size="md"
                    variant="subtle"
                    color="gray"
                    onClick={handleMarkAsRead}
                    aria-label={t('markAsRead')}
                    style={{ padding: '4px' }}
                  >
                    <IconCheck size="1rem" />
                  </ActionIcon>
                </Tooltip>
              )}
            </Group>
          </Group>

          <Text
            size="xs"
            c="dimmed"
            mt={compact ? 2 : 4}
          >
            {formatDate(notification.created_at)}
          </Text>
        </Box>
      </Group>
    </UnstyledButton>
  );
}
