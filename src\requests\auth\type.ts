import z from 'zod';
import {
  changePasswordSchema,
  forgotPasswordSchema,
  loginSchema, registrationSchema,
  resendVerificationOtpSchema,
  resetPasswordSchema, sendVerificationOtpSchema,
  verifyEmailOtpSchema,
  verifyEmailSchema,
} from './request-transform';
import {
  authResponseDataSchema,
  changePasswordApiResponseSchema,
  forgotPasswordApiResponseSchema,
  loginApiResponseSchema,
  registerApiResponseSchema, resendVerificationOtpApiResponseSchema,
  resetPasswordApiResponseSchema, sendVerificationOtpApiResponseSchema,
  userSchema, verifyEmailOtpApiResponseSchema,
  verifyResetTokenApiResponseSchema,
  emailSchema,
  verifySchema,
} from './response-transform';

// Infer types from Zod schemas
export type User = z.infer<typeof userSchema>;

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface RegisterResponse {
  message: string;
}

export interface AuthParams {
  enabled?: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onSuccess?: (data: any) => void;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onError?: (error: any) => void;
}

export interface EmailVerificationParams {
  token: string;
}

export interface PasswordResetParams {
  token: string;
  password: string;
  confirmPassword: string;
}
export interface RegisterRequest {
  email: string;
  password: string;
}

// Infer types from Zod schemas
export type AuthResponseData = z.infer<typeof authResponseDataSchema>;

// Request types
export type LoginRequest = z.infer<typeof loginSchema>;
export type RegistrationRequest = z.infer<typeof registrationSchema>;
export type ForgotPasswordRequest = z.infer<typeof forgotPasswordSchema>;
export type ResetPasswordRequest = z.infer<typeof resetPasswordSchema>;
export type ChangePasswordRequest = z.infer<typeof changePasswordSchema>;
export type VerifyEmailRequest = z.infer<typeof verifyEmailSchema>;
export type SendVerificationOtpRequest = z.infer<typeof sendVerificationOtpSchema>;
export type VerifyEmailOtpRequest = z.infer<typeof verifyEmailOtpSchema>;
export type ResendVerificationOtpRequest = z.infer<typeof resendVerificationOtpSchema>;

// Response types
export type LoginApiResponse = z.infer<typeof loginApiResponseSchema>;
export type RegisterApiResponse = z.infer<typeof registerApiResponseSchema>;
export type ForgotPasswordApiResponse = z.infer<typeof forgotPasswordApiResponseSchema>;
export type VerifyResetTokenApiResponse = z.infer<typeof verifyResetTokenApiResponseSchema>;
export type ResetPasswordApiResponse = z.infer<typeof resetPasswordApiResponseSchema>;
export type ChangePasswordApiResponse = z.infer<typeof changePasswordApiResponseSchema>;
export type SendVerificationOtpApiResponse = z.infer<typeof sendVerificationOtpApiResponseSchema>;
export type VerifyEmailOtpApiResponse = z.infer<typeof verifyEmailOtpApiResponseSchema>;
export type ResendVerificationOtpApiResponse = z.infer<typeof resendVerificationOtpApiResponseSchema>;

// Query props interfaces
export interface LoginQueryProps {
  data: LoginRequest;
  enabled?: boolean;
}

export interface RegisterQueryProps {
  data: RegistrationRequest;
  enabled?: boolean;
}

export interface ForgotPasswordQueryProps {
  data: ForgotPasswordRequest;
  enabled?: boolean;
}

export interface VerifyResetTokenQueryProps {
  token: string;
  email: string;
  enabled?: boolean;
}

export interface ResetPasswordQueryProps {
  data: ResetPasswordRequest;
  enabled?: boolean;
}

export interface ChangePasswordQueryProps {
  data: ChangePasswordRequest;
  enabled?: boolean;
}

export interface SendVerificationOtpQueryProps {
  data: SendVerificationOtpRequest;
  enabled?: boolean;
}

export interface VerifyEmailOtpQueryProps {
  data: VerifyEmailOtpRequest;
  enabled?: boolean;
}

export interface ResendVerificationOtpQueryProps {
  data: ResendVerificationOtpRequest;
  enabled?: boolean;
}

export type VerifyFormValues = z.infer<typeof verifySchema>;
export type EmailFormValues = z.infer<typeof emailSchema>;
