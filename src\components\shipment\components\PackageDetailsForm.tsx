import {
  Group, NumberInput, Select, Textarea,
} from '@mantine/core';
import { UseFormReturnType } from '@mantine/form';
import useTranslation from 'next-translate/useTranslation';
import { SHIPMENT_SIZE } from '../../../requests/shipment';

interface FormValues {
  originAoId: string;
  destAoId: string;
  weight: number;
  size: string;
  description: string;
  receiverName: string;
  receiverPhone: string;
}

interface PackageDetailsFormProps {
  form: UseFormReturnType<FormValues>;
}

export function PackageDetailsForm({ form }: PackageDetailsFormProps) {
  const { t } = useTranslation('shipments');

  const sizeOptions = SHIPMENT_SIZE.map((size) => ({
    value: size,
    label: t(`size${size.charAt(0) + size.slice(1).toLowerCase().replace('_', '')}`),
  }));

  return (
    <>
      {/* Package Details */}
      <Group grow>
        <NumberInput
          label={t('weightKg')}
          placeholder={t('enterWeight')}
          min={0.1}
          max={1000}
          step={0.1}
          decimalScale={1}
          required
          {...form.getInputProps('weight')}
        />
        <Select
          label={t('size')}
          placeholder={t('selectPackageSize')}
          data={sizeOptions}
          required
          {...form.getInputProps('size')}
        />
      </Group>

      {/* Description */}
      <Textarea
        label={t('description')}
        placeholder={t('describeContents')}
        minRows={3}
        maxRows={5}
        required
        {...form.getInputProps('description')}
      />
    </>
  );
}
