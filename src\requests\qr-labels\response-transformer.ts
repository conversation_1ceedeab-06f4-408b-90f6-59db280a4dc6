import { z } from 'zod';

// Backend Response Schemas (snake_case)
const qrLabelBackendSchema = z.object({
  id: z.string(),
  qr_value: z.string(),
  status: z.enum(['AVAILABLE', 'ASSIGNED', 'USED']),
  shipment_id: z.string().nullable().optional(),
  assigned_at: z.string().nullable().optional(),
  used_at: z.string().nullable().optional(),
  created_at: z.string(),
  updated_at: z.string(),
});

const generateForShipmentBackendResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    qr_label: qrLabelBackendSchema,
  }),
});

const generateBulkBackendResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    qr_labels: z.array(qrLabelBackendSchema),
    count: z.number(),
  }),
});

const getQRLabelsBackendResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    qr_labels: z.array(qrLabelBackendSchema),
    pagination: z.object({
      page: z.number(),
      limit: z.number(),
      total: z.number(),
      total_pages: z.number(),
    }),
  }),
});

const getQRLabelBackendResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    qr_label: qrLabelBackendSchema,
  }),
});

const generatePDFBackendResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    pdf_url: z.string(),
  }),
});

// Frontend Response Schemas (camelCase)
const qrLabelFrontendSchema = z.object({
  id: z.string(),
  qrValue: z.string(),
  status: z.enum(['AVAILABLE', 'ASSIGNED', 'USED']),
  shipmentId: z.string().optional(),
  assignedAt: z.string().optional(),
  usedAt: z.string().optional(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const transformQRLabel = (backendQRLabel: any) => ({
  id: backendQRLabel.id,
  qrValue: backendQRLabel.qr_value ?? backendQRLabel.qrValue ?? '',
  status: backendQRLabel.status,
  shipmentId: backendQRLabel.shipment_id ?? backendQRLabel.shipmentId ?? undefined,
  assignedAt: backendQRLabel.assigned_at ?? backendQRLabel.assignedAt ?? undefined,
  usedAt: backendQRLabel.used_at ?? backendQRLabel.usedAt ?? undefined,
  createdAt: backendQRLabel.created_at ?? backendQRLabel.createdAt ?? '',
  updatedAt: backendQRLabel.updated_at ?? backendQRLabel.updatedAt ?? '',
});

export const transformGenerateForShipmentResponse = (backendResponse: z.infer<typeof generateForShipmentBackendResponseSchema>) => ({
  success: backendResponse.success,
  message: backendResponse.message,
  data: {
    qrLabel: transformQRLabel(backendResponse.data.qr_label),
  },
});

export const transformGenerateBulkResponse = (backendResponse: z.infer<typeof generateBulkBackendResponseSchema>) => ({
  success: backendResponse.success,
  message: backendResponse.message,
  data: {
    qrLabels: backendResponse.data.qr_labels.map(transformQRLabel),
    count: backendResponse.data.count,
  },
});

export const transformGetQRLabelsResponse = (backendResponse: z.infer<typeof getQRLabelsBackendResponseSchema>) => ({
  success: backendResponse.success,
  message: backendResponse.message,
  data: {
    qrLabels: backendResponse.data.qr_labels.map(transformQRLabel),
    pagination: {
      page: backendResponse.data.pagination.page,
      limit: backendResponse.data.pagination.limit,
      total: backendResponse.data.pagination.total,
      totalPages: backendResponse.data.pagination.total_pages,
    },
  },
});

export const transformGetQRLabelResponse = (
  backendResponse: z.infer<typeof getQRLabelBackendResponseSchema>,
) => {
  if (backendResponse.success) {
    return {
      success: true,
      message: backendResponse.message,
      data: {
        qrLabel: transformQRLabel(backendResponse.data.qr_label),
      },
    };
  }
  return {
    success: false,
    message: backendResponse.message,
    data: {},
  };
};

export const transformGeneratePDFResponse = (backendResponse: z.infer<typeof generatePDFBackendResponseSchema>) => ({
  success: backendResponse.success,
  message: backendResponse.message,
  data: {
    pdfUrl: backendResponse.data.pdf_url,
  },
});

export const qrLabelResponseSchemas = {
  generateForShipmentBackend: generateForShipmentBackendResponseSchema,
  generateBulkBackend: generateBulkBackendResponseSchema,
  getQRLabelsBackend: getQRLabelsBackendResponseSchema,
  getQRLabelBackend: getQRLabelBackendResponseSchema,
  qrLabelBackend: qrLabelBackendSchema,
  qrLabelFrontend: qrLabelFrontendSchema,
  generatePDFBackend: generatePDFBackendResponseSchema,
};

export const qrLabelResponseTransformers = {
  generateForShipment: transformGenerateForShipmentResponse,
  generateBulk: transformGenerateBulkResponse,
  getQRLabels: transformGetQRLabelsResponse,
  getQRLabel: transformGetQRLabelResponse,
  generatePDF: transformGeneratePDFResponse,
};
