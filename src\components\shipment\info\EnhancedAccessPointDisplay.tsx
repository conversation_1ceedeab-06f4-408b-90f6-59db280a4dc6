/* eslint-disable sonarjs/no-all-duplicated-branches */
/* eslint-disable no-nested-ternary */
/* eslint-disable react/require-default-props */
import React from 'react';
import {
  Stack,
  Text,
  Group,
  Badge,
  Alert,
  Loader,
  Card,
  Divider,
  useMantineTheme,
  useMantineColorScheme,
} from '@mantine/core';
import {
  IconTruck,
  IconFlag,
  IconMapPin,
  IconPhone,
  IconMail,
  IconClock,
  IconBuilding,
  IconCheck,
  IconAlertTriangle,
} from '@tabler/icons-react';
import useTranslation from 'next-translate/useTranslation';
import { AccessPoint } from '../../../requests/access-point';

interface EnhancedAccessPointDisplayProps {
  accessPoint: AccessPoint | null;
  isLoading: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  error: any;
  type: 'origin' | 'destination';
  variant?: 'default' | 'compact' | 'detailed';
  showStatus?: boolean;
  showOperatingHours?: boolean;
}

// eslint-disable-next-line complexity, sonarjs/cognitive-complexity
export default function EnhancedAccessPointDisplay({
  accessPoint,
  isLoading,
  error,
  type,
  variant = 'default',
  showStatus = true,
  showOperatingHours = true,
}: EnhancedAccessPointDisplayProps) {
  const { t } = useTranslation('shipments');
  const theme = useMantineTheme();
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === 'dark';

  const isOrigin = type === 'origin';
  const icon = isOrigin ? <IconTruck size="1.2rem" color="green" /> : <IconFlag size="1.2rem" color="red" />;
  const title = isOrigin ? t('originAccessPoint') : t('destinationAccessPoint');
  const subtitle = isOrigin ? t('packageDropoffLocation') : t('packagePickupLocation');
  const badgeColor = isOrigin ? 'green' : 'red';
  const borderColor = isOrigin ? theme.colors.green[4] : theme.colors.red[4];
  const bgColor = isDark
    ? (isOrigin ? theme.colors.dark[7] : theme.colors.dark[7])
    : (isOrigin ? theme.colors.green[0] : theme.colors.red[0]);

  if (isLoading) {
    return (
      <Card withBorder p="md" radius="md" style={{ minHeight: variant === 'compact' ? '100px' : '160px' }}>
        <Stack gap="xs" align="center" justify="center" h="100%">
          <Group gap="xs">
            {icon}
            <Text size="sm" fw={600} c="dimmed">{title}</Text>
          </Group>
          <Loader size="sm" />
          <Text size="xs" c="dimmed">Loading access point details...</Text>
        </Stack>
      </Card>
    );
  }

  if (error || !accessPoint) {
    return (
      <Card withBorder p="md" radius="md" style={{ minHeight: variant === 'compact' ? '100px' : '160px' }}>
        <Stack gap="xs">
          <Group gap="xs">
            {icon}
            <Text size="sm" fw={600} c="dimmed">{title}</Text>
          </Group>
          <Alert color="red" variant="light" icon={<IconAlertTriangle size="1rem" />}>
            <Text size="xs">Failed to load access point details</Text>
          </Alert>
        </Stack>
      </Card>
    );
  }

  if (variant === 'compact') {
    return (
      <Card
        withBorder
        p="sm"
        radius="md"
        style={{
          borderColor,
          backgroundColor: bgColor,
        }}
      >
        <Group gap="xs" justify="space-between">
          <Group gap="xs">
            {icon}
            <div>
              <Text fw={600} size="sm" lineClamp={1}>
                {accessPoint.businessName || accessPoint.name}
              </Text>
              <Text size="xs" c="dimmed">
                {subtitle}
              </Text>
            </div>
          </Group>
          <Badge size="xs" color={badgeColor} variant="filled">
            {isOrigin ? t('from') : t('to')}
          </Badge>
        </Group>
      </Card>
    );
  }

  return (
    <Card
      withBorder
      p="md"
      radius="md"
      style={{
        borderColor,
        backgroundColor: bgColor,
        minHeight: variant === 'detailed' ? '200px' : '160px',
      }}
    >
      <Stack gap="sm">
        {/* Header */}
        <Group gap="xs" justify="space-between">
          <Group gap="xs">
            {icon}
            <div>
              <Text size="sm" fw={600} c={isOrigin ? 'green.8' : 'red.8'}>
                {title}
              </Text>
              <Text size="xs" c="dimmed" fw={500}>
                {subtitle}
              </Text>
            </div>
          </Group>
          <Badge size="sm" color={badgeColor} variant="filled" radius="md">
            {isOrigin ? t('origin').toUpperCase() : t('destination').toUpperCase()}
          </Badge>
        </Group>

        <Divider />

        {/* Business Information */}
        <Stack gap="xs">
          <Group gap="xs">
            <IconBuilding size="0.8rem" color="gray" />
            <Text fw={600} size="sm" c="dark">
              {accessPoint.businessName || accessPoint.name}
            </Text>
          </Group>

          {accessPoint.address && (
            <Group gap="xs" align="flex-start">
              <IconMapPin size="0.8rem" color="gray" style={{ marginTop: '2px' }} />
              <Text size="xs" c="dimmed" style={{ lineHeight: 1.4 }}>
                {accessPoint.address}
              </Text>
            </Group>
          )}
        </Stack>

        {/* Contact Information */}
        {(accessPoint.phone || accessPoint.email) && (
          <>
            <Divider />
            <Stack gap="xs">
              <Text size="xs" fw={600} c="dimmed">{t('contactInformation')}</Text>
              {accessPoint.phone && (
                <Group gap="xs">
                  <IconPhone size="0.8rem" color="blue" />
                  <Text size="xs" c="blue" fw={500} style={{ fontFamily: 'monospace' }}>
                    {accessPoint.phone}
                  </Text>
                </Group>
              )}
              {accessPoint.email && (
                <Group gap="xs">
                  <IconMail size="0.8rem" color="blue" />
                  <Text size="xs" c="blue" fw={500} style={{ wordBreak: 'break-all' }}>
                    {accessPoint.email}
                  </Text>
                </Group>
              )}
            </Stack>
          </>
        )}

        {/* Service Information */}
        {showOperatingHours && (
          <>
            <Divider />
            <Group gap="xs">
              <IconClock size="0.7rem" color="orange" />
              <Text size="xs" c="dimmed" style={{ fontStyle: 'italic' }}>
                {t('operatingHours')}
                :
                {t('operatingHoursTime')}
              </Text>
            </Group>
          </>
        )}

        {/* Status */}
        {showStatus && (
          <Group justify="space-between" align="center" mt="xs">
            <Group gap="xs">
              <IconCheck size="0.7rem" color="green" />
              <Text size="xs" c="green" fw={500}>
                {isOrigin ? t('availableForDropoff') : t('availableForPickup')}
              </Text>
            </Group>
            <Badge
              size="xs"
              color={accessPoint.status === 'ACTIVE' ? 'green' : 'orange'}
              variant="light"
              radius="sm"
            >
              {accessPoint.status === 'ACTIVE' ? t('active') : accessPoint.status}
            </Badge>
          </Group>
        )}
      </Stack>
    </Card>
  );
}
