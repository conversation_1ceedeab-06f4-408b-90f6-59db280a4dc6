# Naqalat Shipment Relay Platform API Documentation

## Base URL
```
http://localhost:8000/api
```

## Authentication
All protected endpoints require a JWT token in the Authorization header:
```
Authorization: Bearer <jwt_token>
```

## User Types
- `CUSTOMER`: End users who create shipments
- `ACCESS_OPERATOR`: Operators who manage access points
- `CAR_OPERATOR`: Drivers who transport shipments
- `ADMIN`: System administrators

## Response Format
All API responses follow this format:
```json
{
  "success": boolean,
  "message": "string",
  "data": object,
  "error": {
    "type": "string",
    "details": "string"
  }
}
```

---

## Authentication Endpoints

### Register User
**POST** `/auth/register`

Register a new user with basic information only. Users complete their profile after email verification and login.

**Request Body:**
```json
{
  "user_type": "CUSTOMER|ACCESS_OPERATOR|CAR_OPERATOR",
  "name": "string",
  "email": "string",
  "password": "string",
  "phone": "string"
}
```

**Response:**
```json
{
  "success": true,
  "message": "User registered successfully. Please check your email to verify your account, then complete your profile after logging in."
}
```

**Note:** Only basic information is required during registration. Users can complete their profile with additional details through the profile update endpoint after logging in:
- **Access Operators**: Add business name, address, and geo coordinates
- **Car Operators**: Add license number, vehicle info, and access point selections

### Login
**POST** `/auth/login`

Authenticate user and receive JWT token.

**Request Body:**
```json
{
  "email": "string",
  "password": "string"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": "uuid",
      "name": "string",
      "email": "string",
      "user_type": "string",
      "status": "string"
    },
    "token": "jwt_token"
  }
}
```

**Error Responses:**
- `401`: Unauthorized - Invalid credentials
- `403`: Forbidden - Email not verified or account not active

### Forgot Password
**POST** `/auth/forgot-password`

Request password reset email.

**Request Body:**
```json
{
  "email": "string"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Password reset email sent"
}
```

**Error Responses:**
- `400`: Bad Request - Email is required
- `404`: Not Found - User not found

### Verify Reset Token
**GET** `/auth/verify-reset-token?token=<reset_token>`

Verify password reset token validity.

**Query Parameters:**
- `token`: Password reset token
- `email`: User email address

**Response:**
```json
{
  "success": true,
  "message": "Token is valid"
}
```

**Error Responses:**
- `400`: Bad Request - Invalid or expired token
- `404`: Not Found - User not found

### Reset Password
**POST** `/auth/reset-password`

Reset password using reset token.

**Request Body:**
```json
{
  "token": "string",
  "new_password": "string"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Password reset successfully"
}
```

**Error Responses:**
- `400`: Bad Request - Invalid token or weak password
- `404`: Not Found - User not found

### Change Password
**POST** `/auth/change-password`
*Requires Authentication*

Change password for authenticated user.

**Request Body:**
```json
{
  "current_password": "string",
  "new_password": "string"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Password changed successfully"
}
```

**Error Responses:**
- `400`: Bad Request - Invalid current password or weak new password
- `401`: Unauthorized - Authentication required

### Send Verification OTP
**POST** `/auth/send-verification-otp`

Send OTP to user's email for verification.

**Request Body:**
```json
{
  "email": "string"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Verification OTP sent successfully",
  "data": {
    "email": "string",
    "message": "Please check your email for the 6-digit verification code"
  }
}
```

### Verify Email with OTP
**POST** `/auth/verify-email-otp`

Verify user's email address using OTP.

**Request Body:**
```json
{
  "email": "string",
  "otp": "123456"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Email verified successfully",
  "data": {
    "user": {
      "id": "uuid",
      "email": "string",
      "name": "string",
      "user_type": "string",
      "status": "string",
      "email_verified": true
    }
  }
}
```

### Resend Verification OTP
**POST** `/auth/resend-verification-otp`

Resend verification OTP to user's email.

**Request Body:**
```json
{
  "email": "string"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Verification OTP resent successfully",
  "data": {
    "email": "string",
    "message": "Please check your email for the new 6-digit verification code"
  }
}
```

---

## User Profile Endpoints

### Get Profile
**GET** `/users/profile`
*Requires Authentication*

Get current user's profile information.

**Response:**
```json
{
  "success": true,
  "message": "User profile retrieved successfully",
  "data": {
    "user": {
      "id": "uuid",
      "name": "string",
      "email": "string",
      "phone": "string",
      "user_type": "string",
      "status": "string",
      "email_verified": boolean,
      "created_at": "datetime",
      "updated_at": "datetime",
      
      // For ACCESS_OPERATOR:
      "business_name": "string",
      "address": "string",
      "geo_latitude": number,
      "geo_longitude": number,
      "approved": boolean,
      
      // For CAR_OPERATOR:
      "license_number": "string",
      "vehicle_info": "string",
      "approved": boolean,
      "pickup_access_point_id": "uuid",
      "dropoff_access_point_id": "uuid"
    }
  }
}
```

### Update Profile
**PUT** `/users/profile`
*Requires Authentication*

Update current user's profile information. This endpoint is used to complete profiles after registration.

**Request Body:**
```json
{
  "name": "string",
  "phone": "string",

  // For ACCESS_OPERATOR (complete profile):
  "business_name": "string",
  "address": "string",
  "geo_latitude": number,
  "geo_longitude": number,

  // For CAR_OPERATOR (complete profile):
  "license_number": "string",
  "vehicle_info": "string",
  "pickup_access_point_id": "uuid",
  "dropoff_access_point_id": "uuid"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Profile updated successfully",
  "data": {
    "user": {
      "id": "uuid",
      "name": "string",
      "email": "string",
      "phone": "string",
      "user_type": "string",
      "status": "string",
      "email_verified": boolean,
      "created_at": "datetime",
      "updated_at": "datetime",

      // For ACCESS_OPERATOR:
      "business_name": "string",
      "address": "string",
      "geo_latitude": number,
      "geo_longitude": number,
      "approved": boolean,

      // For CAR_OPERATOR:
      "license_number": "string",
      "vehicle_info": "string",
      "approved": boolean,
      "pickup_access_point_id": "uuid",
      "dropoff_access_point_id": "uuid"
    }
  }
}
```

**Profile Completion Notes:**
- **Access Operators**: Must provide business_name and address to be eligible for approval
- **Car Operators**: Must provide license_number and vehicle_info to be eligible for approval
- All fields are optional in the request, but user type-specific fields are required for approval
- If AccessOperator or CarOperator records don't exist, they will be created automatically

**Error Responses:**
- `400`: Bad Request - Invalid field values or validation errors
- `401`: Unauthorized - Authentication required
- `404`: Not Found - User not found

---

## Shipment Endpoints

### Create Shipment
**POST** `/shipments`
*Requires Authentication: CUSTOMER*

Create a new shipment.

**Request Body:**
```json
{
  "origin_ao_id": "uuid",
  "dest_ao_id": "uuid",
  "weight": number,
  "size": "string",
  "description": "string",
  "receiver_name": "string",
  "receiver_phone": "string"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Shipment created successfully. AO will generate their QR code when you arrive for drop-off.",
  "data": {
    "shipment": {
      "id": "uuid",
      "customer_id": "uuid",
      "origin_ao_id": "uuid",
      "dest_ao_id": "uuid",
      "weight": number,
      "size": "string",
      "description": "string",
      "receiver_name": "string",
      "receiver_phone": "string",
      "status": "PENDING",
      "pickup_code": "string",
      "created_at": "datetime"
    },
    "qr_codes": {
      "pickup_qr": {
        "value": "PICKUP123456",
        "description": "Keep this QR for package collection - show to final AO for delivery",
        "contains": {
          "shipment_id": "uuid",
          "pickup_code": "string"
        }
      }
    }
  }
}
```

### Get Shipment
**GET** `/shipments/:id`
*Requires Authentication*

Get shipment details by ID. Users can only access shipments they are authorized to view.

**Path Parameters:**
- `id`: Shipment UUID

**Response:**
```json
{
  "success": true,
  "data": {
    "shipment": {
      "id": "uuid",
      "customer_id": "uuid",
      "origin_ao_id": "uuid",
      "dest_ao_id": "uuid",
      "assigned_car_operator_id": "uuid",
      "status": "PENDING|AWAITING_PICKUP|IN_TRANSIT|ARRIVED_AT_DESTINATION|DELIVERED|CANCELLED",
      "weight": number,
      "size": "string",
      "description": "string",
      "pickup_code": "string",
      "tracking_code": "string",
      "receiver_name": "string",
      "receiver_phone": "string",
      "estimated_delivery": "datetime",
      "picked_up_at": "datetime",
      "cancellation_reason": "USER_CANCELLED|SYSTEM_EXPIRED|ADMIN_CANCELLED",
      "cancelled_at": "datetime",
      "expires_at": "datetime",
      "created_at": "datetime",
      "updated_at": "datetime"
    }
  }
}
```

**Error Responses:**
- `401`: Unauthorized - Authentication required
- `403`: Forbidden - User not authorized to view this shipment
- `404`: Not Found - Shipment not found

### Cancel Shipment
**POST** `/shipments/:id/cancel`
*Requires Authentication: CUSTOMER*

Cancel a shipment (only if status is PENDING). Only the shipment owner can cancel their shipment.

**Path Parameters:**
- `id`: Shipment UUID

**Request Body:**
```json
{
  "reason": "string (optional)"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Shipment cancelled successfully",
  "data": {
    "shipment": {
      "id": "uuid",
      "status": "CANCELLED",
      "cancellation_reason": "USER_CANCELLED",
      "cancelled_at": "datetime",
      "updated_at": "datetime"
    }
  }
}
```

**Error Responses:**
- `400`: Bad Request - Shipment cannot be cancelled (not in PENDING status)
- `401`: Unauthorized - Authentication required
- `403`: Forbidden - Only shipment owner can cancel
- `404`: Not Found - Shipment not found

### Scan Shipment
**POST** `/shipments/scan`
*Requires Authentication: ACCESS_OPERATOR, CAR_OPERATOR*

Scan QR code for shipment workflow with enhanced security validation and photo documentation.

**Request Body:**
```json
{
  "qr_value": "AO_randomString|PICKUP123456",
  "photo_url": "/uploads/photos/folder/filename.jpg",
  "action": "DROPOFF|PICKUP|ARRIVAL",
  "notes": "string",
  "geo_latitude": number,
  "geo_longitude": number
}
```

**QR Code Formats:**
- **AO QR**: `AO_randomString` (generated by Access Operators)
- **Pickup QR**: `PICKUP123456` (generated during shipment creation)

**Action Permissions & Authorization:**
- `DROPOFF`: ACCESS_OPERATOR only (must be origin AO for the shipment)
- `PICKUP`: CAR_OPERATOR only
- `ARRIVAL`: ACCESS_OPERATOR only (must be destination AO for the shipment)

**Security Features:**
- QR code ownership validation
- User authorization for specific shipment
- Unauthorized scan attempts are logged as security events
- Photo required for all scan operations

**Response:**
```json
{
  "success": true,
  "message": "Package received successfully at origin|Package picked up successfully by Car Operator|Package arrival confirmed at destination",
  "data": {
    "shipment": {
      "id": "uuid",
      "status": "AWAITING_PICKUP|IN_TRANSIT|ARRIVED_AT_DESTINATION",
      "updated_at": "datetime"
    },
    "photo_url": "/uploads/photos/folder/filename.jpg"
  }
}
```

**Error Responses:**
- `400`: Bad Request - Invalid QR format, wrong shipment status, or missing photo
- `401`: Unauthorized - Authentication required
- `403`: Forbidden - User not authorized for this action or shipment
- `404`: Not Found - Shipment or QR code not found

### Deliver Shipment
**POST** `/shipments/deliver`
*Requires Authentication: ACCESS_OPERATOR*

Final delivery with both QR codes validation and photo documentation. Only the destination AO can perform final delivery.

**Request Body:**
```json
{
  "shipment_qr": "AO_randomString",
  "pickup_qr": "PICKUP123456",
  "photo_url": "/uploads/photos/folder/filename.jpg",
  "notes": "string",
  "geo_latitude": number,
  "geo_longitude": number
}
```

**Process:**
1. Receiver presents both QR codes to destination AO
2. AO scans both QR codes to verify receiver identity
3. System validates both QR codes belong to the same shipment
4. System verifies AO is authorized for this shipment destination
5. Photo documentation is required
6. Shipment status updated to DELIVERED
7. Customer receives delivery confirmation

**Response:**
```json
{
  "success": true,
  "message": "Package delivered successfully to receiver",
  "data": {
    "shipment": {
      "id": "uuid",
      "status": "DELIVERED",
      "updated_at": "datetime",
      "receiver_name": "string"
    },
    "photo_url": "/uploads/photos/folder/filename.jpg"
  }
}
```

**Error Responses:**
- `400`: Bad Request - Invalid QR codes, wrong shipment status, or missing photo
- `401`: Unauthorized - Authentication required
- `403`: Forbidden - User not authorized for this shipment destination
- `404`: Not Found - Shipment not found

### Get My Shipments
**GET** `/shipments/my`
*Requires Authentication*

Get all shipments for the authenticated user with pagination, filtering, and search.

**Query Parameters:**
- `page`: Page number (default: 0)
- `pageSize`: Items per page (default: 10, max: 100)
- `search`: Search term (searches in receiver name, phone, description, pickup code, and shipment ID)
- `status`: Filter by shipment status (PENDING, AWAITING_PICKUP, IN_TRANSIT, ARRIVED_AT_DESTINATION, DELIVERED, CANCELLED)
- `sort`: Sort field and direction (format: `field:direction`, e.g., `createdAt:desc`, `status:asc`)
- `originAoId`: Filter by origin Access Operator ID (UUID)
- `destAoId`: Filter by destination Access Operator ID (UUID)

**Filtering Behavior:**
- **Customers**: Can filter their own shipments by origin and/or destination AO
- **Access Operators**:
  - Without filters: Shows all shipments where they are either origin or destination AO
  - With `originAoId`: Shows only shipments with that specific origin AO
  - With `destAoId`: Shows only shipments with that specific destination AO
  - With both: Shows shipments matching both origin AND destination AO
- **Car Operators**:
  - Without filters: Shows shipments related to their configured access points or assigned to them
  - With AO filters: Shows shipments matching the specified origin/destination AO filters

**Response:**
```json
{
  "success": true,
  "data": {
    "shipments": [
      {
        "id": "uuid",
        "customer_id": "uuid",
        "origin_ao_id": "uuid",
        "dest_ao_id": "uuid",
        "status": "string",
        "weight": number,
        "size": "string",
        "description": "string",
        "receiver_name": "string",
        "receiver_phone": "string",
        "pickup_code": "string",
        "created_at": "datetime",
        "updated_at": "datetime"
      }
    ],
    "total": number,
    "stats": {
      "total": number,
      "pending": number,
      "inTransit": number,
      "delivered": number,
      "cancelled": number
    },
    "pagination": {
      "page": number,
      "limit": number,
      "total": number,
      "totalPages": number,
      "hasNext": boolean,
      "hasPrev": boolean
    }
  }
}
```


### Get Pending Shipments
**GET** `/shipments/pending`
*Requires Authentication*

Get pending shipments for the authenticated user with pagination.

**Query Parameters:**
- `page`: Page number (default: 0)
- `limit`: Items per page (default: 10, max: 100)

**Response:**
```json
{
  "success": true,
  "data": {
    "shipments": [
      {
        "id": "uuid",
        "customer_id": "uuid",
        "origin_ao_id": "uuid",
        "dest_ao_id": "uuid",
        "status": "PENDING",
        "weight": number,
        "size": "string",
        "description": "string",
        "receiver_name": "string",
        "receiver_phone": "string",
        "pickup_code": "string",
        "expires_at": "datetime",
        "created_at": "datetime"
      }
    ],
    "pagination": {
      "page": number,
      "limit": number,
      "total": number,
      "totalPages": number,
      "hasNext": boolean,
      "hasPrev": boolean
    }
  }
}
```

**Error Responses:**
- `400`: Bad Request - Invalid pagination parameters
- `401`: Unauthorized - Authentication required

---

## QR Label Management Endpoints

### Generate QR Label for Shipment
**POST** `/qr-labels/generate-for-shipment`
*Requires Authentication: ACCESS_OPERATOR*

Generate QR label for a specific shipment (on-demand generation when customer arrives).

**Request Body:**
```json
{
  "shipment_id": "uuid"
}
```

**Response:**
```json
{
  "success": true,
  "message": "QR label generated successfully for shipment",
  "data": {
    "qr_label": {
      "id": "uuid",
      "qr_value": "AO_randomString",
      "status": "ASSIGNED",
      "shipment_id": "uuid",
      "assigned_at": "datetime"
    }
  }
}
```

**Error Responses:**
- `400`: Bad Request - Invalid shipment ID or shipment not found
- `401`: Unauthorized - Authentication required
- `403`: Forbidden - Only Access Operators can generate QR labels
- `404`: Not Found - Shipment not found

---

## Photo Upload Endpoints

### Upload Photo
**POST** `/uploads/photo`
*Requires Authentication*

Upload photo for shipment operations.

**Request Body:**
```json
{
  "photoBase64": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...",
  "folder": "shipment-scans|shipment-delivery|qr-assignment"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Photo uploaded successfully",
  "data": {
    "photo_url": "/uploads/photos/folder/uuid.jpg"
  }
}
```

**Error Responses:**
- `400`: Bad Request - Invalid image format, size too large, or missing photo
- `401`: Unauthorized - Authentication required
- `413`: Payload Too Large - Image exceeds 5MB limit
- `422`: Unprocessable Entity - Invalid base64 format

---

## Access Points Endpoints

### Get All Access Points
**GET** `/access-points`
*Requires Authentication*

Get list of all active and approved access operators.

**Query Parameters:**
- `page`: Page number (default: 0)
- `limit`: Items per page (default: 10, max: 100)
- `search`: Search by name or business name
- `approved`: Filter by approval status (true/false)
- `status`: Filter by user status (ACTIVE/INACTIVE)

**Response:**
```json
{
  "success": true,
  "message": "Access operators retrieved successfully",
  "data": {
    "access_operators": [
      {
        "id": "uuid",
        "name": "string",
        "email": "string",
        "phone": "string",
        "business_name": "string",
        "address": "string",
        "geo_latitude": number,
        "geo_longitude": number,
        "approved": boolean,
        "status": "string",
        "created_at": "datetime"
      }
    ],
    "pagination": {
      "page": number,
      "limit": number,
      "total": number,
      "totalPages": number,
      "hasNext": boolean,
      "hasPrev": boolean
    }
  }
}
```

**Error Responses:**
- `400`: Bad Request - Invalid pagination parameters
- `401`: Unauthorized - Authentication required

### Get Access Point by ID
**GET** `/access-points/:id`
*Requires Authentication*

Get specific access operator details.

**Path Parameters:**
- `id`: Access Operator UUID

**Response:**
```json
{
  "success": true,
  "message": "Access operator retrieved successfully",
  "data": {
    "access_operator": {
      "id": "uuid",
      "name": "string",
      "email": "string",
      "phone": "string",
      "business_name": "string",
      "address": "string",
      "geo_latitude": number,
      "geo_longitude": number,
      "approved": boolean,
      "status": "string",
      "created_at": "datetime"
    }
  }
}
```

**Error Responses:**
- `401`: Unauthorized - Authentication required
- `404`: Not Found - Access operator not found

---

## Unified Data Endpoint

### Get My Data
**GET** `/my-data`
*Requires Authentication*

Get personalized data for the authenticated user based on their user type.

**Query Parameters:**
- `type`: Filter by data type (e.g., 'shipments')
- `status`: Filter by status
- `page`: Page number (default: 0)
- `limit`: Items per page (default: 10)

**Response:**
```json
{
  "success": true,
  "data": {
    "shipments": [...],
    "user_type": "CAR_OPERATOR",
    "total": 25,
    "pagination": {
      "total": 25,
      "page": 0,
      "limit": 10,
      "totalPages": 3
    }
  }
}
```

---

## Admin Endpoints

All admin endpoints are prefixed with `/admin` and require admin authentication.

### Admin Authentication

#### Register Admin
**POST** `/admin/register`

Register a new admin user.

#### Login Admin
**POST** `/admin/login`

Admin login with email and password.

#### Forgot Password
**POST** `/admin/forgot-password`

Request admin password reset.

#### Reset Password
**POST** `/admin/reset-password`

Reset admin password with token.

#### Verify OTP
**POST** `/admin/verify-otp`

Verify admin OTP for two-factor authentication.

#### Resend OTP
**POST** `/admin/resend-otp`

Resend OTP for admin verification.

### Admin Profile Management

#### Get Admin Profile
**GET** `/admin/profile`
*Requires Admin Authentication*

#### Update Admin Profile
**PUT** `/admin/profile`
*Requires Admin Authentication*

#### Change Admin Password
**POST** `/admin/change-password`
*Requires Admin Authentication*

#### Logout Admin
**POST** `/admin/logout`
*Requires Admin Authentication*

### Admin Management

#### Get All Admins
**GET** `/admin/all`
*Requires Admin Authentication*

#### Change Admin Status
**POST** `/admin/status`
*Requires Admin Authentication*

### User Management (Admin)

#### Get All Users
**GET** `/admin/users`
*Requires Admin Authentication*

Get list of all users with filtering and pagination.

#### Get User by ID
**GET** `/admin/users/:id`
*Requires Admin Authentication*

#### Change User Status
**POST** `/admin/users/status`
*Requires Admin Authentication*

Activate or deactivate user accounts.

#### Change Operator Approval
**POST** `/admin/users/approval`
*Requires Admin Authentication*

Approve or reject Access Operators and Car Operators.

### Shipment Statistics

#### Get Expired Shipment Stats
**GET** `/admin/shipments/expired-stats`
*Requires Admin Authentication*

Get statistics about expired shipments.

---

## Error Codes

### HTTP Status Codes
- `200`: Success
- `201`: Created
- `400`: Bad Request
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `422`: Unprocessable Entity
- `500`: Internal Server Error

### Error Types
- `VALIDATION_ERROR`: Request validation failed
- `AUTHENTICATION_ERROR`: Authentication required
- `AUTHORIZATION_ERROR`: Insufficient permissions
- `NOT_FOUND_ERROR`: Resource not found
- `BUSINESS_LOGIC_ERROR`: Business rule violation
- `INTERNAL_ERROR`: Server error

---

## Complete Shipment Workflow - Step by Step

### Step 1: Customer Creates Shipment
**API Call:** `POST /api/shipments`

**Process:**
1. Customer selects origin and destination Access Operators
2. **System validates route availability** - Checks if there's an active Car Operator for this route
3. Provides shipment details (weight, size, description, receiver info)
4. System generates 6-digit pickup code
5. System creates pickup QR: `PICKUP{6-digit-code}`
6. Shipment status: `PENDING`
7. 24-hour expiry timer starts

**Route Validation:**
- System checks for Car Operators with `pickup_access_point_id = origin_ao_id` AND `dropoff_access_point_id = dest_ao_id`
- Only approved and active Car Operators are considered
- If no Car Operator available, shipment creation is rejected with error message

**API Response (Success):**
```json
{
  "success": true,
  "message": "Shipment created successfully. AO will generate their QR code when you arrive for drop-off.",
  "data": {
    "shipment": {
      "id": "uuid",
      "status": "PENDING",
      "pickup_code": "ABC123",
      "expires_at": "2024-01-02T10:00:00Z"
    },
    "qr_codes": {
      "pickup_qr": {
        "value": "PICKUPABC123",
        "description": "Keep this QR for package collection - show to final AO for delivery"
      }
    }
  }
}
```

**API Response (Route Not Available):**
```json
{
  "success": false,
  "error": {
    "type": "ROUTE_VALIDATION_ERROR",
    "message": "No Car Operator available for route from origin_ao_id to dest_ao_id. Please select different Access Operators or contact support."
  }
}
```

### Step 2: Customer Arrives at Origin AO
**API Call:** `POST /api/qr-labels/generate-for-shipment`

**Process:**
1. Customer brings package to origin Access Operator
2. AO takes photo of the package
3. AO generates QR label for the shipment: `AO_{randomString}`
4. QR gets assigned to the specific shipment in database
5. Photo gets stored with QR assignment action
6. AO attaches physical QR label to package
7. Timer stops, shipment ready for Car Operator pickup

**API Request:**
```json
{
  "shipment_id": "uuid",
  "photo_url": "/uploads/photos/shipment-photos/uuid.jpg",
  "notes": "Package received from customer",
  "geo_latitude": 40.7128,
  "geo_longitude": -74.0060
}
```

**API Response:**
```json
{
  "success": true,
  "message": "QR label generated successfully for shipment",
  "data": {
    "qr_label": {
      "id": "uuid",
      "qr_value": "AO_x7k9m2p1",
      "status": "ASSIGNED",
      "shipment_id": "uuid",
      "assigned_at": "2024-01-01T10:30:00Z"
    }
  }
}
```

### Step 3: AO Confirms Package Receipt (DROPOFF)
**API Call:** `POST /api/shipments/scan`

**Process:**
1. AO scans the generated QR code: `AO_x7k9m2p1`
2. AO takes photo of package
3. System validates QR ownership (belongs to scanning AO)
4. Shipment status changes: `PENDING` → `AWAITING_PICKUP`
5. Package ready for Car Operator collection

**API Request:**
```json
{
  "qr_value": "AO_x7k9m2p1",
  "photo_url": "/uploads/photos/shipment-scans/uuid.jpg",
  "action": "DROPOFF",
  "notes": "Package received in good condition",
  "geo_latitude": 40.7128,
  "geo_longitude": -74.0060
}
```

**API Response:**
```json
{
  "success": true,
  "message": "Package received successfully at origin",
  "data": {
    "shipment": {
      "id": "uuid",
      "status": "AWAITING_PICKUP",
      "updated_at": "2024-01-01T10:35:00Z"
    }
  }
}
```

### Step 4: Car Operator Picks Up Package (PICKUP)
**API Call:** `POST /api/shipments/scan`

**Process:**
1. Car Operator scans AO QR code: `AO_x7k9m2p1`
2. Car Operator takes photo of package pickup
3. System validates Car Operator permissions
4. Shipment status changes: `AWAITING_PICKUP` → `IN_TRANSIT`
5. Package in transit to destination

**API Request:**
```json
{
  "qr_value": "AO_x7k9m2p1",
  "photo_url": "/uploads/photos/shipment-scans/uuid.jpg",
  "action": "PICKUP",
  "notes": "Package picked up for transport",
  "geo_latitude": 40.7128,
  "geo_longitude": -74.0060
}
```

**API Response:**
```json
{
  "success": true,
  "message": "Package picked up successfully by Car Operator",
  "data": {
    "shipment": {
      "id": "uuid",
      "status": "IN_TRANSIT",
      "updated_at": "2024-01-01T11:00:00Z"
    }
  }
}
```

### Step 5: Car Operator Arrives at Destination (ARRIVAL)
**API Call:** `POST /api/shipments/scan`

**Process:**
1. Car Operator arrives at destination AO
2. Destination AO scans QR code: `AO_x7k9m2p1`
3. Destination AO takes photo of package arrival
4. System validates destination AO permissions
5. Shipment status changes: `IN_TRANSIT` → `ARRIVED_AT_DESTINATION`
6. System sends notification to receiver

**API Request:**
```json
{
  "qr_value": "AO_x7k9m2p1",
  "photo_url": "/uploads/photos/shipment-scans/uuid.jpg",
  "action": "ARRIVAL",
  "notes": "Package arrived at destination",
  "geo_latitude": 40.7589,
  "geo_longitude": -73.9851
}
```

**API Response:**
```json
{
  "success": true,
  "message": "Package arrival confirmed at destination",
  "data": {
    "shipment": {
      "id": "uuid",
      "status": "ARRIVED_AT_DESTINATION",
      "updated_at": "2024-01-01T14:00:00Z"
    }
  }
}
```

### Step 6: Final Delivery to Receiver
**API Call:** `POST /api/shipments/deliver`

**Process:**
1. Receiver arrives at destination AO with pickup QR: `PICKUPABC123`
2. Destination AO scans both QR codes:
   - Shipment QR: `AO_x7k9m2p1`
   - Pickup QR: `PICKUPABC123`
3. System validates both QR codes belong to same shipment
4. AO takes photo of delivery
5. Shipment status changes: `ARRIVED_AT_DESTINATION` → `DELIVERED`
6. Delivery complete

**API Request:**
```json
{
  "shipment_qr": "AO_x7k9m2p1",
  "pickup_qr": "PICKUPABC123",
  "photo_url": "/uploads/photos/shipment-delivery/uuid.jpg",
  "notes": "Package delivered to receiver",
  "geo_latitude": 40.7589,
  "geo_longitude": -73.9851
}
```

**API Response:**
```json
{
  "success": true,
  "message": "Package delivered successfully to receiver",
  "data": {
    "shipment": {
      "id": "uuid",
      "status": "DELIVERED",
      "updated_at": "2024-01-01T15:00:00Z",
      "receiver_name": "John Doe"
    }
  }
}
```

---

## Shipment Status Flow

1. **PENDING**: Initial status after creation (24-hour timer active)
2. **AWAITING_PICKUP**: After dropoff scan by Access Operator (timer stopped)
3. **IN_TRANSIT**: After pickup scan by Car Operator
4. **ARRIVED_AT_DESTINATION**: After arrival scan by destination Access Operator
5. **DELIVERED**: After final delivery to receiver
6. **CANCELLED**: If cancelled by customer (only from PENDING)

---

## QR Code System

### AO QR Code Format
**Format:** `AO_{randomString}`
- **Example:** `AO_x7k9m2p1`
- Generated by Access Operators with only prefix and random string
- No shipment ID embedded in the QR code
- Linked to shipment in database when assigned
- Used for tracking shipment through the workflow
- Attached to physical package by AO

### Pickup QR Code Format
**Format:** `PICKUP{6-digit-code}`
- **Example:** `PICKUPABC123`
- Used by final recipient to collect package
- Generated during shipment creation with 6-digit code
- No underscore separator (simplified format)
- Contains pickup code for verification

### QR Code Security Features
- **Ownership Validation**: AO QR codes validated against database
- **Status Tracking**: QR codes have status (UNUSED, ASSIGNED, USED)
- **Audit Trail**: All QR operations logged
- **Authorization**: Only authorized users can scan specific QR types

---

## Photo Tracking System

### Photo Storage for Each Operation
Every shipment operation requires photo documentation, creating a complete visual audit trail:

**Photo Actions:**
- **QR_ASSIGNMENT**: When AO assigns QR to shipment (customer drop-off)
- **DROPOFF**: When AO confirms package receipt
- **PICKUP**: When Car Operator picks up package
- **ARRIVAL**: When Car Operator arrives at destination
- **DELIVERY**: When destination AO delivers to receiver

### Photo Data Structure
```json
{
  "id": "uuid",
  "shipment_id": "uuid",
  "user_id": "uuid",
  "photo_url": "/uploads/photos/shipment-photos/uuid.jpg",
  "action": "DROPOFF",
  "notes": "Package in good condition",
  "latitude": 40.7128,
  "longitude": -74.0060,
  "created_at": "2024-01-01T10:30:00Z"
}
```

### Photo Requirements
- **Mandatory**: All scan and delivery operations require photos
- **Format**: JPEG, PNG, GIF supported
- **Size**: Maximum 10MB per photo
- **Storage**: Photos stored with unique filenames and organized by date
- **Metadata**: Each photo includes user ID, action type, location, and timestamp

---

## Route Validation System

### How Route Validation Works
Before allowing shipment creation, the system validates that there's an available Car Operator for the selected route.

**Validation Process:**
1. Customer selects origin AO (A1) and destination AO (A2)
2. System searches for Car Operators where:
   - `pickup_access_point_id = A1`
   - `dropoff_access_point_id = A2`
   - `approved = true`
   - `user.status = ACTIVE`
3. If no matching Car Operator found, shipment creation is rejected

**Example Scenario:**
- **Available Routes**:
  - CO1: A1 → A2
  - CO2: A3 → A4
- **Customer Request**: A2 → A4 ❌ (No Car Operator available)
- **Customer Request**: A1 → A2 ✅ (CO1 available)

### Route Management Benefits
- **Prevents Orphaned Shipments**: No shipments created without transport capability
- **Improves Customer Experience**: Clear feedback on available routes
- **Operational Efficiency**: Only viable shipments enter the system
- **Resource Planning**: Helps identify route coverage gaps

---

## Complete API Workflow Reference

### 1. Authentication APIs
```bash
# Register user
POST /api/auth/register
# Login user
POST /api/auth/login
# Verify email with OTP
POST /api/auth/verify-email-otp
```

### 2. Shipment Creation Workflow
```bash
# Get available access points
GET /api/access-points

# Create shipment (Customer)
POST /api/shipments
Content-Type: application/json
Authorization: Bearer <customer_token>
{
  "origin_ao_id": "uuid",
  "dest_ao_id": "uuid",
  "weight": 2.5,
  "size": "Medium",
  "description": "Electronics package",
  "receiver_name": "John Doe",
  "receiver_phone": "**********"
}
```

### 3. QR Generation Workflow
```bash
# Generate QR for shipment (Access Operator)
POST /api/qr-labels/generate-for-shipment
Content-Type: application/json
Authorization: Bearer <ao_token>
{
  "shipment_id": "uuid"
}
```

### 4. Photo Upload Workflow
```bash
# Upload photo before scanning
POST /api/uploads/photo
Content-Type: application/json
Authorization: Bearer <token>
{
  "photoBase64": "data:image/jpeg;base64,/9j/4AAQ...",
  "folder": "shipment-scans"
}
```

### 5. Scanning Workflow
```bash
# DROPOFF scan (Access Operator)
POST /api/shipments/scan
Content-Type: application/json
Authorization: Bearer <ao_token>
{
  "qr_value": "AO_x7k9m2p1",
  "photo_url": "/uploads/photos/shipment-scans/uuid.jpg",
  "action": "DROPOFF",
  "notes": "Package received",
  "geo_latitude": 40.7128,
  "geo_longitude": -74.0060
}

# PICKUP scan (Car Operator)
POST /api/shipments/scan
Content-Type: application/json
Authorization: Bearer <co_token>
{
  "qr_value": "AO_x7k9m2p1",
  "photo_url": "/uploads/photos/shipment-scans/uuid.jpg",
  "action": "PICKUP",
  "notes": "Package picked up",
  "geo_latitude": 40.7128,
  "geo_longitude": -74.0060
}

# ARRIVAL scan (Destination Access Operator)
POST /api/shipments/scan
Content-Type: application/json
Authorization: Bearer <dest_ao_token>
{
  "qr_value": "AO_x7k9m2p1",
  "photo_url": "/uploads/photos/shipment-scans/uuid.jpg",
  "action": "ARRIVAL",
  "notes": "Package arrived",
  "geo_latitude": 40.7589,
  "geo_longitude": -73.9851
}
```

### 6. Final Delivery Workflow
```bash
# Final delivery (Destination Access Operator)
POST /api/shipments/deliver
Content-Type: application/json
Authorization: Bearer <dest_ao_token>
{
  "shipment_qr": "AO_x7k9m2p1",
  "pickup_qr": "PICKUPABC123",
  "photo_url": "/uploads/photos/shipment-delivery/uuid.jpg",
  "notes": "Delivered to receiver",
  "geo_latitude": 40.7589,
  "geo_longitude": -73.9851
}
```

### 7. Tracking and Management APIs
```bash
# Get my shipments (with search and filters)
GET /api/shipments/my?page=0&pageSize=10&search=john&status=PENDING&sort=createdAt:desc&originAoId=uuid&destAoId=uuid
Authorization: Bearer <token>

# Get specific shipment
GET /api/shipments/:id
Authorization: Bearer <token>

# Cancel shipment (Customer only, PENDING status only)
POST /api/shipments/:id/cancel
Authorization: Bearer <customer_token>
{
  "reason": "Changed plans"
}
```

---

## User Roles and Permissions

### Customer
- ✅ Create shipments
- ✅ View own shipments
- ✅ Cancel pending shipments
- ✅ Track shipment status
- ❌ Cannot scan QR codes
- ❌ Cannot generate QR codes

### Access Operator
- ✅ Generate QR codes for shipments
- ✅ Scan QR codes (DROPOFF, ARRIVAL actions)
- ✅ View shipments for their access point
- ✅ Perform final delivery
- ❌ Cannot pickup packages (PICKUP action)

### Car Operator
- ✅ Scan QR codes (PICKUP action only)
- ✅ View shipments they can transport
- ❌ Cannot generate QR codes
- ❌ Cannot perform DROPOFF or ARRIVAL actions

### Admin
- ✅ Full system access
- ✅ User management
- ✅ Approve operators
- ✅ View all shipments and statistics

---

## Photo Upload Workflow

1. **Upload Photo**: POST `/uploads/photo` with base64 image
2. **Receive Photo URL**: Get photo URL in response
3. **Use Photo URL**: Include photo URL in scan/delivery requests

This two-step process prevents transaction timeouts and improves performance.

**Example Workflow:**
```javascript
// Step 1: Upload photo
const photoResponse = await fetch('/api/uploads/photo', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    photoBase64: capturedPhotoBase64,
    folder: 'shipment-scans'
  })
});
const { photo_url } = photoResponse.data;

// Step 2: Use photo URL in scan
const scanResponse = await fetch('/api/shipments/scan', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    qr_value: "AO_randomString",
    photo_url: photo_url,
    action: "DROPOFF",
    notes: "Package received in good condition",
    geo_latitude: 40.7128,
    geo_longitude: -74.0060
  })
});
```

---

## Enhanced Notification System

### Receiver Notifications
When a shipment arrives at the destination (status: `ARRIVED_AT_DESTINATION`), the system automatically sends an email notification to the receiver containing:

- **Pickup Details**: Shipment ID, pickup code, receiver phone
- **Pickup QR Link**: Direct link to view the pickup QR code
- **Instructions**: Step-by-step pickup process
- **Security Notice**: Both QR codes required for delivery

### Email Template Example
```
Subject: Your Package Has Arrived - Ready for Pickup

Dear [Receiver Name],

Your package has arrived at the destination and is ready for pickup!

Pickup Details:
- Shipment ID: [shipment_id]
- Pickup Code: [pickup_code]
- Phone: [receiver_phone]

To collect your package:
1. Visit the destination access point
2. Show your pickup QR code: [View Pickup QR Link]
3. Present valid ID matching the receiver name

Important: Both the pickup QR code and the destination AO's QR code are required for final delivery.
```

### Security Event Logging
All unauthorized scan attempts are logged with:
- User ID and expected authorization
- QR code value and shipment details
- Timestamp and reason for rejection
- Audit trail for security monitoring

---

## Rate Limiting

- Authentication endpoints: 5 requests per minute per IP
- General API endpoints: 100 requests per minute per user
- Photo upload: 10 requests per minute per user

---

## Email Verification Workflow

### OTP-Based Verification (Recommended)

1. **User Registration**: User registers with email and password
2. **Automatic OTP Send**: System automatically sends 6-digit OTP to user's email
3. **User Receives OTP**: User checks email for verification code
4. **OTP Verification**: User submits email and OTP via `/auth/verify-email-otp`
5. **Account Activation**: Email verified, user can now log in

### OTP Workflow Example

```javascript
// Step 1: Register user (OTP sent automatically)
const registerResponse = await fetch('/api/auth/register', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    user_type: 'CUSTOMER',
    name: 'John Doe',
    email: '<EMAIL>',
    password: 'securePassword123',
    phone: '+**********'
  })
});

// Step 2: User receives OTP via email (123456)

// Step 3: Verify email with OTP
const verifyResponse = await fetch('/api/auth/verify-email-otp', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: '<EMAIL>',
    otp: '123456'
  })
});

// Step 4: User can now log in
const loginResponse = await fetch('/api/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'securePassword123'
  })
});
```

### OTP Features

- **6-digit numeric code**: Easy to type and remember
- **5-minute expiration**: Security through time limitation
- **Automatic invalidation**: Used OTPs cannot be reused
- **Resend capability**: Users can request new OTP if needed
- **Email-based delivery**: Secure delivery to verified email address

---

## Notes

- All timestamps are in ISO 8601 format
- All UUIDs are version 4
- Photo uploads support JPEG, PNG, and GIF formats (max 5MB)
- Geo coordinates use decimal degrees format
- Phone numbers should include country code
- All endpoints require authentication unless explicitly marked as "No Authentication"
- Car operators must be approved by admin before they can access most endpoints
- Access operators must be approved by admin before they can access most endpoints
- Only active and approved access operators are returned in access points endpoints
- **Email verification is required**: Users must verify their email with OTP before logging in
- **OTP expiration**: All OTPs expire after 5 minutes for security
- **Legacy support**: Token-based verification endpoints still available for backward compatibility

### New Workflow Features
- **Simplified Registration**: Only basic information required during registration, users complete profiles after login
- **Profile Completion**: Access Operators and Car Operators add business/vehicle details through profile update
- **Automatic Record Creation**: AccessOperator/CarOperator records created automatically during profile completion
- **New QR System**:
  - Only pickup QR generated at shipment creation
  - AO generates shipment QR on-demand with format `AO_{randomString}`
  - No shipment ID embedded in QR codes
  - QR codes linked to shipments via database
- **Enhanced Security**:
  - QR ownership validation through database lookup
  - Authorization validation for all scan operations
  - Security event logging for unauthorized attempts
  - Role-based action permissions
- **Receiver Information**: Required receiver name and phone number for all shipments
- **Automatic Notifications**: Email notifications sent to receiver when package arrives at destination
- **Photo Documentation**: Required for all scan and delivery operations
- **Dual QR Verification**: Final delivery requires both pickup QR and AO QR for enhanced security
- **Audit Trail**: Complete tracking of all QR operations and status changes

---

## Common Error Scenarios and Troubleshooting

### QR Code Errors
```json
// Invalid QR format
{
  "success": false,
  "error": {
    "type": "VALIDATION_ERROR",
    "details": "Invalid QR code format. Expected AO_randomString or PICKUP123456"
  }
}

// QR not found
{
  "success": false,
  "error": {
    "type": "NOT_FOUND_ERROR",
    "details": "QR code not found or not assigned to a shipment"
  }
}

// QR ownership error
{
  "success": false,
  "error": {
    "type": "AUTHORIZATION_ERROR",
    "details": "QR code does not belong to the scanning Access Operator"
  }
}

// QR already used
{
  "success": false,
  "error": {
    "type": "BUSINESS_LOGIC_ERROR",
    "details": "QR code has already been used"
  }
}
```

### Status Transition Errors
```json
// Wrong status for action
{
  "success": false,
  "error": {
    "type": "BUSINESS_LOGIC_ERROR",
    "details": "Shipment must be in PENDING status for dropoff"
  }
}

// Wrong user type for action
{
  "success": false,
  "error": {
    "type": "AUTHORIZATION_ERROR",
    "details": "Only Car Operators can perform pickup scans"
  }
}
```

### Photo Upload Errors
```json
// Missing photo
{
  "success": false,
  "error": {
    "type": "VALIDATION_ERROR",
    "details": "Photo is required for all shipment scan operations"
  }
}

// Invalid photo format
{
  "success": false,
  "error": {
    "type": "VALIDATION_ERROR",
    "details": "Invalid image format. Supported formats: JPEG, PNG, GIF"
  }
}
```
