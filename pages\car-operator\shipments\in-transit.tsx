import { useState } from 'react';
import {
  Container,
  Loader,
  Center,
} from '@mantine/core';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import { useIsClient } from '../../../src/hooks/useIsClient';
import { COInTransitShipmentsTable, COShipmentDetailView } from '../../../src/components/shipment';
import { Shipment } from '../../../src/requests/shipment';

export default function COInTransitShipmentsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const isClient = useIsClient();
  const [selectedShipment, setSelectedShipment] = useState<Shipment | null>(null);

  const handleViewShipment = (shipment: Shipment) => {
    setSelectedShipment(shipment);
  };

  const handleBackToList = () => {
    setSelectedShipment(null);
  };

  // Redirect if not authenticated or not CO user
  if (isClient && status === 'authenticated' && session?.user?.user_type !== 'CAR_OPERATOR') {
    router.push('/');
    return null;
  }

  // Show loading while checking authentication
  if (!isClient || status === 'loading') {
    return (
      <Center h="100vh">
        <Loader size="lg" />
      </Center>
    );
  }

  // Redirect to login if not authenticated
  if (status === 'unauthenticated') {
    router.push('/auth/login');
    return null;
  }

  // Show simplified shipment detail view if a shipment is selected
  if (selectedShipment) {
    return (
      <Container size="xl" py="md">
        <COShipmentDetailView
          shipment={selectedShipment}
          onBack={handleBackToList}
          // Car Operators cannot assign IN_TRANSIT shipments - only destination AO can
        />
      </Container>
    );
  }

  return (
    <Container size="xl" py="md">
      <COInTransitShipmentsTable
        onViewShipment={handleViewShipment}
      />
      {/* Car Operators cannot assign IN_TRANSIT shipments - only destination AO can */}
    </Container>
  );
}
