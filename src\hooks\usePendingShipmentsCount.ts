import { useQuery } from '@tanstack/react-query';
import { useSession } from 'next-auth/react';
import { getPendingShipmentsQuery } from '../requests/shipment';

/**
 * Hook to get the count of pending shipments for AO users
 * Returns the count for badge display in sidebar
 */
export const usePendingShipmentsCount = () => {
  const { data: session, status } = useSession();

  const aoId = session?.user?.id;
  // Only fetch for authenticated ACCESS_OPERATOR users
  const isAOUser = status === 'authenticated' && session?.user?.user_type === 'ACCESS_OPERATOR' && !!aoId;

  const {
    data: shipmentsData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    ...getPendingShipmentsQuery({
      pagination: {
        page: 0,
        pageSize: 1, // We only need the count, so minimal page size
      },
      filters: {
        status: 'PENDING',
        originAoId: aoId || undefined, // Only count shipments where this AO is the origin
      },
    }),
    enabled: isAOUser, // Only enable query for AO users
    refetchInterval: isAOUser ? 30000 : false, // Only refetch for AO users
    refetchOnWindowFocus: isAOUser,
  });

  // Extract count from pagination data, only for AO users
  const pendingCount = isAOUser ? (shipmentsData?.data?.pagination?.total || 0) : 0;

  return {
    pendingCount,
    isLoading: isAOUser ? isLoading : false,
    error: isAOUser ? error : null,
    refetch,
    isEnabled: isAOUser,
  };
};

export default usePendingShipmentsCount;
