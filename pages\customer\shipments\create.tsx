import { useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import useTranslation from 'next-translate/useTranslation';
import {
  Container,
  Loader,
  Center,
  Stack,
  Button,
  Group,
  Text,
  Title,
} from '@mantine/core';
import { IconArrowLeft } from '@tabler/icons-react';
import { CreateShipmentForm } from '../../../src/components/shipment';

export default function CreateShipmentPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { t } = useTranslation('shipments');

  // Authentication and authorization check
  useEffect(() => {
    if (status === 'loading') return;

    if (!session) {
      router.push('/auth/login');
      return;
    }

    if (session.user?.user_type !== 'CUSTOMER') {
      router.push('/');
    }
  }, [session, status, router]);

  const handleSuccess = () => {
    // Navigate back to shipments list after successful creation
    router.push('/customer/shipments');
  };

  const handleCancel = () => {
    // Navigate back to shipments list
    router.push('/customer/shipments');
  };

  // Loading state
  if (status === 'loading') {
    return (
      <Center style={{ height: '50vh' }}>
        <Loader size="lg" />
      </Center>
    );
  }

  // Not authenticated or not a customer
  if (!session || session.user?.user_type !== 'CUSTOMER') {
    return null; // Will redirect
  }

  return (
    <Container size="xl" p="xl">
      <Stack gap="lg">
        {/* Header with back button */}
        <Group gap="md">
          <Button
            variant="subtle"
            leftSection={<IconArrowLeft size="1rem" />}
            onClick={handleCancel}
          >
            {t('backToShipments')}
          </Button>
        </Group>

        {/* Page Title */}
        <div>
          <Title order={1} mb="sm">
            {t('createNewShipmentTitle')}
          </Title>
          <Text size="lg" c="dimmed" mb="sm">
            {t('createNewShipmentSubtitle')}
          </Text>
          <Text size="md" c="orange" fw={500}>
            {t('reminderText')}
          </Text>
        </div>

        {/* Create Shipment Form */}
        <CreateShipmentForm
          onSuccess={handleSuccess}
          onCancel={handleCancel}
        />
      </Stack>
    </Container>
  );
}
