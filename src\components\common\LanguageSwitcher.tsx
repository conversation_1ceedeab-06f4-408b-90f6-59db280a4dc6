import { ActionIcon, Menu } from '@mantine/core';
import { IconLanguage } from '@tabler/icons-react';
import { useRouter } from 'next/router';
import { setCookie } from 'cookies-next';
import { LocaleCookie } from '../../data';

export default function LanguageSwitcher() {
  const router = useRouter();

  const switchLanguage = (locale: string) => {
    // Set cookie first to persist the choice
    setCookie(LocaleCookie, locale, {
      maxAge: 365 * 24 * 60 * 60, // 1 year
      sameSite: 'lax',
      path: '/',
    });

    // Then navigate to the new locale
    router.push(router.asPath, router.asPath, { locale });
  };

  const currentLocale = router.locale || 'ar';

  return (
    <Menu shadow="md" width={120}>
      <Menu.Target>
        <ActionIcon
          variant="default"
          size={36}
          aria-label="Switch language"
        >
          <IconLanguage size="1.2rem" />
        </ActionIcon>
      </Menu.Target>

      <Menu.Dropdown>
        <Menu.Item
          onClick={() => switchLanguage('ar')}
          style={{
            fontWeight: currentLocale === 'ar' ? 'bold' : 'normal',
            backgroundColor: currentLocale === 'ar' ? 'var(--mantine-color-blue-light)' : 'transparent',
          }}
        >
          العربية
        </Menu.Item>
        <Menu.Item
          onClick={() => switchLanguage('en')}
          style={{
            fontWeight: currentLocale === 'en' ? 'bold' : 'normal',
            backgroundColor: currentLocale === 'en' ? 'var(--mantine-color-blue-light)' : 'transparent',
          }}
        >
          English
        </Menu.Item>
      </Menu.Dropdown>
    </Menu>
  );
}
