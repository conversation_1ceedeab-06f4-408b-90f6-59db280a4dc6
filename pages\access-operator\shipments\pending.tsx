/* eslint-disable no-nested-ternary */
/* eslint-disable sonarjs/cognitive-complexity */
/* eslint-disable complexity */
import { useState, useEffect } from 'react';
import {
  Container,
  Title,
  Text,
  Stack,
  Group,
  TextInput,
  Button,
  Loader,
  Center,
  Alert,
  Pagination,
  Paper,
  Box,
} from '@mantine/core';
import {
  IconSearch,
  IconRefresh,
  IconAlertCircle,
  IconPackage,
} from '@tabler/icons-react';
import { useQuery } from '@tanstack/react-query';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import useTranslation from 'next-translate/useTranslation';
import { getPendingShipmentsQuery } from '../../../src/requests/shipment';
import { useIsClient } from '../../../src/hooks/useIsClient';
import { PendingAndInTransitShipmentsList } from '../../../src/components/shipment';

const ITEMS_PER_PAGE = 12;

export default function PendingShipmentsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const isClient = useIsClient();
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const { t } = useTranslation('shipments');
  const isRTL = router.locale === 'ar';

  const aoId = session?.user?.id;

  // Fetch pending shipments - moved before conditional returns
  const {
    data: shipmentsData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    ...getPendingShipmentsQuery({
      pagination: {
        page: currentPage - 1, // API uses 0-based pagination
        pageSize: ITEMS_PER_PAGE,
      },
      filters: {
        status: 'PENDING',
        originAoId: aoId || undefined, // Only show shipments where this AO is the origin
        search: searchQuery.trim() || undefined,
        role: 'origin',
      },
      enabled: status === 'authenticated' && session?.user?.user_type === 'ACCESS_OPERATOR' && !!aoId,
    }),
    refetchOnWindowFocus: false,
  });

  // Debounced search effect
  useEffect(() => {
    if (status === 'authenticated' && aoId) {
      const timeoutId = setTimeout(() => {
        setCurrentPage(1);
        refetch();
      }, 500); // 500ms delay

      return () => clearTimeout(timeoutId);
    }
    return undefined;
  }, [searchQuery, refetch, status, aoId]);

  // Redirect if not authenticated or not AO user
  if (isClient && status === 'authenticated' && session?.user?.user_type !== 'ACCESS_OPERATOR') {
    router.push('/');
    return null;
  }

  // Show loading while checking authentication
  if (!isClient || status === 'loading') {
    return (
      <Center h="100vh">
        <Loader size="lg" />
      </Center>
    );
  }

  // Redirect to login if not authenticated
  if (status === 'unauthenticated') {
    router.push('/auth/login');
    return null;
  }

  const handleRefresh = () => {
    refetch();
  };

  const shipments = shipmentsData?.data?.shipments || [];
  const pagination = shipmentsData?.data?.pagination;
  const totalPages = pagination?.totalPages || 1;

  return (
    <Container size="xl" py="md">
      <Stack gap="lg">
        {/* Header */}
        <Group justify="space-between" align="flex-start">
          {isRTL && (
            <Button
              leftSection={<IconRefresh size="1rem" />}
              variant="light"
              onClick={handleRefresh}
              loading={isLoading}
            >
              {t('refresh')}
            </Button>
          )}
          <Box>
            <Title order={2} mb="xs">
              {t('pendingShipments')}
            </Title>
          </Box>
          {!isRTL && (
            <Button
              leftSection={<IconRefresh size="1rem" />}
              variant="light"
              onClick={handleRefresh}
              loading={isLoading}
            >
              {t('refresh')}
            </Button>
          )}
        </Group>

        {/* Search */}
        <Paper p="md" withBorder>
          <TextInput
            placeholder={t('searchPendingPlaceholder')}
            leftSection={!isRTL ? <IconSearch size="1rem" /> : undefined}
            rightSection={isRTL ? <IconSearch size="1rem" /> : undefined}
            value={searchQuery}
            onChange={(event) => setSearchQuery(event.currentTarget.value)}
            style={{ width: '100%' }}
          />
        </Paper>

        {/* Content */}
        {isLoading ? (
          <Center py="xl">
            <Stack align="center" gap="md">
              <Loader size="lg" />
              <Text c="dimmed">{t('loadingPendingShipments')}</Text>
            </Stack>
          </Center>
        ) : error ? (
          <Alert
            icon={<IconAlertCircle size="1rem" />}
            title={t('errorLoadingShipments')}
            color="red"
            variant="light"
          >
            {error instanceof Error ? error.message : t('failedToLoadPendingShipments')}
            <Button variant="light" size="sm" mt="sm" onClick={handleRefresh}>
              {t('tryAgain')}
            </Button>
          </Alert>
        ) : shipments.length === 0 ? (
          <Paper p="xl" withBorder>
            <Stack align="center" gap="md">
              <IconPackage size="3rem" color="var(--mantine-color-gray-5)" />
              <Title order={3} c="dimmed">
                {t('noPendingShipments')}
              </Title>
              <Text c="dimmed" ta="center">
                {searchQuery
                  ? t('noShipmentsMatch')
                  : t('noPendingShipmentsMessage')}
              </Text>
              {searchQuery && (
                <Button
                  variant="light"
                  onClick={() => {
                    setSearchQuery('');
                    setCurrentPage(1);
                    refetch();
                  }}
                >
                  {t('clearSearch')}
                </Button>
              )}
            </Stack>
          </Paper>
        ) : (
          <>
            {/* Shipments List */}
            <PendingAndInTransitShipmentsList
              shipments={shipments}
              onRefresh={handleRefresh}
            />

            {/* Pagination */}
            {totalPages > 1 && (
              <Group justify="center" mt="lg">
                <Pagination
                  value={currentPage}
                  onChange={setCurrentPage}
                  total={totalPages}
                  size="sm"
                />
              </Group>
            )}

            {/* Results Summary */}
            <Text c="dimmed" size="sm" ta="center">
              {t('showing')}
              {' '}
              {shipments.length}
              {' '}
              {t('of')}
              {' '}
              {pagination?.total || 0}
              {' '}
              {t('pendingShipmentsLower')}
              {searchQuery && ` ${t('matching')} "${searchQuery}"`}
            </Text>
          </>
        )}
      </Stack>
    </Container>
  );
}
