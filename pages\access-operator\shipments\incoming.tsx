/* eslint-disable sonarjs/cognitive-complexity */
/* eslint-disable no-nested-ternary */
/* eslint-disable complexity */
import { useState } from 'react';
import {
  Container,
  Title,
  Text,
  Stack,
  Group,
  TextInput,
  Button,
  Loader,
  Center,
  Alert,
  Pagination,
  Paper,
  Box,
} from '@mantine/core';
import {
  IconSearch,
  IconRefresh,
  IconAlertCircle,
  IconTruck,
} from '@tabler/icons-react';
import { useQuery } from '@tanstack/react-query';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import useTranslation from 'next-translate/useTranslation';
import { useIsClient } from '../../../src/hooks/useIsClient';
import { PendingAndInTransitShipmentsList } from '../../../src/components/shipment';
import { getMyShipmentsQuery } from '../../../src/requests/shipment';
import { ACCESS_OPERATOR_USER_TYPE } from '../../../src/data';

export default function IncomingShipmentsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const isClient = useIsClient();
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const { t } = useTranslation('shipments');
  const isRTL = router.locale === 'ar';

  const aoId = session?.user?.id;

  const isAuthenticated = status === 'authenticated';
  const userType = session?.user?.user_type;

  const {
    data: shipmentsData,
    isLoading,
    refetch,
    error,
  } = useQuery({
    ...getMyShipmentsQuery({
      pagination: {
        page: 0,
        pageSize: 1,
      },
      filters: {
        status: 'IN_TRANSIT',
        destAoId: aoId || undefined,
      },
    }),
    enabled: isAuthenticated && userType === ACCESS_OPERATOR_USER_TYPE && !!session?.user?.id,
    refetchInterval: 30000,
  });

  if (isClient && status === 'authenticated' && session?.user?.user_type !== 'ACCESS_OPERATOR') {
    router.push('/');
    return null;
  }

  if (!isClient || status === 'loading') {
    return (
      <Center h="100vh">
        <Loader size="lg" />
      </Center>
    );
  }

  if (status === 'unauthenticated') {
    router.push('/auth/login');
    return null;
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
  };

  const handleRefresh = () => {
    refetch();
  };

  const shipments = shipmentsData?.data?.shipments || [];
  const pagination = shipmentsData?.data?.pagination;
  const totalPages = pagination?.totalPages || 1;

  return (
    <Container size="xl" py="md">
      <Stack gap="lg">
        <Group justify="space-between" align="flex-start">
          {isRTL && (
            <Button
              leftSection={<IconRefresh size="1rem" />}
              variant="light"
              onClick={handleRefresh}
              loading={isLoading}
            >
              {t('refresh')}
            </Button>
          )}
          <Box>
            <Title order={2} mb="xs">
              {t('incomingShipments')}
            </Title>
          </Box>
          {!isRTL && (
            <Button
              leftSection={<IconRefresh size="1rem" />}
              variant="light"
              onClick={handleRefresh}
              loading={isLoading}
            >
              {t('refresh')}
            </Button>
          )}
        </Group>

        <Paper p="md" withBorder>
          <form onSubmit={handleSearch}>
            <TextInput
              placeholder={t('searchIncomingPlaceholder')}
              leftSection={<IconSearch size="1rem" />}
              value={searchQuery}
              onChange={(event) => setSearchQuery(event.currentTarget.value)}
              style={{ flex: 1 }}
            />
          </form>
        </Paper>

        {isLoading ? (
          <Center py="xl">
            <Stack align="center" gap="md">
              <Loader size="lg" />
              <Text c="dimmed">{t('loadingIncomingShipments')}</Text>
            </Stack>
          </Center>
        ) : error ? (
          <Alert
            icon={<IconAlertCircle size="1rem" />}
            title={t('errorLoadingShipments')}
            color="red"
            variant="light"
          >
            {error instanceof Error ? error.message : t('failedToLoadIncomingShipments')}
            <Button variant="light" size="sm" mt="sm" onClick={handleRefresh}>
              {t('tryAgain')}
            </Button>
          </Alert>
        ) : shipments.length === 0 ? (
          <Paper p="xl" withBorder>
            <Stack align="center" gap="md">
              <IconTruck size="3rem" color="var(--mantine-color-gray-5)" />
              <Title order={3} c="dimmed">
                {t('noIncomingShipments')}
              </Title>
              <Text c="dimmed" ta="center">
                {searchQuery ? t('noShipmentsMatch') : t('noIncomingShipmentsMessage')}
              </Text>
              {searchQuery && (
                <Button
                  variant="light"
                  onClick={() => {
                    setSearchQuery('');
                    setCurrentPage(1);
                    refetch();
                  }}
                >
                  {t('clearSearch')}
                </Button>
              )}
            </Stack>
          </Paper>
        ) : (
          <>
            <PendingAndInTransitShipmentsList shipments={shipments} onRefresh={handleRefresh} />
            {totalPages > 1 && (
              <Group justify="center" mt="lg">
                <Pagination value={currentPage} onChange={setCurrentPage} total={totalPages} size="sm" />
              </Group>
            )}
          </>
        )}
      </Stack>
    </Container>
  );
}
