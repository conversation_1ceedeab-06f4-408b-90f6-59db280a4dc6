import {
  Stack,
  Group,
  Text,
  Button,
  TextInput,
} from '@mantine/core';
import {
  IconPhone,
} from '@tabler/icons-react';

interface ReceiverInfoStepProps {
  receiverPhone: string;
  setReceiverPhone: (value: string) => void;
  error: string | null;
  setError: (error: string | null) => void;
  onBack: () => void;
  onNext: () => void;
}

export default function ReceiverInfoStep({
  receiverPhone,
  setReceiverPhone,
  error,
  setError,
  onBack,
  onNext,
}: ReceiverInfoStepProps) {
  const clearAlerts = () => {
    setError(null);
  };

  const validateReceiverPhone = () => {
    if (!receiverPhone) {
      setError('Please enter the receiver phone number');
      return false;
    }
    return true;
  };

  return (
    <Stack gap="md">
      <Text fw={500}>Receiver Information:</Text>
      <TextInput
        label="Receiver Phone Number"
        placeholder="Enter receiver's phone number"
        value={receiverPhone}
        onChange={(event) => {
          clearAlerts();
          setReceiverPhone(event.currentTarget.value);
        }}
        required
        leftSection={<IconPhone size="1rem" />}
        description="Enter the phone number of the person receiving the package"
      />

      <Group justify="space-between" mt="md">
        <Button variant="light" onClick={onBack}>
          Back
        </Button>
        <Button
          onClick={() => {
            if (validateReceiverPhone()) {
              onNext();
            }
          }}
        >
          Next Step
        </Button>
      </Group>
    </Stack>
  );
}
