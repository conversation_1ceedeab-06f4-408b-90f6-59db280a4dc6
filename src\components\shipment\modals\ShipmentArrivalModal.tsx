/* eslint-disable no-console */
/* eslint-disable complexity */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable max-lines */
/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable no-nested-ternary */
/* eslint-disable react/require-default-props */
import { useState } from 'react';
import {
  Modal,
  Stack,
  Group,
  Text,
  Button,
  Stepper,
  Paper,
  Box,
  TextInput,
  Textarea,
  Alert,
  Image,
  Tabs,
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import {
  IconCamera,
  IconCheck,
  IconAlertTriangle,
  IconKeyboard,
} from '@tabler/icons-react';
import { useMutation } from '@tanstack/react-query';
import { useIsClient } from '../../../hooks/useIsClient';
import { Shipment } from '../../../requests/shipment';
import { useScanShipmentMutation } from '../../../requests/hooks/enhanced-mutations';
import QRScanner from '../../common/QRScanner';
import useTranslation from 'next-translate/useTranslation';

interface ShipmentArrivalModalProps {
  opened: boolean;
  onClose: () => void;
  shipment: Shipment | null;
  onSuccess?: () => void;
}

export default function ShipmentArrivalModal({
  opened,
  onClose,
  shipment,
  onSuccess,
}: ShipmentArrivalModalProps) {
  const { t } = useTranslation('shipments');
  const isClient = useIsClient();
  const [activeStep, setActiveStep] = useState(0);
  const [scannedQR, setScannedQR] = useState('');
  const [photoPreview, setPhotoPreview] = useState<string | null>(null);
  const [photoUrl, setPhotoUrl] = useState<string | null>(null);
  const [notes, setNotes] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [scanMethod, setScanMethod] = useState<'camera' | 'manual'>('camera');

  // This modal is specifically for ARRIVAL action
  const totalSteps = 3; // QR Code -> Photo -> Notes
  const completedIndex = totalSteps;

  const handleClose = () => {
    setActiveStep(0);
    setScannedQR('');
    setPhotoPreview(null);
    setPhotoUrl(null);
    setNotes('');
    setError(null);
    setScanMethod('camera');
    onClose();
  };

  const clearAlerts = () => {
    setError(null);
  };

  const handleStepChange = (step: number) => {
    clearAlerts();
    setActiveStep(step);
  };

  const handleQRScan = (value: string) => {
    clearAlerts();
    setScannedQR(value);
  };

  const validateShipmentQR = () => {
    if (!scannedQR) {
      setError('Please scan or enter a shipment QR code');
      return false;
    }
    return true;
  };

  const validatePhoto = () => {
    if (!photoUrl) {
      setError('Please take a photo');
      return false;
    }
    return true;
  };

  // Upload photo mutation
  const uploadPhotoMutation = useMutation({
    mutationFn: async (data: { photoBase64: string; folder: string; geo_latitude?: number; geo_longitude?: number }) => {
      const response = await fetch('/api/uploads/photo', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          photoBase64: data.photoBase64,
          folder: data.folder,
          geo_latitude: data.geo_latitude || null,
          geo_longitude: data.geo_longitude || null,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to upload photo');
      }

      return response.json();
    },
    onError: (error: Error) => {
      notifications.show({
        title: 'Upload Failed',
        message: error.message || 'Failed to upload photo. Please try again.',
        color: 'red',
        icon: <IconAlertTriangle size="1rem" />,
        autoClose: 5000,
      });
    },
  });

  // Scan shipment mutation (ARRIVAL action) with auto cache invalidation
  const scanShipmentMutationInstance = useScanShipmentMutation({
    successMessage: 'Package has been successfully marked as arrived at destination.',
    onSuccess: () => {
      setActiveStep(completedIndex);
      setError(null);

      if (onSuccess) {
        setTimeout(() => {
          onSuccess();
          handleClose();
        }, 2000);
      }
    },
  });

  const handlePhotoUpload = async (base64Data: string) => {
    try {
      const photoResponse = await uploadPhotoMutation.mutateAsync({
        photoBase64: base64Data,
        folder: 'shipment-scans',
      });

      // Ensure the photo URL matches the required format
      const photoUrl = photoResponse.data.photo_url;
      setPhotoUrl(photoUrl);
      setError(null);
    } catch (error) {
      console.error('Error uploading photo:', error);
      if (error instanceof Error) {
        setError(error.message);
      } else {
        setError('Failed to upload photo. Please try again.');
      }
      setPhotoUrl(null);
      setPhotoPreview(null);
    }
  };

  const handlePhotoChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = async (e) => {
        const base64Data = e.target?.result as string;
        setPhotoPreview(base64Data);

        // Upload photo immediately when user selects it
        await handlePhotoUpload(base64Data);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleScanAndAssign = async () => {
    // This modal is specifically for ARRIVAL action - validate required fields
    if (!photoUrl || !scannedQR) {
      setError('Please complete all required fields (QR code and photo)');
      return;
    }

    try {
      const scanData = {
        shipmentId: shipment?.id as string,
        qrValue: scannedQR,
        photoUrl,
        action: 'ARRIVAL' as const,
        notes: notes || undefined,
      };

      await scanShipmentMutationInstance.mutateAsync({ data: scanData });

      if (onSuccess) {
        onSuccess();
      }
      handleClose();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to process arrival. Please try again.';
      setError(errorMessage);
      notifications.show({
        title: 'Error',
        message: errorMessage,
        color: 'red',
        icon: <IconAlertTriangle size="1rem" />,
      });
    }
  };

  if (!isClient) {
    return null;
  }

  return (
    <Modal
      opened={opened}
      onClose={handleClose}
      title={t('markPackageAsArrived')}
      size="lg"
      centered
    >
      <Stack gap="md">
        {error && (
          <Alert
            icon={<IconAlertTriangle size="1rem" />}
            color="red"
            variant="light"
            onClose={() => setError(null)}
            withCloseButton
          >
            {error}
          </Alert>
        )}

        <Stepper
          active={activeStep}
          onStepClick={handleStepChange}
          allowNextStepsSelect={false}
        >
          <Stepper.Step
            label={t('shipmentCode')}
            description={t('scanShipmentCode')}
          >
            <Stack gap="md">
              {!scannedQR && (
                <>
                  <Alert color="blue" variant="light">
                    <Text size="sm">
                      <strong>
                        {t('important')}
                        :
                      </strong>
                      {' '}
                      {t('scanCodeAttachedToPackage')}
                    </Text>
                  </Alert>

                  <Tabs value={scanMethod} onChange={(value) => setScanMethod(value as 'camera' | 'manual')}>
                    <Tabs.List>
                      <Tabs.Tab value="camera" leftSection={<IconCamera size="0.8rem" />}>
                        {t('cameraScan')}
                      </Tabs.Tab>
                      <Tabs.Tab value="manual" leftSection={<IconKeyboard size="0.8rem" />}>
                        {t('manualEntry')}
                      </Tabs.Tab>
                    </Tabs.List>

                    <Tabs.Panel value="camera" pt="md">
                      {(!scannedQR || error) && (
                        <QRScanner
                          onScan={handleQRScan}
                          onError={(e) => setError(e)}
                          expectedFormat="AO_"
                          title={t('scanShipmentCode')}
                          description={t('pointCameraAtShipmentCode')}
                          isActive={scanMethod === 'camera' && opened && activeStep === 0}
                        />
                      )}
                    </Tabs.Panel>

                    <Tabs.Panel value="manual" pt="md">
                      <TextInput
                        label={t('enterShipmentCode')}
                        placeholder={t('enterShipmentCodePlaceholder')}
                        value={scannedQR}
                        onChange={(event) => {
                          clearAlerts();
                          setScannedQR(event.currentTarget.value);
                        }}
                        required
                        style={{ width: '100%' }}
                        description={t('enterShipmentCodeDescription')}
                      />
                    </Tabs.Panel>
                  </Tabs>
                </>
              )}

              {scannedQR && !error && (
                <Alert
                  icon={<IconCheck size="1rem" />}
                  color="green"
                  variant="light"
                  mt="md"
                >
                  <Text size="sm">{t('codeScannedSuccessfully')}</Text>
                </Alert>
              )}

              <Group justify="flex-end" mt="md">
                <Button
                  onClick={() => {
                    if (validateShipmentQR()) {
                      handleStepChange(activeStep + 1);
                    }
                  }}
                >
                  {t('nextStep')}
                </Button>
              </Group>
            </Stack>
          </Stepper.Step>

          <Stepper.Step
            label={t('photo')}
            description={t('takePackagePhoto')}
            icon={<IconCamera size="1.1rem" />}
          >
            <Paper p="md" withBorder>
              <Stack gap="md">
                <Text size="sm" c="dimmed">
                  {t('takePhotoForDocumentation')}
                </Text>

                <input
                  type="file"
                  accept="image/*"
                  capture="environment"
                  onChange={handlePhotoChange}
                  style={{ display: 'none' }}
                  id="photo-input"
                />
                <Button
                  component="label"
                  htmlFor="photo-input"
                  leftSection={<IconCamera size="1rem" />}
                  variant="light"
                  fullWidth
                  loading={uploadPhotoMutation.isPending}
                  disabled={uploadPhotoMutation.isPending}
                >
                  {uploadPhotoMutation.isPending ? t('uploadingPhoto') : t('takePhoto')}
                </Button>

                {photoPreview && (
                  <Box>
                    <Text size="sm" c="dimmed" mb="xs">
                      {t('packagePhoto')}
                      :
                    </Text>
                    <Image
                      src={photoPreview}
                      alt={t('packagePhoto')}
                      height={200}
                      fit="contain"
                      radius="md"
                    />
                    {photoUrl && !error && (
                      <Alert
                        icon={<IconCheck size="1rem" />}
                        color="green"
                        variant="light"
                        mt="md"
                      >
                        {t('photoUploadedSuccessfully')}
                      </Alert>
                    )}
                  </Box>
                )}

                <Group justify="flex-end" mt="md">
                  <Button variant="light" onClick={() => handleStepChange(activeStep - 1)}>
                    {t('back')}
                  </Button>
                  <Button
                    onClick={() => {
                      if (validatePhoto()) {
                        handleStepChange(activeStep + 1);
                      }
                    }}
                  >
                    {t('nextStep')}
                  </Button>
                </Group>
              </Stack>
            </Paper>
          </Stepper.Step>

          <Stepper.Step
            label={t('notes')}
            description={t('addNotes')}
          >
            <Stack gap="md">
              <Textarea
                label={t('notes')}
                placeholder={t('addNotesAboutPackageArrival')}
                value={notes}
                onChange={(event) => {
                  clearAlerts();
                  setNotes(event.currentTarget.value);
                }}
                rows={3}
              />

              <Group justify="flex-end" mt="md">
                <Button variant="light" onClick={() => handleStepChange(activeStep - 1)}>
                  {t('back')}
                </Button>
                <Button
                  onClick={handleScanAndAssign}
                  loading={scanShipmentMutationInstance.isPending}
                  leftSection={<IconCheck size="1rem" />}
                  color="green"
                >
                  {t('markAsArrived')}
                </Button>
              </Group>
            </Stack>
          </Stepper.Step>

          <Stepper.Completed>
            <Stack align="center">
              <IconCheck size="3rem" color="green" />
              <Text fw={600} size="lg">
                {t('arrivalConfirmed')}
              </Text>
              <Text c="dimmed" size="sm" ta="center">
                {t('packageMarkedArrivedAtDestination')}
              </Text>
              <Button mt="md" onClick={handleClose}>{t('close')}</Button>
            </Stack>
          </Stepper.Completed>
        </Stepper>
      </Stack>
    </Modal>
  );
}
