import z from 'zod';
import {
  getNotificationsRequestSchema,
  markAsReadRequestSchema,
  updatePreferencesRequestSchema,
} from './request-transformer';
import {
  notificationSchema,
  notificationListApiResponseSchema,
  unreadCountApiResponseSchema,
  markAsReadApiResponseSchema,
  markAllAsReadApiResponseSchema,
  notificationPreferencesSchema,
  getPreferencesApiResponseSchema,
  updatePreferencesApiResponseSchema,
  filterOptionsSchema,
  filterOptionsApiResponseSchema,
} from './response-transformer';

// Notification Types Enum
export enum NotificationType {
  SHIPMENT_CREATED = 'SHIPMENT_CREATED',
  SHIPMENT_ASSIGNED_QR = 'SHIPMENT_ASSIGNED_QR',
  SHIPMENT_DROPPED_OFF = 'SHIPMENT_DROPPED_OFF',
  SHIPMENT_PICKED_UP = 'SHIPMENT_PICKED_UP',
  SHIPMENT_IN_TRANSIT = 'SHIPMENT_IN_TRANSIT',
  SHIPMENT_ARRIVED = 'SHIPMENT_ARRIVED',
  SHIPMENT_READY_FOR_DELIVERY = 'SHIPMENT_READY_FOR_DELIVERY',
  SHIPMENT_DELIVERED = 'SHIPMENT_DELIVERED',
  SHIPMENT_CANCELLED = 'SHIPMENT_CANCELLED',
  SHIPMENT_EXPIRED = 'SHIPMENT_EXPIRED',
  SHIPMENT_STATUS_CHANGED = 'SHIPMENT_STATUS_CHANGED',
  QR_CODE_ASSIGNED = 'QR_CODE_ASSIGNED',
  PACKAGE_READY_FOR_PICKUP = 'PACKAGE_READY_FOR_PICKUP',
  DELIVERY_REMINDER = 'DELIVERY_REMINDER',
  SYSTEM_ALERT = 'SYSTEM_ALERT',
  USER_REGISTERED = 'USER_REGISTERED',
  USER_EMAIL_VERIFIED = 'USER_EMAIL_VERIFIED',
  OPERATOR_NEEDS_APPROVAL = 'OPERATOR_NEEDS_APPROVAL',
  USER_STATUS_CHANGED = 'USER_STATUS_CHANGED',
  SECURITY_ALERT = 'SECURITY_ALERT',
  SYSTEM_ERROR = 'SYSTEM_ERROR',
  ADMIN_CREATED = 'ADMIN_CREATED',
  BULK_OPERATION_COMPLETED = 'BULK_OPERATION_COMPLETED',
  SYSTEM_MAINTENANCE = 'SYSTEM_MAINTENANCE'
}

// Priority Levels
export enum NotificationPriority {
  LOW = 'LOW',
  NORMAL = 'NORMAL',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

// Infer types from Zod schemas
export type Notification = z.infer<typeof notificationSchema>;
export type NotificationPreferences = z.infer<typeof notificationPreferencesSchema>;

// Request types
export type GetNotificationsRequest = z.infer<typeof getNotificationsRequestSchema>;
export type MarkAsReadRequest = z.infer<typeof markAsReadRequestSchema>;
export type UpdatePreferencesRequest = z.infer<typeof updatePreferencesRequestSchema>;

// Response types
export type NotificationListApiResponse = z.infer<typeof notificationListApiResponseSchema>;
export type UnreadCountApiResponse = z.infer<typeof unreadCountApiResponseSchema>;
export type MarkAsReadApiResponse = z.infer<typeof markAsReadApiResponseSchema>;
export type MarkAllAsReadApiResponse = z.infer<typeof markAllAsReadApiResponseSchema>;
export type GetPreferencesApiResponse = z.infer<typeof getPreferencesApiResponseSchema>;
export type UpdatePreferencesApiResponse = z.infer<typeof updatePreferencesApiResponseSchema>;
export type FilterOptionsResponse = z.infer<typeof filterOptionsSchema>;
export type FilterOptionsApiResponse = z.infer<typeof filterOptionsApiResponseSchema>;

// Query props interfaces
export interface GetNotificationsQueryProps {
  params?: {
    page?: number;
    limit?: number;
    search?: string;
    read?: boolean;
    type?: string;
    priority?: string;
    shipmentId?: string;
    fromDate?: string;
    toDate?: string;
  };
  enabled?: boolean;
}

export interface GetUnreadCountQueryProps {
  enabled?: boolean;
}

export interface MarkAsReadQueryProps {
  notificationId: string;
  enabled?: boolean;
}

export interface MarkAllAsReadQueryProps {
  enabled?: boolean;
}

export interface GetPreferencesQueryProps {
  enabled?: boolean;
}

export interface UpdatePreferencesQueryProps {
  data: UpdatePreferencesRequest;
  enabled?: boolean;
}

export interface GetFilterOptionsQueryProps {
  enabled?: boolean;
}

// Notification state interface for UI components
export interface NotificationState {
  notifications: Notification[];
  unreadCount: number;
  loading: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  preferences: NotificationPreferences | null;
}

// Notification filter interface
export interface NotificationFilters {
  read?: boolean;
  type?: NotificationType;
  priority?: NotificationPriority;
  shipmentId?: string;
  fromDate?: string;
  toDate?: string;
}

// Notification polling configuration
export interface NotificationPollingConfig {
  interval: number;
  enabled: boolean;
  onlyWhenVisible: boolean;
}
