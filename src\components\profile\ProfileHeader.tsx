import {
  Group, Title, Badge,
} from '@mantine/core';
import { IconUser } from '@tabler/icons-react';
import useTranslation from 'next-translate/useTranslation';
import { useRouter } from 'next/router';
import type { ProfileHeaderProps } from './types';

export default function ProfileHeader({ user, userType }: ProfileHeaderProps) {
  const { t } = useTranslation('profile');
  const router = useRouter();
  const isRTL = router.locale === 'ar';

  const getUserTypeLabel = (type: string) => {
    switch (type) {
      case 'CUSTOMER': return t('customer');
      case 'ACCESS_OPERATOR': return t('accessOperator');
      case 'CAR_OPERATOR': return t('carOperator');
      case 'ADMIN': return t('admin');
      default: return type?.replace('_', ' ');
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'ACTIVE': return t('active');
      case 'INACTIVE': return t('inactive');
      case 'PENDING': return t('pending');
      default: return status;
    }
  };

  const getApprovalLabel = (approved: boolean | null | undefined) => {
    if (approved === true) return t('approved');
    if (approved === false) return t('notApproved');
    return null; // Don't show anything if approval status is null/undefined
  };

  const getApprovalColor = (approved: boolean | null | undefined) => {
    if (approved === true) return 'green';
    if (approved === false) return 'red';
    return 'gray';
  };

  return (
    <Group justify="space-between" mb="lg">
      {/* For RTL: Profile title goes on the right, badges on the left */}
      {isRTL ? (
        <>
          <Group gap="xs">
            <Badge color={user.status === 'ACTIVE' ? 'green' : 'yellow'} variant="light">
              {getStatusLabel(user.status)}
            </Badge>
            {getApprovalLabel(user.approved) && (
              <Badge color={getApprovalColor(user.approved)} variant="light">
                {getApprovalLabel(user.approved)}
              </Badge>
            )}
            <Badge variant="outline">
              {getUserTypeLabel(userType)}
            </Badge>
          </Group>
          <div>
            <Title order={2} mb="xs">
              <IconUser
                size="1.5rem"
                style={{
                  marginLeft: '0.5rem',
                  display: 'inline',
                }}
              />
              {t('profile')}
            </Title>
          </div>
        </>
      ) : (
        <>
          <div>
            <Title order={2} mb="xs">
              <IconUser
                size="1.5rem"
                style={{
                  marginRight: '0.5rem',
                  display: 'inline',
                }}
              />
              {t('profile')}
            </Title>
          </div>
          <Group gap="xs">
            <Badge color={user.status === 'ACTIVE' ? 'green' : 'yellow'} variant="light">
              {getStatusLabel(user.status)}
            </Badge>
            {getApprovalLabel(user.approved) && (
              <Badge color={getApprovalColor(user.approved)} variant="light">
                {getApprovalLabel(user.approved)}
              </Badge>
            )}
            <Badge variant="outline">
              {getUserTypeLabel(userType)}
            </Badge>
          </Group>
        </>
      )}
    </Group>
  );
}
