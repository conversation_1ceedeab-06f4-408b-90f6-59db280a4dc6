import { NextApiRequest, NextApiResponse } from 'next';
import { ZodError } from 'zod';
import { getJwt } from '../../src/utils';
import { BACKEND_API } from '../../src/lib/axios';
import { API_ENDPOINT, HTTP_CODE } from '../../src/data';
import {
  getNotificationsRequestSchema,
  validateNotificationListResponse,
} from '../../src/requests/notifications';
import createApiError from '../../src/utils/create-api-error';

const apiMethods = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
} as const;

async function handleGetNotifications(req: NextApiRequest, res: NextApiResponse, token: string) {
  try {
    const validatedParams = getNotificationsRequestSchema.parse(req.query);

    const queryParams = new URLSearchParams();
    Object.entries(validatedParams).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, String(value));
      }
    });

    const queryString = queryParams.toString();
    const baseUrl = API_ENDPOINT.notifications.base;
    const url = queryString ? `${baseUrl}?${queryString}` : baseUrl;

    const response = await BACKEND_API.get(url, {
      headers: {
        Authorization: token,
      },
    });

    const validatedResponse = validateNotificationListResponse(response.data);

    return res.status(HTTP_CODE.SUCCESS).json(validatedResponse);
  } catch (error: unknown) {
    if (error && typeof error === 'object' && 'response' in error) {
      const apiError = error as { response?: { status?: number; data?: unknown } };
      return res.status(apiError.response?.status || HTTP_CODE.INTERNAL_SERVER_ERROR).json(
        apiError.response?.data || createApiError('Backend API error', 'BACKEND_ERROR'),
      );
    }

    if (error instanceof ZodError) {
      return res.status(HTTP_CODE.BAD_REQUEST).json(
        createApiError('Invalid query parameters', 'VALIDATION_ERROR', error.errors),
      );
    }

    return res.status(HTTP_CODE.INTERNAL_SERVER_ERROR).json(
      createApiError('Failed to fetch notifications', 'INTERNAL_ERROR'),
    );
  }
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { method } = req;

  try {
    const { token } = await getJwt(req);
    if (!token) {
      return res.status(HTTP_CODE.UNAUTHORIZED).json(
        createApiError('Authentication required', 'UNAUTHORIZED'),
      );
    }

    switch (method) {
      case apiMethods.GET:
        return await handleGetNotifications(req, res, token as string);

      case apiMethods.POST:
      case apiMethods.PUT:
      default:
        return res.status(HTTP_CODE.METHOD_NOT_ALLOWED).json(
          createApiError(`Method ${method} not allowed`, 'METHOD_NOT_ALLOWED'),
        );
    }
  } catch (error) {
    if (error instanceof ZodError) {
      return res.status(HTTP_CODE.BAD_REQUEST).json(
        createApiError('Invalid request data', 'VALIDATION_ERROR', error.errors),
      );
    }

    return res.status(HTTP_CODE.INTERNAL_SERVER_ERROR).json(
      createApiError('Internal server error', 'INTERNAL_ERROR'),
    );
  }
}
