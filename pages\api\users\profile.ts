import { NextApiRequest, NextApiResponse } from 'next';
import { getJwt } from '../../../src/utils';
import { BACKEND_API } from '../../../src/lib/axios';
import { API_ENDPOINT, HTTP_CODE } from '../../../src/data';
import {
  profileRequestSchemas,
  profileReturnParams,
} from '../../../src/requests';
import {
  getProfileApiResponseSchema,
  updateProfileApiResponseSchema,
} from '../../../src/requests/profile/response-transformer';
import createApiError from '../../../src/utils/create-api-error';

const apiMethods = {
  GET: 'GET',
  PUT: 'PUT',
} as const;

async function handleGet(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { token } = await getJwt(req);
    // Use proper logging instead of console.log
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.log('token', token);
    }

    const response = await BACKEND_API.get(API_ENDPOINT.users.profile, {
      headers: {
        Authorization: token,
      },
      // passing params and pagination following your pattern
      params: { ...profileReturnParams(req) },
    });

    // Validate and transform response using schema
    const validatedResponse = getProfileApiResponseSchema.parse(response.data);
    return res.status(HTTP_CODE.SUCCESS).json(validatedResponse);
  } catch (e) {
    const error = createApiError({ error: e });
    return res.status(error.code).json(error);
  }
}

async function handlePut(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { token } = await getJwt(req);

    // Validate and transform the request body using the backend schema
    const backendPayload = profileRequestSchemas.updateProfileBackend.parse(req.body);

    const response = await BACKEND_API.put(
      API_ENDPOINT.users.profile,
      backendPayload,
      {
        headers: {
          Authorization: token,
        },
      },
    );

    // Validate and transform response using schema
    const validatedResponse = updateProfileApiResponseSchema.parse(response.data);
    return res.status(HTTP_CODE.SUCCESS).json(validatedResponse);
  } catch (e) {
    const error = createApiError({ error: e });
    return res.status(error.code).json(error);
  }
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === apiMethods.GET) {
    return handleGet(req, res);
  }

  if (req.method === apiMethods.PUT) {
    return handlePut(req, res);
  }

  res.setHeader('Allow', ['GET', 'PUT']);
  return res.status(HTTP_CODE.METHOD_NOT_ALLOWED).json({
    success: false,
    message: `Method ${req.method} not allowed`,
  });
}
