/* eslint-disable linebreak-style */
import type { NextApiRequest, NextApiResponse } from 'next';
import { getJwt } from '../../../src/utils';
import { BACKEND_API } from '../../../src/lib/axios';
import { API_ENDPOINT, HTTP_CODE } from '../../../src/data';
import createApiError from '../../../src/utils/create-api-error';
import { qrLabelRequestSchemas } from '../../../src/requests/qr-labels/request-transformer';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(HTTP_CODE.METHOD_NOT_ALLOWED).json({
      success: false,
      message: 'Method not allowed',
    });
  }

  try {
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');

    const { token } = await getJwt(req);
    if (!token) {
      return res.status(HTTP_CODE.UNAUTHORIZED).json({
        success: false,
        message: 'Authentication required',
      });
    }

    // Validate request body using frontend schema
    const validatedBody = qrLabelRequestSchemas.generatePDFFrontend.parse(req.body);

    // Prepare backend payload (identical in this case, but keep abstraction)
    const backendPayload = {
      count: validatedBody.count,
    };

    // Send request to backend API expecting PDF binary
    const response = await BACKEND_API.post(
      API_ENDPOINT.qrLabels.generatePDF,
      backendPayload,
      {
        headers: {
          Authorization: token,
          'Content-Type': 'application/json',
        },
        responseType: 'arraybuffer',
      },
    );

    // Forward PDF to client
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', 'attachment; filename="qr_labels.pdf"');

    // Convert ArrayBuffer to Buffer before sending
    const buffer = Buffer.from(response.data);
    res.setHeader('Content-Length', buffer.length.toString());
    return res.status(200).send(buffer);
  } catch (error: unknown) {
    const apiError = createApiError({ error });
    return res.status(apiError.code).json(apiError);
  }
}
