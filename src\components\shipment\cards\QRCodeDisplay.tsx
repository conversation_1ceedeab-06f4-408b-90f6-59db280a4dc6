/* eslint-disable react/require-default-props */
import { useRef, useState } from 'react';
import {
  Paper,
  Stack,
  Group,
  Text,
  Button,
  Box,
  Alert,
  Loader,
} from '@mantine/core';
import {
  IconDownload,
  IconQrcode,
  IconAlertCircle,
} from '@tabler/icons-react';
import QRCode from 'react-qr-code';
import html2canvas from 'html2canvas';
import { notifications } from '@mantine/notifications';

interface QRCodeDisplayProps {
  value: string;
  title?: string;
  description?: string;
  size?: number;
  downloadFileName?: string;
}

export default function QRCodeDisplay({
  value,
  title = 'QR Code',
  description,
  size = 200,
  downloadFileName = 'qr-code',
}: QRCodeDisplayProps) {
  const qrRef = useRef<HTMLDivElement>(null);
  const [isDownloading, setIsDownloading] = useState(false);

  const handleDownload = async () => {
    if (!qrRef.current) {
      notifications.show({
        title: 'Error',
        message: 'QR code not found',
        color: 'red',
        icon: <IconAlertCircle size="1rem" />,
      });
      return;
    }

    setIsDownloading(true);

    try {
      // Create a temporary container with better styling for the download
      const tempContainer = document.createElement('div');
      tempContainer.style.position = 'absolute';
      tempContainer.style.left = '-9999px';
      tempContainer.style.top = '-9999px';
      tempContainer.style.background = 'white';
      tempContainer.style.padding = '20px';
      tempContainer.style.borderRadius = '8px';
      tempContainer.style.fontFamily = 'Arial, sans-serif';

      // Create the content for download
      tempContainer.innerHTML = `
        <div style="text-align: center; background: white; padding: 20px; border-radius: 8px;">
          <h3 style="margin: 0 0 10px 0; color: #333; font-size: 18px;">${title}</h3>
          ${description ? `<p style="margin: 0 0 15px 0; color: #666; font-size: 14px;">${description}</p>` : ''}
          <div style="display: inline-block; padding: 10px; background: white; border: 2px solid #e9ecef; border-radius: 8px;">
            ${qrRef.current.innerHTML}
          </div>
          <p style="margin: 15px 0 0 0; color: #666; font-size: 12px;">Generated on ${new Date().toLocaleDateString()}</p>
        </div>
      `;

      document.body.appendChild(tempContainer);

      // Generate the image
      const canvas = await html2canvas(tempContainer, {
        backgroundColor: 'white',
        scale: 2, // Higher resolution
        useCORS: true,
        allowTaint: true,
      });

      // Remove the temporary container
      document.body.removeChild(tempContainer);

      // Convert to blob and download
      canvas.toBlob((blob) => {
        if (blob) {
          const url = URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `${downloadFileName}-${new Date().getTime()}.png`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          URL.revokeObjectURL(url);

          notifications.show({
            title: 'Success',
            message: 'QR code downloaded successfully',
            color: 'green',
          });
        } else {
          throw new Error('Failed to generate image');
        }
      }, 'image/png');
    } catch (error) {
      notifications.show({
        title: 'Download Failed',
        message: 'Failed to download QR code. Please try again.',
        color: 'red',
        icon: <IconAlertCircle size="1rem" />,
      });
    } finally {
      setIsDownloading(false);
    }
  };

  if (!value || value.trim() === '') {
    return (
      <Alert icon={<IconAlertCircle size="1rem" />} color="orange" title="No QR Code Available">
        No pickup code available for this shipment.
      </Alert>
    );
  }

  return (
    <Paper p="md" withBorder>
      <Stack gap="md" align="center">
        <Group gap="xs">
          <IconQrcode size="1.2rem" color="purple" />
          <Text fw={600}>{title}</Text>
        </Group>

        {description && (
          <Text size="sm" c="dimmed" ta="center">
            {description}
          </Text>
        )}

        <Box
          ref={qrRef}
          style={{
            padding: '16px',
            backgroundColor: 'white',
            borderRadius: '8px',
            border: '2px solid #e9ecef',
          }}
        >
          <QRCode
            value={value}
            size={size}
            style={{ height: 'auto', maxWidth: '100%', width: '100%' }}
            viewBox="0 0 256 256"
          />
        </Box>

        <Text size="sm" fw={500} c="blue" ta="center" style={{ fontFamily: 'monospace' }}>
          {value}
        </Text>

        <Button
          leftSection={isDownloading ? <Loader size="1rem" /> : <IconDownload size="1rem" />}
          onClick={handleDownload}
          disabled={isDownloading}
          variant="light"
          color="blue"
        >
          {isDownloading ? 'Generating...' : 'Download as PNG'}
        </Button>
      </Stack>
    </Paper>
  );
}
