import { NextApiRequest, NextApiResponse } from 'next';
import { BACKEND_API } from '../../../src/lib/axios';
import { API_ENDPOINT, HTTP_CODE } from '../../../src/data';
import { authRequestSchemas } from '../../../src/requests';
import createApiError from '../../../src/utils/create-api-error';
import { registerApiResponseSchema } from '../../../src/requests/auth/response-transform';

// eslint-disable-next-line sonarjs/cognitive-complexity
async function handlePost(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Validate and transform the request body using the backend schema
    const validatedData = authRequestSchemas.registrationBackend.parse(req.body);

    const response = await BACKEND_API.post(
      API_ENDPOINT.auth.register,
      validatedData,
    );

    const validatedResponse = registerApiResponseSchema.parse(response.data);
    return res.status(HTTP_CODE.SUCCESS).json(validatedResponse);
  } catch (e: unknown) {
    if (e && typeof e === 'object' && 'code' in e) {
      const connectionError = e as { code: string };
      if (connectionError.code === 'ECONNREFUSED') {
        return res.status(HTTP_CODE.SERVICE_UNAVAILABLE).json({
          success: false,
          type: 'BACKEND_UNAVAILABLE',
          message: 'server is not running.',
          details: 'Connection refused to backend API. Make sure the backend server is running.',
        });
      }
    }

    if (e && typeof e === 'object' && 'response' in e) {
      const axiosError = e as { response?: { data?: Record<string, unknown> } };
      const errorData = axiosError.response?.data;

      if (
        errorData
        && ((errorData?.error as { type: string })?.type === 'QR_SYSTEM_ERROR'
          || (errorData as { type: string })?.type === 'QR_SYSTEM_ERROR'
          || ((errorData?.error as { message: string })?.message
            && (errorData.error as { message: string }).message.includes(
              'QR code system',
            )))
      ) {
        return res.status(HTTP_CODE.SERVICE_UNAVAILABLE).json({
          success: false,
          type: 'QR_SYSTEM_ERROR',
          message:
            'QR code system is temporarily unavailable. Registration is currently disabled. Please try again later.',
          details:
            'The QR code system required for user registration is currently experiencing issues.',
        });
      }
    }

    const error = createApiError({ error: e });
    return res.status(error.code).json(error);
  }
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  if (req.method === 'POST') {
    return handlePost(req, res);
  }

  res.setHeader('Allow', ['POST']);
  return res.status(HTTP_CODE.METHOD_NOT_ALLOWED).json({
    success: false,
    message: `Method ${req.method} not allowed`,
  });
}
