import { NextApiResponse } from 'next';
import { z } from 'zod';
import { HTTP_CODE } from '../data';

/**
 * Creates a standardized API response with schema validation
 * @param res - Next.js API response object
 * @param schema - Zod schema for response validation and transformation
 * @param data - Raw data from backend to be validated and transformed
 * @returns Validated and transformed response
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const createApiResponse = <T = any>(
  res: NextApiResponse,
  schema: z.ZodType<T>,
  data: unknown,
): void => {
  try {
    // Validate and transform the data using the provided schema
    const validatedData = schema.parse(data);

    res.status(HTTP_CODE.SUCCESS).json(validatedData);
  } catch (error) {
    // If schema validation fails, return a validation error
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.error('Response schema validation failed:', error);
    }

    res.status(HTTP_CODE.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Response validation failed',
      error: process.env.NODE_ENV === 'development' ? error : undefined,
    });
  }
};

export default createApiResponse;
