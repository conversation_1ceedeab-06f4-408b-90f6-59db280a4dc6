import { useState } from 'react';
import {
  Container,
  Loader,
  Center,
} from '@mantine/core';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import { useIsClient } from '../../../src/hooks/useIsClient';
import { COShipmentsTable, COShipmentDetailView, PickupProcessModal } from '../../../src/components/shipment';
import COAssignToAOModal from '../../../src/components/shipment/modals/COAssignToAOModal';
import { Shipment } from '../../../src/requests/shipment';

export default function COAvailableShipmentsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const isClient = useIsClient();
  const [selectedShipment, setSelectedShipment] = useState<Shipment | null>(null);
  const [pickupShipment, setPickupShipment] = useState<Shipment | null>(null);
  const [assignShipment, setAssignShipment] = useState<Shipment | null>(null);

  const handleViewShipment = (shipment: Shipment) => {
    setSelectedShipment(shipment);
  };

  const handlePickupShipment = (shipment: Shipment) => {
    setPickupShipment(shipment);
  };

  const handleAssignShipment = (shipment: Shipment) => {
    setAssignShipment(shipment);
  };

  const handlePickupSuccess = () => {
    setPickupShipment(null);
    setSelectedShipment(null); // Return to list view after successful pickup
  };

  const handleAssignSuccess = () => {
    setAssignShipment(null);
    setSelectedShipment(null); // Return to list view after successful assign
  };

  const handleBackToList = () => {
    setSelectedShipment(null);
  };

  // Redirect if not authenticated or not CO user
  if (isClient && status === 'authenticated' && session?.user?.user_type !== 'CAR_OPERATOR') {
    router.push('/');
    return null;
  }

  // Show loading while checking authentication
  if (!isClient || status === 'loading') {
    return (
      <Center h="100vh">
        <Loader size="lg" />
      </Center>
    );
  }

  // Redirect to login if not authenticated
  if (status === 'unauthenticated') {
    router.push('/auth/login');
    return null;
  }

  return (
    <Container size="xl" py="md">
      {selectedShipment ? (
        <COShipmentDetailView
          shipment={selectedShipment}
          onBack={handleBackToList}
          onPickup={handlePickupShipment}
          onAssignToAO={handleAssignShipment}
        />
      ) : (
        <COShipmentsTable
          onViewShipment={handleViewShipment}
          onPickup={handlePickupShipment}
          onAssignShipment={handleAssignShipment}
        />
      )}

      <PickupProcessModal
        opened={!!pickupShipment}
        onClose={() => setPickupShipment(null)}
        shipment={pickupShipment}
        onSuccess={handlePickupSuccess}
        pickupType="CO_PICKUP"
      />

      <COAssignToAOModal
        opened={!!assignShipment}
        onClose={() => setAssignShipment(null)}
        shipment={assignShipment}
        onSuccess={handleAssignSuccess}
      />
    </Container>
  );
}
