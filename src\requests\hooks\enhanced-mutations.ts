/* eslint-disable max-lines */
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { notifications } from '@mantine/notifications';
import { IconCheck, IconAlertCircle } from '@tabler/icons-react';
import React from 'react';
import { getTranslation } from '../../utils/translation';

// Import mutation functions with invalidation
import {
  createShipmentMutationWithInvalidation,
  cancelShipmentMutationWithInvalidation,
  scanShipmentMutationWithInvalidation,
  deliverShipmentMutationWithInvalidation,
} from '../shipment/calls';

import {
  createAccessPointMutationWithInvalidation,
  updateAccessPointMutationWithInvalidation,
} from '../access-point/calls';

import {
  updateProfileMutationWithInvalidation,
} from '../profile/calls';

import {
  loginMutationWithInvalidation,
  registerMutationWithInvalidation,
  changePasswordMutationWithInvalidation,
} from '../auth/calls';

import {
  generateForShipmentMutationWithInvalidation,
} from '../qr-labels/calls';

import { CacheInvalidationManager } from '../cache-invalidation';

/**
 * Helper function to map server error messages to translation keys
 */
const mapErrorMessageToKey = (message: string): string | null => {
  const errorMappings: Record<string, string> = {
    'No Car Operator available for route': 'noCarOperatorAvailable',
    'Route not available': 'routeNotAvailable',
    'Failed to create shipment': 'shipmentCreationFailed',
    'Invalid credentials': 'invalidCredentials',
    'Email already exists': 'emailAlreadyExists',
    'Account is locked': 'accountLocked',
    'Account is disabled': 'accountDisabled',
    'Session expired': 'sessionExpired',
    'Token expired': 'tokenExpired',
    'Permission denied': 'permissionDenied',
    'Resource not found': 'resourceNotFound',
    'Internal server error': 'internalServerError',
  };

  // Check for exact matches first
  // eslint-disable-next-line no-restricted-syntax
  for (const [serverMessage, key] of Object.entries(errorMappings)) {
    if (message.includes(serverMessage)) {
      return key;
    }
  }

  return null;
};

/**
 * Helper function to get translated error messages
 */
// eslint-disable-next-line complexity, sonarjs/cognitive-complexity
const getTranslatedErrorMessage = async (error: unknown, fallbackKey: string = 'generalError'): Promise<string> => {
  try {
    const t = await getTranslation('error');

    // Try to extract error message from different error formats
    let errorKey = fallbackKey;
    let errorMessage = '';

    if (typeof error === 'string') {
      errorMessage = error;
    } else if (error && typeof error === 'object') {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const errorObj = error as Record<string, any>;

      // Check for backend error structure
      if (errorObj.message) {
        if (typeof errorObj.message === 'object' && errorObj.message.key) {
          errorKey = errorObj.message.key;
          errorMessage = errorObj.message.fallback || '';
        } else if (typeof errorObj.message === 'string') {
          errorMessage = errorObj.message;
        }
      }

      // Check for axios error structure
      if (errorObj.response?.data?.message) {
        if (typeof errorObj.response.data.message === 'object' && errorObj.response.data.message.key) {
          errorKey = errorObj.response.data.message.key;
          errorMessage = errorObj.response.data.message.fallback || '';
        } else if (typeof errorObj.response.data.message === 'string') {
          errorMessage = errorObj.response.data.message;
        }
      }

      // Check for 500 status code
      if (errorObj.response?.status === 500) {
        errorKey = 'internalServerError';
      }
    }

    // If we have an error message, try to map it to a translation key
    if (errorMessage) {
      const mappedKey = mapErrorMessageToKey(errorMessage);
      if (mappedKey) {
        errorKey = mappedKey;
      }
    }

    // Try to translate the error key
    const translatedMessage = t(errorKey);

    // If translation exists and is different from the key, use it
    if (translatedMessage && translatedMessage !== errorKey) {
      return translatedMessage;
    }

    // If we have an error message from backend and no translation was found, use the original message
    if (errorMessage) {
      return errorMessage;
    }

    // Fallback to general error translation
    const fallbackTranslation = t(fallbackKey);
    return fallbackTranslation !== fallbackKey ? fallbackTranslation : 'An error occurred';
  } catch (translationError) {
    // If translation fails, return a basic error message
    return 'An error occurred';
  }
};

/**
 * Helper function to get translated success/error titles
 */
const getTranslatedTitles = async () => {
  try {
    const tCommon = await getTranslation('common');
    return {
      success: tCommon('success') !== 'success' ? tCommon('success') : 'Success',
      error: tCommon('error') !== 'error' ? tCommon('error') : 'Error',
    };
  } catch {
    return {
      success: 'Success',
      error: 'Error',
    };
  }
};

/**
 * Custom hooks that provide mutations with automatic cache invalidation
 * and consistent success/error handling
 */

interface MutationOptions {
  onSuccess?: () => void;
  onError?: (error: unknown) => void;
  successMessage?: string;
  errorMessage?: string;
  showNotifications?: boolean;
}

/**
 * Hook for create shipment mutation with auto-invalidation
 */
export const useCreateShipmentMutation = (options: MutationOptions = {}) => {
  const queryClient = useQueryClient();
  const {
    onSuccess,
    onError,
    successMessage = 'Shipment created successfully',
    showNotifications = true,
  } = options;

  return useMutation({
    ...createShipmentMutationWithInvalidation(queryClient),
    onSuccess: async (...args) => {
      if (showNotifications) {
        const titles = await getTranslatedTitles();
        notifications.show({
          title: titles.success,
          message: successMessage,
          color: 'green',
          icon: React.createElement(IconCheck, { size: '1rem' }),
        });
      }
      onSuccess?.();
    },
    onError: async (error: unknown) => {
      if (showNotifications) {
        const titles = await getTranslatedTitles();
        const translatedErrorMessage = await getTranslatedErrorMessage(error, 'generalError');

        notifications.show({
          title: titles.error,
          message: translatedErrorMessage,
          color: 'red',
          icon: React.createElement(IconAlertCircle, { size: '1rem' }),
        });
      }
      onError?.(error);
    },
  });
};

/**
 * Hook for cancel shipment mutation with auto-invalidation
 */
export const useCancelShipmentMutation = (options: MutationOptions = {}) => {
  const queryClient = useQueryClient();
  const {
    onSuccess,
    onError,
    successMessage,
    showNotifications = true,
  } = options;

  return useMutation({
    ...cancelShipmentMutationWithInvalidation(queryClient),
    onSuccess: async (...args) => {
      if (showNotifications) {
        const titles = await getTranslatedTitles();
        const tShipments = await getTranslation('shipments');
        const defaultSuccessMessage = tShipments('shipmentCancelledSuccess') !== 'shipmentCancelledSuccess'
          ? tShipments('shipmentCancelledSuccess')
          : 'Shipment cancelled successfully';

        notifications.show({
          title: titles.success,
          message: successMessage || defaultSuccessMessage,
          color: 'green',
          icon: React.createElement(IconCheck, { size: '1rem' }),
        });
      }
      onSuccess?.();
    },
    onError: async (error: unknown) => {
      if (showNotifications) {
        const titles = await getTranslatedTitles();
        const translatedErrorMessage = await getTranslatedErrorMessage(error, 'generalError');

        notifications.show({
          title: titles.error,
          message: translatedErrorMessage,
          color: 'red',
          icon: React.createElement(IconAlertCircle, { size: '1rem' }),
        });
      }
      onError?.(error);
    },
  });
};

/**
 * Hook for scan shipment mutation with auto-invalidation
 */
export const useScanShipmentMutation = (options: MutationOptions = {}) => {
  const queryClient = useQueryClient();
  const {
    onSuccess,
    onError,
    successMessage,
    errorMessage = 'Failed to assign shipment',
    showNotifications = true,
  } = options;

  return useMutation({
    ...scanShipmentMutationWithInvalidation(queryClient),
    onSuccess: (...args) => {
      if (showNotifications) {
        notifications.show({
          title: 'Success',
          message: successMessage,
          color: 'green',
          icon: React.createElement(IconCheck, { size: '1rem' }),
        });
      }
      onSuccess?.();
    },
    onError: (error: unknown) => {
      if (showNotifications) {
        notifications.show({
          title: 'Error',
          message: errorMessage,
          color: 'red',
          icon: React.createElement(IconAlertCircle, { size: '1rem' }),
        });
      }
      onError?.(error);
    },
  });
};

/**
 * Hook for deliver shipment mutation with auto-invalidation
 */
export const useDeliverShipmentMutation = (options: MutationOptions = {}) => {
  const queryClient = useQueryClient();
  const {
    onSuccess,
    onError,
    successMessage = 'Shipment delivered successfully',
    errorMessage = 'Failed to deliver shipment',
    showNotifications = true,
  } = options;

  return useMutation({
    ...deliverShipmentMutationWithInvalidation(queryClient),
    onSuccess: (...args) => {
      if (showNotifications) {
        notifications.show({
          title: 'Success',
          message: successMessage,
          color: 'green',
          icon: React.createElement(IconCheck, { size: '1rem' }),
        });
      }
      onSuccess?.();
    },
    onError: (error: unknown) => {
      if (showNotifications) {
        notifications.show({
          title: 'Error',
          message: errorMessage,
          color: 'red',
          icon: React.createElement(IconAlertCircle, { size: '1rem' }),
        });
      }
      onError?.(error);
    },
  });
};

/**
 * Hook for create access point mutation with auto-invalidation
 */
export const useCreateAccessPointMutation = (options: MutationOptions = {}) => {
  const queryClient = useQueryClient();
  const {
    onSuccess,
    onError,
    successMessage = 'Access point created successfully',
    errorMessage = 'Failed to create access point',
    showNotifications = true,
  } = options;

  return useMutation({
    ...createAccessPointMutationWithInvalidation(queryClient),
    onSuccess: (...args) => {
      if (showNotifications) {
        notifications.show({
          title: 'Success',
          message: successMessage,
          color: 'green',
          icon: React.createElement(IconCheck, { size: '1rem' }),
        });
      }
      onSuccess?.();
    },
    onError: (error: unknown) => {
      if (showNotifications) {
        notifications.show({
          title: 'Error',
          message: errorMessage,
          color: 'red',
          icon: React.createElement(IconAlertCircle, { size: '1rem' }),
        });
      }
      onError?.(error);
    },
  });
};

/**
 * Hook for update access point mutation with auto-invalidation
 */
export const useUpdateAccessPointMutation = (options: MutationOptions = {}) => {
  const queryClient = useQueryClient();
  const {
    onSuccess,
    onError,
    successMessage = 'Access point updated successfully',
    errorMessage = 'Failed to update access point',
    showNotifications = true,
  } = options;

  return useMutation({
    ...updateAccessPointMutationWithInvalidation(queryClient),
    onSuccess: (...args) => {
      if (showNotifications) {
        notifications.show({
          title: 'Success',
          message: successMessage,
          color: 'green',
          icon: React.createElement(IconCheck, { size: '1rem' }),
        });
      }
      onSuccess?.();
    },
    onError: (error: unknown) => {
      if (showNotifications) {
        notifications.show({
          title: 'Error',
          message: errorMessage,
          color: 'red',
          icon: React.createElement(IconAlertCircle, { size: '1rem' }),
        });
      }
      onError?.(error);
    },
  });
};

/**
 * Hook for update profile mutation with auto-invalidation
 */
export const useUpdateProfileMutation = (options: MutationOptions = {}) => {
  const queryClient = useQueryClient();
  const {
    onSuccess,
    successMessage = 'Profile updated successfully',
    showNotifications = true,
  } = options;

  return useMutation({
    ...updateProfileMutationWithInvalidation(queryClient),
    onSuccess: (...args) => {
      if (showNotifications) {
        notifications.show({
          title: 'Success',
          message: successMessage,
          color: 'green',
          icon: React.createElement(IconCheck, { size: '1rem' }),
        });
      }
      onSuccess?.();
    },
  });
};

/**
 * Hook for login mutation with auto-invalidation
 */
export const useLoginMutation = (options: MutationOptions = {}) => {
  const queryClient = useQueryClient();
  const {
    onSuccess,
    onError,
    successMessage = 'Login successful',
    showNotifications = true,
  } = options;

  return useMutation({
    ...loginMutationWithInvalidation(queryClient),
    onSuccess: async (...args) => {
      if (showNotifications) {
        const titles = await getTranslatedTitles();
        notifications.show({
          title: titles.success,
          message: successMessage,
          color: 'green',
          icon: React.createElement(IconCheck, { size: '1rem' }),
        });
      }
      onSuccess?.();
    },
    onError: async (error: unknown) => {
      if (showNotifications) {
        const titles = await getTranslatedTitles();
        const translatedErrorMessage = await getTranslatedErrorMessage(error, 'generalError');

        notifications.show({
          title: titles.error,
          message: translatedErrorMessage,
          color: 'red',
          icon: React.createElement(IconAlertCircle, { size: '1rem' }),
        });
      }
      onError?.(error);
    },
  });
};

/**
 * Hook for register mutation with auto-invalidation
 */
export const useRegisterMutation = (options: MutationOptions = {}) => {
  const queryClient = useQueryClient();
  const {
    onSuccess,
    onError,
    successMessage = 'Registration successful',
    showNotifications = true,
  } = options;

  return useMutation({
    ...registerMutationWithInvalidation(queryClient),
    onSuccess: async (...args) => {
      if (showNotifications) {
        const titles = await getTranslatedTitles();
        notifications.show({
          title: titles.success,
          message: successMessage,
          color: 'green',
          icon: React.createElement(IconCheck, { size: '1rem' }),
        });
      }
      onSuccess?.();
    },
    onError: async (error: unknown) => {
      if (showNotifications) {
        const titles = await getTranslatedTitles();
        const translatedErrorMessage = await getTranslatedErrorMessage(error, 'generalError');

        notifications.show({
          title: titles.error,
          message: translatedErrorMessage,
          color: 'red',
          icon: React.createElement(IconAlertCircle, { size: '1rem' }),
        });
      }
      onError?.(error);
    },
  });
};

/**
 * Hook for change password mutation with auto-invalidation
 */
export const useChangePasswordMutation = (options: MutationOptions = {}) => {
  const queryClient = useQueryClient();
  const {
    onSuccess,
    onError,
    successMessage = 'Password changed successfully',
    errorMessage = 'Failed to change password',
    showNotifications = true,
  } = options;

  return useMutation({
    ...changePasswordMutationWithInvalidation(queryClient),
    onSuccess: (...args) => {
      if (showNotifications) {
        notifications.show({
          title: 'Success',
          message: successMessage,
          color: 'green',
          icon: React.createElement(IconCheck, { size: '1rem' }),
        });
      }
      onSuccess?.();
    },
    onError: (error: unknown) => {
      if (showNotifications) {
        notifications.show({
          title: 'Error',
          message: errorMessage,
          color: 'red',
          icon: React.createElement(IconAlertCircle, { size: '1rem' }),
        });
      }
      onError?.(error);
    },
  });
};

/**
 * Hook for generate QR label mutation with auto-invalidation
 */
export const useGenerateQRLabelMutation = (options: MutationOptions = {}) => {
  const queryClient = useQueryClient();
  const {
    onSuccess,
    onError,
    successMessage = 'QR label generated successfully',
    errorMessage = 'Failed to generate QR label',
    showNotifications = true,
  } = options;

  return useMutation({
    ...generateForShipmentMutationWithInvalidation(queryClient),
    onSuccess: (...args) => {
      if (showNotifications) {
        notifications.show({
          title: 'Success',
          message: successMessage,
          color: 'green',
          icon: React.createElement(IconCheck, { size: '1rem' }),
        });
      }
      onSuccess?.();
    },
    onError: (error: unknown) => {
      if (showNotifications) {
        notifications.show({
          title: 'Error',
          message: errorMessage,
          color: 'red',
          icon: React.createElement(IconAlertCircle, { size: '1rem' }),
        });
      }
      onError?.(error);
    },
  });
};

/**
 * Hook to get cache invalidation manager directly
 */
export const useCacheInvalidation = () => {
  const queryClient = useQueryClient();
  return new CacheInvalidationManager(queryClient);
};
