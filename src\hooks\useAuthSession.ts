import { useSession } from 'next-auth/react';
import type { Session } from 'next-auth';

interface AuthSessionState {
  session: Session | null;
  status: 'loading' | 'authenticated' | 'unauthenticated';
  isLoading: boolean;
}

/**
 * Custom hook that provides better session handling with debugging
 */
export const useAuthSession = (): AuthSessionState => {
  const { data: session, status } = useSession();

  return {
    session,
    status,
    isLoading: status === 'loading',
  };
};

export default useAuthSession;
