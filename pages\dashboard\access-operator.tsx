import { useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import useTranslation from 'next-translate/useTranslation';
import {
  Container,
  Title,
  Text,
  Paper,
  Stack,
  Loader,
  Center,
  Grid,
  Card,
  Group,
  Button,
} from '@mantine/core';
import {
  IconBuilding,
  IconPackage,
  IconTruck,
  IconUsers,
  IconQrcode,
} from '@tabler/icons-react';
import Link from 'next/link';
import { useQuery } from '@tanstack/react-query';
import { getDashboardQuery } from '../../src/requests/dashboard/call';
import type {
  DashboardApiResponse,
  AccessOperatorDashboardData,
  RecentShipment,
} from '../../src/requests/dashboard/type';

// Helpers
const formatNumber = (n?: number) => (typeof n === 'number' ? n.toLocaleString() : '—');

export default function AccessOperatorDashboard() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { t } = useTranslation('dashboard');
  const { t: tCommon } = useTranslation('common');
  const {
    data: dashboard,
    isLoading: isDashboardLoading,
  } = useQuery<DashboardApiResponse>(getDashboardQuery({ enabled: status === 'authenticated' }));

  useEffect(() => {
    if (status === 'loading') return; // Still loading

    if (!session) {
      router.push('/auth/login');
      return;
    }

    // Check if user is an access operator
    if (session.user?.user_type !== 'ACCESS_OPERATOR') {
      router.push('/'); // Redirect to home if not an access operator
    }
  }, [session, status, router]);

  if (status === 'loading' || isDashboardLoading) {
    return (
      <Center style={{ height: '100vh' }}>
        <Loader />
      </Center>
    );
  }

  if (!session || session.user?.user_type !== 'ACCESS_OPERATOR') {
    return null; // Will redirect
  }

  // Extract data for easy access (fallbacks keep UI stable)
  const aoData = dashboard?.data?.dashboardData as AccessOperatorDashboardData | undefined;
  const shipmentStats = aoData?.shipmentStats ?? {};
  const recentShipments = aoData?.recentShipments ?? [];

  return (
    <Container size="xl" py="xl">
      <Stack gap="xl">
        {/* Header */}
        <div>
          <Title order={1} mb="sm">
            {t('accessOperatorDashboard')}
          </Title>
          <Text size="lg" c="dimmed">
            {t('welcomeBack')}
            ,
            {' '}
            {session.user?.name || session.user?.email}
            !
            {' '}
            {t('manageAccessPoints')}
          </Text>
        </div>

        {/* Quick Stats */}
        <Grid>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card shadow="sm" padding="lg" radius="md" withBorder>
              <Group justify="space-between" mb="xs">
                <Text fw={500}>{t('pendingShipments')}</Text>
                <IconBuilding size="1.4rem" color="blue" />
              </Group>
              <Text size="xl" fw={700} c="blue">
                {formatNumber(shipmentStats.pending)}
              </Text>
              <Text size="sm" c="dimmed">
                {t('currentlyAssigned')}
              </Text>
            </Card>
          </Grid.Col>

          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card shadow="sm" padding="lg" radius="md" withBorder>
              <Group justify="space-between" mb="xs">
                <Text fw={500}>{tCommon('pending')}</Text>
                <IconPackage size="1.4rem" color="green" />
              </Group>
              <Text size="xl" fw={700} c="green">
                {formatNumber(shipmentStats.awaitingPickup)}
              </Text>
              <Text size="sm" c="dimmed">
                {t('thisMonth')}
              </Text>
            </Card>
          </Grid.Col>

          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card shadow="sm" padding="lg" radius="md" withBorder>
              <Group justify="space-between" mb="xs">
                <Text fw={500}>{t('inTransitShipments')}</Text>
                <IconTruck size="1.4rem" color="orange" />
              </Group>
              <Text size="xl" fw={700} c="orange">
                {formatNumber(shipmentStats.inTransit)}
              </Text>
              <Text size="sm" c="dimmed">
                {t('currentlyAssigned')}
              </Text>
            </Card>
          </Grid.Col>

          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card shadow="sm" padding="lg" radius="md" withBorder>
              <Group justify="space-between" mb="xs">
                <Text fw={500}>{t('deliveredShipments')}</Text>
                <IconUsers size="1.4rem" color="gray" />
              </Group>
              <Text size="xl" fw={700}>
                {formatNumber(shipmentStats.delivered)}
              </Text>
              <Text size="sm" c="dimmed">
                {t('thisMonth')}
              </Text>
            </Card>
          </Grid.Col>
        </Grid>

        {/* Quick Actions */}
        <Paper shadow="sm" p="lg" radius="md" withBorder>
          <Title order={3} mb="md">
            {tCommon('actions')}
          </Title>
          <Group>
            <Button
              component={Link}
              href="/qr-generator"
              variant="outline"
              size="md"
              leftSection={<IconQrcode size="1rem" />}
            >
              {tCommon('qrCodes')}
            </Button>
          </Group>
        </Paper>

        {/* Recent Shipments */}
        <Paper shadow="sm" p="lg" radius="md" withBorder>
          <Title order={3} mb="md">
            {t('recentShipments')}
          </Title>
          <Stack gap="sm">
            {recentShipments.length === 0 && (
              <Text size="sm" c="dimmed">{t('noRecentShipments')}</Text>
            )}
            {recentShipments.slice(0, 5).map((s: RecentShipment) => (
              <Group key={s.id} justify="space-between">
                <Text size="sm">
                  {s.trackingCode}
                  {' '}
                  —
                  {' '}
                  {s.status}
                </Text>
                <Text size="xs" c="dimmed">
                  {new Date(s.createdAt).toLocaleDateString()}
                </Text>
              </Group>
            ))}
          </Stack>
        </Paper>
      </Stack>
    </Container>
  );
}
