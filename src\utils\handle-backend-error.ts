/* eslint-disable sonarjs/cognitive-complexity */
/* eslint-disable sonarjs/no-duplicate-string */
/* eslint-disable complexity */
// Removed automatic sign-out to prevent forcing the user back to the login page on every 401/403.
import { notifications } from '@mantine/notifications';
import { ErrorBackendType, ErrorFrontendType } from '../types';
import { HTTP_CODE, httpCodeType } from '../data';
import { getTranslation } from './translation';

interface ErrorItem {
  message: string;
  key: string;
}

export const handleBackendError = (
  e: ErrorBackendType,
  errors: ErrorItem[],
  throwError: boolean = false,
) => {
  const errorMessageKey = 'generalError';
  const errorMessage = e?.response?.data?.error?.message ?? '';
  const messageItem = errors.find(
    (item) => item.message.trim().toLowerCase() === errorMessage.trim().toLowerCase(),
  );
  if (messageItem && throwError) throw new Error(messageItem.key);
  else if (messageItem) return messageItem.key;
  else return errorMessageKey;
};

export const createApiError = (
  code: httpCodeType,
  message: string,
  key: string,
  trace?: string,
  errors?: ErrorItem[],
) => ({
  code,
  message,
  key,
  trace: trace ?? '',
  errors: errors ?? [],
});
const errorCache: { [key: string]: number } = {};
export const handleApiError = async (error: ErrorFrontendType, showNotification: boolean = true) => {
  // this is not a pure function and it is ok since it will be used only in the context of this app
  let errorMessageKey = 'generalError';
  let defaultMessage = '';

  if (
    error.response?.data?.code === HTTP_CODE.UNAUTHORIZED
    || error.response?.status === HTTP_CODE.UNAUTHORIZED
    || error.response?.data?.code === HTTP_CODE.FORBIDDEN
    || error.response?.status === HTTP_CODE.FORBIDDEN
  ) {
    // if authorized then logout
    // else show please login first.
    errorMessageKey = 'authorizationError';
  } else if (error.response) {
    const { data, status } = error.response;

    // Handle 500 errors specifically
    if (status === 500) {
      errorMessageKey = 'internalServerError';
    } else if (typeof data.message === 'string') {
      // Handle both new string format and legacy object format
      defaultMessage = data.message;
      errorMessageKey = 'generalError';
    } else if (data.message && typeof data.message === 'object') {
      const key = data.message?.key ?? '';
      defaultMessage = data.message?.fallback ?? '';
      errorMessageKey = key || 'generalError';
    } else {
      errorMessageKey = 'generalError';
    }
  }

  // Get translated error message
  let errorMessage: string;
  try {
    const t = await getTranslation('error');
    const translatedMessage = t(errorMessageKey);

    // If translation exists and is different from the key, use it
    if (translatedMessage && translatedMessage !== errorMessageKey) {
      errorMessage = translatedMessage;
    } else if (defaultMessage) {
      // Use backend message if available
      errorMessage = defaultMessage;
    } else {
      // Fallback to general error translation
      const fallbackTranslation = t('generalError');
      errorMessage = fallbackTranslation !== 'generalError' ? fallbackTranslation : 'An error occurred';
    }
  } catch (translationError) {
    // If translation fails, use default message or fallback
    errorMessage = defaultMessage || 'An error occurred';
  }

  const canShowNotification = Date.now() - (errorCache[errorMessageKey ?? ''] ?? 0) > 30 * 1000;
  if (typeof window !== 'undefined' && canShowNotification && showNotification) {
    errorCache[errorMessageKey || ''] = Date.now();
    notifications.show({
      message: errorMessage,
      color: 'red',
    });
  }

  // return errorMessageKey;
};
