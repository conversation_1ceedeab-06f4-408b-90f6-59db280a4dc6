/* eslint-disable no-nested-ternary */
import {
  Stack,
  Group,
  Text,
  Button,
  Paper,
  Box,
  Textarea,
  Alert,
  Image,
} from '@mantine/core';
import {
  IconCamera,
  IconCheck,
} from '@tabler/icons-react';
import useTranslation from 'next-translate/useTranslation';

interface PhotoNotesStepProps {
  photoPreview: string | null;
  photoUrl: string | null;
  notes: string;
  setNotes: (value: string) => void;
  onFileChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  uploadPending: boolean;
  error: string | null;
  setError: (error: string | null) => void;
  onBack: () => void;
  onComplete: () => void;
  loading: boolean;
  isCarOperatorPickup: boolean;
}

export default function PhotoNotesStep({
  photoPreview,
  photoUrl,
  notes,
  setNotes,
  onFileChange,
  uploadPending,
  error,
  setError,
  onBack,
  onComplete,
  loading,
  isCarOperatorPickup,
}: PhotoNotesStepProps) {
  const { t } = useTranslation('shipments');

  const clearAlerts = () => {
    setError(null);
  };

  const validatePhoto = () => {
    if (!photoUrl) {
      setError(t('pleaseTakePhoto'));
      return false;
    }
    return true;
  };

  return (
    <Paper p="md" withBorder>
      <Stack gap="md">
        <Text size="sm" c="dimmed">
          {isCarOperatorPickup
            ? t('takePhotoPickupDocumentation')
            : t('takePhotoProofDelivery')}
        </Text>

        <input
          type="file"
          accept="image/*"
          capture="environment"
          onChange={onFileChange}
          style={{ display: 'none' }}
          id="pickup-photo-input"
        />
        <Button
          component="label"
          htmlFor="pickup-photo-input"
          leftSection={<IconCamera size="1rem" />}
          variant="light"
          fullWidth
          loading={uploadPending}
          disabled={uploadPending}
        >
          {uploadPending
            ? t('uploadingPhoto')
            : isCarOperatorPickup
              ? t('takePickupPhoto')
              : t('takeDeliveryPhoto')}
        </Button>

        {photoPreview && (
          <Box>
            <Text size="sm" c="dimmed" mb="xs">
              {isCarOperatorPickup ? t('pickupPhoto') : t('deliveryPhoto')}
              :
            </Text>
            <Image
              src={photoPreview}
              alt={isCarOperatorPickup ? t('pickupPhoto') : t('deliveryPhoto')}
              height={200}
              fit="contain"
              radius="md"
            />
            {photoUrl && !error && (
              <Alert
                icon={<IconCheck size="1rem" />}
                color="green"
                variant="light"
                mt="md"
              >
                {t('photoUploadedSuccessfully')}
              </Alert>
            )}
          </Box>
        )}

        <Textarea
          label={t('notesOptional')}
          placeholder={isCarOperatorPickup
            ? t('addNotesAboutPickup')
            : t('addNotesAboutDelivery')}
          value={notes}
          onChange={(event) => {
            clearAlerts();
            setNotes(event.currentTarget.value);
          }}
          rows={3}
        />

        <Group justify="space-between" mt="md">
          <Button variant="light" onClick={onBack}>
            {t('back')}
          </Button>
          <Button
            onClick={() => {
              if (validatePhoto()) {
                onComplete();
              }
            }}
            loading={loading}
            leftSection={<IconCheck size="1rem" />}
            color="green"
            disabled={!photoUrl}
          >
            {isCarOperatorPickup ? t('completePickup') : t('completeDelivery')}
          </Button>
        </Group>
      </Stack>
    </Paper>
  );
}
