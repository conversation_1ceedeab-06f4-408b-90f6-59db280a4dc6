import { z } from 'zod';
import { USER_TYPES } from './request-transform';

const USER_STATUS = ['PENDING', 'ACTIVE', 'SUSPENDED'] as const;

// Backend user schema
const userBackendSchema = z.object({
  id: z.string().uuid(),
  name: z.string(),
  email: z.string().email(),
  phone: z.string().optional(),
  user_type: z.enum(USER_TYPES),
  status: z.enum(USER_STATUS),
  email_verified: z.boolean().optional(),
  created_at: z.string().optional(),
  updated_at: z.string().optional(),
  // For ACCESS_OPERATOR
  business_name: z.string().optional(),
  address: z.string().optional(),
  geo_latitude: z.number().optional(),
  geo_longitude: z.number().optional(),
  approved: z.boolean().optional(),
  // For CAR_OPERATOR
  license_number: z.string().optional(),
  vehicle_info: z.string().optional(),
  pickup_access_point_id: z.string().nullable().optional(),
  dropoff_access_point_id: z.string().nullable().optional(),
});

// Transformed user schema
export const userSchema = userBackendSchema.transform((item) => ({
  id: item.id,
  name: item.name,
  email: item.email,
  phone: item.phone,
  userType: item.user_type,
  status: item.status,
  emailVerified: item.email_verified,
  createdAt: item.created_at,
  updatedAt: item.updated_at,
  // For ACCESS_OPERATOR
  businessName: item.business_name,
  address: item.address,
  geoLatitude: item.geo_latitude,
  geoLongitude: item.geo_longitude,
  approved: item.approved,
  // For CAR_OPERATOR
  licenseNumber: item.license_number,
  vehicleInfo: item.vehicle_info,
  pickupAccessPointId: item.pickup_access_point_id,
  dropoffAccessPointId: item.dropoff_access_point_id,
}));

export const tokenSchema = z.string();

// Auth response data schema
export const authResponseDataSchema = z.object({
  user: userBackendSchema,
  token: tokenSchema,
});

// Login API response schema
export const loginApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: authResponseDataSchema.transform((item) => ({
    user: userSchema.parse(item.user),
    token: item.token,
  })),
});

// Register API response schema
export const registerApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({}).optional(),
});

// Forgot password API response schema
export const forgotPasswordApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({}).optional(),
});

// Verify reset token API response schema
export const verifyResetTokenApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({}).optional(),
});

// Reset password API response schema
export const resetPasswordApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({}).optional(),
});

// Change password API response schema
export const changePasswordApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({}).optional(),
});

// Send verification OTP API response schema
export const sendVerificationOtpApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    email: z.string(),
    message: z.string(),
  }),
});

// Verify email OTP API response schema
export const verifyEmailOtpApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    user: userBackendSchema.transform((item) => userSchema.parse(item)),
  }),
});

// Resend verification OTP API response schema
export const resendVerificationOtpApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    email: z.string(),
    message: z.string(),
  }),
});

export const verifySchema = z.object({
  otp: z.string().length(6, { message: 'OTP must be 6 digits' }),
});

export const emailSchema = z.object({
  email: z.string().email({ message: 'Invalid email address' }),
});

export const authResponseSchemas = {
  user: userSchema,
  token: tokenSchema,
  authData: authResponseDataSchema,
  loginApiResponse: loginApiResponseSchema,
  registerApiResponse: registerApiResponseSchema,
  forgotPasswordApiResponse: forgotPasswordApiResponseSchema,
  verifyResetTokenApiResponse: verifyResetTokenApiResponseSchema,
  resetPasswordApiResponse: resetPasswordApiResponseSchema,
  changePasswordApiResponse: changePasswordApiResponseSchema,
  sendVerificationOtpApiResponse: sendVerificationOtpApiResponseSchema,
  verifyEmailOtpApiResponse: verifyEmailOtpApiResponseSchema,
  resendVerificationOtpApiResponse: resendVerificationOtpApiResponseSchema,
  verifySchema,
  emailSchema,
};

export default authResponseSchemas;
