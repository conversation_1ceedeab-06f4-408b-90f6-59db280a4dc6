module.exports = {
  locales: ['ar', 'en'],
  defaultLocale: 'ar',
  pages: {
    '*': ['common', 'error', 'notifications'],
    '/auth/login': ['common', 'error', 'auth'],
    '/auth/register': ['common', 'error', 'auth'],
    '/auth/forgot-password': ['common', 'error', 'auth'],
    '/auth/reset-password': ['common', 'error', 'auth'],
    '/auth/verify-email': ['common', 'error', 'auth'],
    '/dashboard/customer': ['common', 'error', 'dashboard', 'notifications'],
    '/dashboard/access-operator': ['common', 'error', 'dashboard', 'notifications'],
    '/dashboard/car-operator': ['common', 'error', 'dashboard', 'notifications'],
    '/customer/shipments': ['common', 'error', 'shipments', 'notifications'],
    '/customer/shipments/create': ['common', 'error', 'shipments', 'notifications'],
    '/access-operator/shipments': ['common', 'error', 'shipments', 'notifications'],
    '/access-operator/shipments/my': ['common', 'error', 'shipments', 'notifications'],
    '/access-operator/shipments/pending': ['common', 'error', 'shipments', 'notifications'],
    '/access-operator/shipments/incoming': ['common', 'error', 'shipments', 'notifications'],
    '/car-operator/shipments': ['common', 'error', 'shipments', 'notifications'],
    '/car-operator/shipments/my': ['common', 'error', 'shipments', 'notifications'],
    '/car-operator/shipments/in-transit': ['common', 'error', 'shipments', 'notifications'],
    '/profile': ['common', 'error', 'profile', 'notifications'],
    '/notifications': ['common', 'error', 'notifications'],
    '/qr-generator': ['common', 'error', 'qr'],
    '/map/access-points': ['common', 'error', 'map'],
    '/map/transit-points': ['common', 'error', 'map'],
  },
  loadLocaleFrom: (lang, ns) =>
    import(`./locales/${lang}/${ns}.json`).then((m) => m.default),
  interpolation: {
    prefix: '{{',
    suffix: '}}',
  },
}
