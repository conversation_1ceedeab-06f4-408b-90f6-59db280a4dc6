/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react/jsx-no-useless-fragment */
/* eslint-disable no-nested-ternary */
/* eslint-disable complexity */
/* eslint-disable max-lines */
/* eslint-disable react/require-default-props */
import React, {
  useState, useRef, useMemo, useEffect, useCallback,
} from 'react';
import { useRouter } from 'next/router';
import { useDebouncedCallback } from 'use-debounce';
import { useMediaQuery } from '@mantine/hooks';
import {
  Stack,
  Group,
  TextInput,
  Select,
  Button,
  Text,
  Loader,
  Center,
  Alert,
  Paper,
  Badge,
  Box,
  Grid,
  Pagination,
} from '@mantine/core';
import {
  IconSearch,
  IconFilter,
  IconAlertCircle,
  IconPackage,
  IconEye,
  IconRefresh,
  IconQrcode,
} from '@tabler/icons-react';
import { useQuery } from '@tanstack/react-query';
import { useSession } from 'next-auth/react';
import useTranslation from 'next-translate/useTranslation';
import { useIsClient } from '../../../hooks/useIsClient';
import { getShipmentsQuery, SHIPMENT_STATUS } from '../../../requests';
import { Shipment, shipmentSortKeysMapping } from '../../../requests/shipment';
import { getExpiryInfo, shouldHighlightExpiry } from '../../../utils/shipmentExpiry';
import { DataTableColumn, DataTable } from '../../common/DataTable';
import { StatusBadge } from '../../common/StatusBadge';
import { ShipmentCard } from '../cards';

interface AOShipmentsTableProps {
  onViewShipment?: (shipment: Shipment) => void;
  onScanArrival?: (shipment: Shipment) => void;
  onPickup?: (shipment: Shipment) => void;
}

const ITEMS_PER_PAGE = 10;

// eslint-disable-next-line sonarjs/cognitive-complexity
export default function AOShipmentsTable({
  onViewShipment,
  onScanArrival,
  onPickup,
}: AOShipmentsTableProps) {
  const router = useRouter();
  const { t } = useTranslation('shipments');

  // Add client-side rendering guard to prevent hydration issues
  const isClient = useIsClient();
  const isDesktop = useMediaQuery('(min-width: 992px)');
  const isRTL = router.locale === 'ar';

  // Helper function to get translated size label
  const getSizeLabel = (size: string) => {
    switch (size) {
      case 'SMALL':
        return t('sizeSmall');
      case 'MEDIUM':
        return t('sizeMedium');
      case 'LARGE':
        return t('sizeLarge');
      case 'EXTRA_LARGE':
        return t('sizeExtraLarge');
      default:
        return size;
    }
  };

  // Helper function to get translated status label
  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'PENDING':
        return t('statusPending');
      case 'READY_FOR_PICKUP':
        return t('statusReadyForPickup');
      case 'IN_TRANSIT':
        return t('statusInTransit');
      case 'ARRIVED_AT_DESTINATION':
        return t('statusArrivedAtDestination');
      case 'DELIVERED':
        return t('statusDelivered');
      case 'CANCELLED':
        return t('statusCancelled');
      case 'EXPIRED':
        return t('statusExpired');
      default:
        return status.replace('_', ' ').toLowerCase().replace(/\b\w/g, (l) => l.toUpperCase());
    }
  };

  // Safe date formatting function
  const formatDate = (dateString: string) => {
    if (!isClient) return 'Loading...';
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return 'Invalid date';
    }
  };

  // State for pagination and filters
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(ITEMS_PER_PAGE);
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [searchInput, setSearchInput] = useState('');
  const [searchQuery, setSearchQuery] = useState(''); // Separate state for the actual search query

  // Track initialization to prevent loops
  const isInitializedRef = useRef(false);
  const skipUrlUpdateRef = useRef(false);

  // Ref to maintain search input focus
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Sort state - default to newest first (createdAt desc)
  const [sortStatus, setSortStatus] = useState({
    columnAccessor: 'createdAt',
    direction: 'desc' as 'asc' | 'desc',
  });

  // Get filters from URL query parameters
  const statusFilterFromUrl = `${router.query.status || ''}`;
  const searchFromUrl = `${router.query.query || ''}`;

  // Debounced search handler - only updates search query and URL
  const debouncedSearch = useDebouncedCallback((term: string) => {
    setPage(1);
    setSearchQuery(term);

    // Don't push a new URL if the query string is already the same
    const current = (router.query.query ?? '') as string;
    if (current === term.trim()) {
      return;
    }

    skipUrlUpdateRef.current = true;
    router.push(
      {
        pathname: router.pathname,
        query: {
          ...router.query,
          query: term.trim() || undefined,
          page: undefined, // Reset page when searching
        },
      },
      undefined,
      { shallow: true },
    );
  }, 300);

  // Immediate input handler - only updates UI input value
  const handleInputChange = useCallback((value: string) => {
    setSearchInput(value); // Update input display immediately
    debouncedSearch(value); // Debounce the actual search
  }, [debouncedSearch]);

  // Initialize state from URL on mount only
  useEffect(() => {
    if (isInitializedRef.current) return;
    isInitializedRef.current = true;

    // Initialize from URL
    const urlSearchValue = searchFromUrl !== 'undefined' && searchFromUrl !== '' ? searchFromUrl : '';
    const urlStatusValue = statusFilterFromUrl !== 'undefined' && statusFilterFromUrl !== '' ? statusFilterFromUrl : null;

    setSearchInput(urlSearchValue);
    setSearchQuery(urlSearchValue);
    setStatusFilter(urlStatusValue);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Empty dependency array - only run once on mount

  // Sync URL changes to state (but skip if we just updated the URL ourselves)
  useEffect(() => {
    // Skip if we just updated the URL ourselves
    if (skipUrlUpdateRef.current) {
      skipUrlUpdateRef.current = false;
      return;
    }

    // Only update if URL params actually changed from external source (e.g., browser back/forward)
    const urlSearchValue = searchFromUrl !== 'undefined' && searchFromUrl !== '' ? searchFromUrl : '';
    const urlStatusValue = statusFilterFromUrl !== 'undefined' && statusFilterFromUrl !== '' ? statusFilterFromUrl : null;

    // Update status if different
    if (urlStatusValue !== statusFilter) {
      setStatusFilter(urlStatusValue);
    }

    // Update search if different (from external navigation)
    if (urlSearchValue !== searchQuery) {
      setSearchQuery(urlSearchValue);
      setSearchInput(urlSearchValue);
    }
  }, [statusFilterFromUrl, searchFromUrl, searchQuery, statusFilter]);

  // Clear filters function
  const clearFilters = () => {
    // Clear all states immediately for smooth UI
    setPage(1);
    setStatusFilter(null);
    setSearchInput('');
    setSearchQuery('');

    // Clear URL params efficiently
    const newQuery = { ...router.query };
    delete newQuery.status;
    delete newQuery.query;
    delete newQuery.page;

    skipUrlUpdateRef.current = true;
    router.push(
      {
        pathname: router.pathname,
        query: newQuery,
      },
      undefined,
      { shallow: true },
    );
  };

  const { data: session } = useSession();
  const aoId = session?.user?.id;

  // Build filters object for backend - memoized to prevent unnecessary re-renders
  const filters = useMemo(() => ({
    search: searchQuery.trim(),
    status: statusFilter || undefined,
  }), [searchQuery, statusFilter]);

  const handleSortChange = (sort: { accessor: string; direction: 'asc' | 'desc' }) => {
    setSortStatus({
      columnAccessor: sort.accessor,
      direction: sort.direction,
    });
  };

  // Build sort object for backend - memoized to prevent unnecessary re-renders
  const sortObj = useMemo(() => ({
    sortBy: shipmentSortKeysMapping.has(sortStatus.columnAccessor)
      ? shipmentSortKeysMapping.get(sortStatus.columnAccessor)
      : sortStatus.columnAccessor,
    sortOrder: sortStatus.direction as 'asc' | 'desc',
  }), [sortStatus.columnAccessor, sortStatus.direction]);

  // Fetch shipments where AO is origin
  const {
    data: originShipmentsData,
    isLoading: isLoadingOrigin,
    error: errorOrigin,
    refetch: refetchOrigin,
  } = useQuery({
    ...getShipmentsQuery({
      pagination: {
        page: page - 1,
        pageSize,
      },
      filters: { ...filters, originAoId: aoId },
      sort: `${sortObj.sortBy}:${sortObj.sortOrder}`,
    }),
    refetchOnWindowFocus: false,
    staleTime: 0,
    gcTime: 0,
    refetchOnMount: true,
    retry: 1,
    retryDelay: 1000,
    enabled: !!aoId,
  });

  // Fetch shipments where AO is destination
  const {
    data: destShipmentsData,
    isLoading: isLoadingDest,
    error: errorDest,
    refetch: refetchDest,
  } = useQuery({
    ...getShipmentsQuery({
      pagination: {
        page: page - 1,
        pageSize,
      },
      filters: { ...filters, destAoId: aoId },
      sort: `${sortObj.sortBy}:${sortObj.sortOrder}`,
    }),
    refetchOnWindowFocus: false,
    staleTime: 0,
    gcTime: 0,
    refetchOnMount: true,
    retry: 1,
    retryDelay: 1000,
    enabled: !!aoId,
  });

  // Merge and deduplicate shipments
  const shipments = useMemo(() => {
    const origin = originShipmentsData?.data?.shipments || [];
    const dest = destShipmentsData?.data?.shipments || [];
    const all = [...origin, ...dest];
    return all.reduce((acc: Shipment[], curr) => {
      if (!acc.find((s: Shipment) => s.id === curr.id)) acc.push(curr);
      return acc;
    }, [] as Shipment[]);
  }, [originShipmentsData, destShipmentsData]);

  const isLoading = isLoadingOrigin || isLoadingDest;
  const error = errorOrigin || errorDest;
  const refetch = () => {
    refetchOrigin();
    refetchDest();
  };
  const totalCount = shipments.length;
  const totalPages = Math.ceil(totalCount / pageSize);

  const handlePageSizeChange = (newPageSize: string | null) => {
    if (newPageSize) {
      setPageSize(parseInt(newPageSize, 10));
      setPage(1); // Reset to first page when changing page size
    }
  };

  const handleStatusChange = (value: string | null) => {
    setPage(1); // Reset to first page when filtering
    setStatusFilter(value);

    // Update URL efficiently
    const newQuery = { ...router.query };
    if (value) {
      newQuery.status = value;
    } else {
      delete newQuery.status;
    }

    router.replace({
      pathname: router.pathname,
      query: newQuery,
    }, undefined, { shallow: true });
  };

  const columns: DataTableColumn<Shipment>[] = [
    {
      label: t('shipmentId'),
      accessor: 'id',
      render: (shipment) => (
        <Text size="sm" fw={500}>
          {`#${shipment.id.slice(-8).toUpperCase()}`}
        </Text>
      ),
      sortable: true,
    },
    {
      label: t('status'),
      accessor: 'status',
      render: (shipment) => <StatusBadge status={shipment.status} />,
      sortable: true,
    },
    {
      label: t('weightAndSize'),
      accessor: 'weight',
      render: (shipment) => (
        <Text size="sm">
          {shipment.weight}
          {t('kg')}
          {' • '}
          {getSizeLabel(shipment.size)}
        </Text>
      ),
    },
    {
      label: 'Expires',
      accessor: 'expiresAt',
      render: (shipment) => {
        const expiryInfo = getExpiryInfo(shipment.expiresAt);
        return (
          <div>
            <Badge
              size="sm"
              color={expiryInfo.color}
              variant={expiryInfo.isExpired ? 'filled' : 'light'}
            >
              {expiryInfo.timeRemaining || 'N/A'}
            </Badge>
            {shipment.expiresAt && shouldHighlightExpiry(shipment.expiresAt) && (
              <Text size="xs" c="dimmed" mt={2}>
                {expiryInfo.isExpired ? 'Expired' : 'Urgent'}
              </Text>
            )}
          </div>
        );
      },
    },
    {
      label: 'Last Updated',
      accessor: 'updatedAt',
      render: (shipment) => (
        <Text size="sm">
          {formatDate(shipment.updatedAt)}
        </Text>
      ),
      sortable: true,
    },
  ];

  const rowActions = (shipment: Shipment) => (
    <Group gap="xs" justify="center">
      <Button
        variant="light"
        size="xs"
        leftSection={<IconEye size="1rem" />}
        onClick={() => onViewShipment?.(shipment)}
      >
        View
      </Button>
      {shipment.status === 'IN_TRANSIT' && shipment.destAoId === aoId && onScanArrival && (
        <Button
          variant="light"
          color="green"
          size="xs"
          leftSection={<IconQrcode size="1rem" />}
          onClick={() => onScanArrival(shipment)}
        >
          Scan Arrival
        </Button>
      )}
      {shipment.status === 'ARRIVED_AT_DESTINATION' && shipment.destAoId === aoId && onPickup && (
        <Button
          variant="light"
          color="blue"
          size="xs"
          leftSection={<IconPackage size="1rem" />}
          onClick={() => onPickup(shipment)}
        >
          Process Pickup
        </Button>
      )}
    </Group>
  );

  // Show loading skeleton during hydration
  if (!isClient) {
    return (
      <Stack gap="lg">
        <Paper p="md" withBorder>
          <Box h={60} bg="gray.1" style={{ borderRadius: 'var(--mantine-radius-sm)' }} />
        </Paper>
        <Paper withBorder>
          <Box h={400} bg="gray.1" style={{ borderRadius: 'var(--mantine-radius-sm)' }} />
        </Paper>
      </Stack>
    );
  }

  return (
    <Stack gap="lg">
      {/* Header with stats */}
      <Paper p="md" withBorder>
        <Group justify="space-between" mb="md">
          {isRTL && (
            <Button
              leftSection={<IconRefresh size="1rem" />}
              variant="light"
              onClick={() => refetch()}
              loading={isLoading}
            >
              {t('refresh')}
            </Button>
          )}
          <Group gap="md">
            <IconPackage size="1.5rem" color="blue" />
            <div>
              <Text size="lg" fw={600}>{t('myAssignedShipments')}</Text>
              <Text size="sm" c="dimmed">{t('viewAndManageAssigned')}</Text>
            </div>
          </Group>
          {!isRTL && (
            <Button
              leftSection={<IconRefresh size="1rem" />}
              variant="light"
              onClick={() => refetch()}
              loading={isLoading}
            >
              {t('refresh')}
            </Button>
          )}
        </Group>

        {/* Stats */}
        <Group gap="lg">
          <div>
            <Text size="xl" fw={700} c="blue">{totalCount}</Text>
            <Text size="sm" c="dimmed">{t('totalAssigned')}</Text>
          </div>
        </Group>
      </Paper>

      {/* Filters */}
      <Paper p="md" withBorder>
        <Group gap="md" align="flex-end">
          <TextInput
            ref={searchInputRef}
            placeholder={t('searchShipmentsPlaceholder')}
            leftSection={<IconSearch size="1rem" />}
            value={searchInput}
            onChange={(e) => handleInputChange(e.currentTarget.value)}
            style={{ flex: 1 }}
          />
          <Select
            placeholder={t('filterByStatus')}
            leftSection={<IconFilter size="1rem" />}
            data={[
              { value: '', label: t('allStatuses') },
              ...SHIPMENT_STATUS.map((status) => ({
                value: status,
                label: getStatusLabel(status),
              })),
            ]}
            value={statusFilter}
            onChange={handleStatusChange}
            clearable
            style={{ minWidth: 200 }}
          />
          <Select
            placeholder={t('itemsPerPage')}
            data={[
              { value: '10', label: t('perPage10') },
              { value: '25', label: t('perPage25') },
              { value: '50', label: t('perPage50') },
            ]}
            value={pageSize.toString()}
            onChange={handlePageSizeChange}
            style={{ minWidth: 150 }}
          />
          {(searchQuery || statusFilter) && (
            <Button variant="light" onClick={clearFilters}>
              {t('clearFilters')}
            </Button>
          )}
        </Group>
      </Paper>

      {/* Loading State */}
      {isLoading && (
        <Center py="xl">
          <Stack align="center" gap="md">
            <Loader size="lg" />
            <Text c="dimmed">{t('loadingAssignedShipments')}</Text>
          </Stack>
        </Center>
      )}

      {/* Error State */}
      {error && (
        <Alert
          icon={<IconAlertCircle size="1rem" />}
          title={t('errorLoadingShipments')}
          color="red"
          variant="light"
        >
          {error instanceof Error ? error.message : t('failedToLoadShipments')}
          <Button variant="light" size="sm" mt="sm" onClick={() => refetch()}>
            {t('tryAgain')}
          </Button>
        </Alert>
      )}

      {/* Shipments Table */}
      {!isLoading && !error && (
        <>
          {shipments.length === 0 ? (
            <Center py="xl">
              <Stack align="center" gap="md">
                <IconPackage size="3rem" color="gray" />
                <div style={{ textAlign: 'center' }}>
                  <Text size="lg" fw={500} c="dimmed">
                    {totalCount === 0 ? t('noAssignedShipmentsYet') : t('noShipmentsMatchFilters')}
                  </Text>
                </div>
              </Stack>
            </Center>
          ) : (
            <>
              {isDesktop ? (
                <DataTable<Shipment>
                  columns={columns}
                  data={shipments}
                  loading={isLoading}
                  error={
                    typeof error === 'string'
                      ? error
                      : error && typeof (error as any).message === 'string'
                        ? (error as any).message
                        : undefined
                  }
                  emptyMessage={totalCount === 0 ? t('noAssignedShipmentsYet') : t('noShipmentsMatchFilters')}
                  pagination={{ page, totalPages, onPageChange: setPage }}
                  rowActions={rowActions}
                  sortState={{ accessor: sortStatus.columnAccessor, direction: sortStatus.direction }}
                  onSortChange={handleSortChange}
                />
              ) : (
                <>
                  <Grid>
                    {shipments.map((shipment: Shipment) => (
                      <Grid.Col key={shipment.id} span={12}>
                        <ShipmentCard
                          shipment={shipment}
                          hideDefaultActions
                          extraActions={rowActions(shipment)}
                        />
                      </Grid.Col>
                    ))}
                  </Grid>
                  {totalPages > 1 && (
                    <Center>
                      <Pagination value={page} total={totalPages} onChange={setPage} size="sm" />
                    </Center>
                  )}
                </>
              )}

              {/* Results Summary */}
              <Text c="dimmed" size="sm" ta="center">
                {t('showingResults', { count: shipments.length, total: totalCount })}
                {(searchInput || statusFilter) && ` ${t('withCurrentFilters')}`}
              </Text>
            </>
          )}
        </>
      )}
    </Stack>
  );
}
