import { NextApiRequest, NextApiResponse } from 'next';
import { getJwt } from '../../../src/utils';
import { BACKEND_API } from '../../../src/lib/axios';
import { API_ENDPOINT, HTTP_CODE } from '../../../src/data';
import createApiError from '../../../src/utils/create-api-error';
import { myShipmentsApiResponseSchema } from '../../../src/requests/my/response-transformer';

async function handleGet(req: NextApiRequest, res: NextApiResponse) {
  try {
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');

    const { token } = await getJwt(req);
    if (!token) {
      return res.status(HTTP_CODE.UNAUTHORIZED).json({
        success: false,
        message: 'Authentication required',
      });
    }
    // Forward query parameters directly to backend - backend handles parameter processing
    const response = await BACKEND_API.get(API_ENDPOINT.shipments.my, {
      headers: {
        Authorization: token,
      },
      params: req.query,
    });

    const validatedResponse = myShipmentsApiResponseSchema.parse(response.data);
    return res.status(HTTP_CODE.SUCCESS).json(validatedResponse);
  } catch (error: unknown) {
    const apiError = createApiError({ error });
    return res.status(apiError.code).json(apiError);
  }
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'GET') {
    return handleGet(req, res);
  }

  res.setHeader('Allow', ['GET']);
  return res.status(HTTP_CODE.METHOD_NOT_ALLOWED).json({
    success: false,
    message: `Method ${req.method} not allowed`,
  });
}
