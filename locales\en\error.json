{"wrong": "Something went wrong", "generalError": "An error occurred", "networkError": "Network connection error", "serverError": "Server error occurred", "validationError": "Validation error", "authenticationError": "Authentication failed", "authorizationError": "Access denied", "notFoundError": "Resource not found", "timeoutError": "Request timeout", "rateLimitError": "Too many requests", "maintenanceError": "System under maintenance", "invalidCredentials": "Invalid email or password", "accountLocked": "Account is locked", "accountDisabled": "Account is disabled", "accountNotVerified": "Account not verified", "passwordTooWeak": "Password is too weak", "passwordMismatch": "Passwords do not match", "emailAlreadyExists": "Email already exists", "emailNotFound": "<PERSON><PERSON> not found", "invalidEmail": "Invalid email format", "invalidPhone": "Invalid phone number", "requiredField": "This field is required", "invalidFormat": "Invalid format", "fileTooLarge": "File size too large", "fileTypeNotSupported": "File type not supported", "uploadFailed": "Upload failed", "downloadFailed": "Download failed", "connectionLost": "Connection lost", "sessionExpired": "Session expired", "tokenExpired": "Token expired", "invalidToken": "Invalid token", "permissionDenied": "Permission denied", "resourceNotFound": "Resource not found", "duplicateEntry": "Duplicate entry", "conflictError": "Conflict error", "badRequest": "Bad request", "internalServerError": "Internal server error occurred. Please try again later.", "serviceUnavailable": "Service unavailable", "gatewayTimeout": "Gateway timeout", "tooManyRequests": "Too many requests", "paymentRequired": "Payment required", "methodNotAllowed": "Method not allowed", "notAcceptable": "Not acceptable", "proxyAuthRequired": "Proxy authentication required", "requestTimeout": "Request timeout", "lengthRequired": "Length required", "preconditionFailed": "Precondition failed", "payloadTooLarge": "Payload too large", "uriTooLong": "URI too long", "unsupportedMediaType": "Unsupported media type", "rangeNotSatisfiable": "Range not satisfiable", "expectationFailed": "Expectation failed", "unprocessableEntity": "Unprocessable entity", "locked": "Resource locked", "failedDependency": "Failed dependency", "upgradeRequired": "Upgrade required", "preconditionRequired": "Precondition required", "tooManyRequestsRetry": "Too many requests, retry later", "requestHeaderFieldsTooLarge": "Request header fields too large", "unavailableForLegalReasons": "Unavailable for legal reasons", "notImplemented": "Not implemented", "badGateway": "Bad gateway", "httpVersionNotSupported": "HTTP version not supported", "variantAlsoNegotiates": "<PERSON><PERSON><PERSON> also negotiates", "insufficientStorage": "Insufficient storage", "loopDetected": "Loop detected", "notExtended": "Not extended", "networkAuthRequired": "Network authentication required", "requestFailed": "Request failed. Please try again.", "connectionError": "Connection error. Please check your internet connection.", "unknownError": "An unknown error occurred. Please try again.", "loginFailed": "<PERSON><PERSON> failed. Please check your credentials and try again.", "credentialsSignin": "Invalid email or password. Please try again.", "noCarOperatorAvailable": "No Car Operator available for route. Please select different Access Operators or contact support.", "routeNotAvailable": "Route not available between selected access points. Please choose different locations.", "shipmentCreationFailed": "Failed to create shipment. Please try again."}