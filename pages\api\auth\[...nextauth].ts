/* eslint-disable @typescript-eslint/no-explicit-any */
import NextAuth, {
  DefaultSession, NextAuthOptions, User,
} from 'next-auth';
import { JWT } from 'next-auth/jwt';
import CredentialsProvider from 'next-auth/providers/credentials';
import { BACKEND_API } from '../../../src/lib/axios';
import { TOKEN_SECRET } from '../../../src/data';

interface TokenPayload {
  exp: number;
  user_type?: string;
}

interface ExtendedUser extends User {
  accessToken: string;
  accessTokenExpires: number;
  user_type?: string;
  status?: string;
  phone?: string;
}

declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      name: string;
      email: string;
      user_type?: string;
      status?: string;
      phone?: string;
    } & DefaultSession['user'];
    accessToken?: string;
    error?: string;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    accessToken?: string | null;
    accessTokenExpires?: number;
    user?: {
      id: string;
      name: string;
      email: string;
      user_type?: string;
      status?: string;
      phone?: string;
    };
    error?: string;
  }
}

// Revoke the access token on signOut
async function revokeAccessToken(token: any) {
  try {
    if (token.accessToken) {
      await BACKEND_API.post('/auth/logout', {
        token: token.accessToken,
      });
    }
  } catch (err) {
    // eslint-disable-next-line no-console
    console.error('Logout error:', err);
  }
}

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      id: 'shipment-platform',
      name: 'Shipment Platform',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        try {
          // 1) Exchange credentials for an access token and user info
          const resp = await BACKEND_API.post('/auth/login', {
            email: credentials?.email,
            password: credentials?.password,
          });

          if (!resp.data.success || !resp.data.data) return null;

          const { user, token } = resp.data.data;
          // Decode exp & user_type from JWT
          let payload: TokenPayload = {} as any;
          try {
            const base64 = token.split('.')[1];
            payload = JSON.parse(Buffer.from(base64, 'base64').toString());
          } catch { /* empty */ }

          return {
            id: user.id,
            name: user.name,
            email: user.email,
            user_type: payload.user_type || user.user_type,
            status: user.status,
            phone: user.phone,
            accessToken: token,
            accessTokenExpires: payload.exp * 1000,
          } as ExtendedUser;
        } catch (error: any) {
          // Handle different types of errors
          if (error.response?.status === 500) {
            // For 500 errors, throw a specific error that can be handled by the frontend
            throw new Error('INTERNAL_SERVER_ERROR');
          } else if (error.response?.status === 401 || error.response?.status === 403) {
            // For authentication errors
            throw new Error('INVALID_CREDENTIALS');
          } else if (error.response?.data?.message) {
            // If backend provides a specific error message
            throw new Error(error.response.data.message);
          } else {
            // Generic error fallback
            throw new Error('LOGIN_FAILED');
          }
        }
      },
    }),
  ],

  session: {
    strategy: 'jwt',
    maxAge: 24 * 60 * 60,
    updateAge: 0,
  },

  callbacks: {
    async jwt({ token, user }): Promise<JWT> {
      if (user) {
        const extendedUser = user as ExtendedUser;
        return {
          ...token,
          user: {
            id: extendedUser.id,
            name: extendedUser.name!,
            email: extendedUser.email!,
            user_type: extendedUser.user_type,
            status: extendedUser.status,
            phone: extendedUser.phone,
          },
          accessToken: extendedUser.accessToken,
          accessTokenExpires: extendedUser.accessTokenExpires,
        } as JWT;
      }

      // 3) On subsequent calls, check expiry
      if (token.accessTokenExpires && Date.now() < token.accessTokenExpires) {
        return token;
      }

      // Token expired - instead of forcing re-auth, return token with error
      // This allows the frontend to handle the expired token gracefully
      return {
        ...token,
        accessToken: null,
        error: 'TokenExpired',
      } as JWT;
    },

    // 4) Expose user + token to the client session
    async session({ session, token }) {
      if (token.user) {
        return {
          ...session,
          user: {
            ...session.user,
            ...token.user,
          },
          accessToken: token.accessToken,
          error: token.error,
        };
      }
      return session;
    },
  },

  events: {
    // 5) Revoke on signOut
    async signOut({ token }) {
      if (token) await revokeAccessToken(token);
    },
  },

  pages: {
    signIn: '/auth/login',
    signOut: '/auth/logout',
    error: '/auth/error',
  },

  secret: TOKEN_SECRET,
};

export default NextAuth(authOptions);
