import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import useTranslation from 'next-translate/useTranslation';
import {
  Container, Loader, Center, Alert,
} from '@mantine/core';
import { IconAlertCircle } from '@tabler/icons-react';
import { useProfileData } from './hooks/useProfileData';
import { useProfileForm } from './hooks/useProfileForm';
import { useProfileSubmit } from './hooks/useProfileSubmit';
import ProfileFormContainer from './ProfileFormContainer';
import type { ProfilePageProps } from './types';

// eslint-disable-next-line no-empty-pattern
export default function ProfilePage({}: ProfilePageProps) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [isEditing, setIsEditing] = useState(false);
  const { t } = useTranslation('profile');
  const { t: tCommon } = useTranslation('common');

  // Redirect if not authenticated
  useEffect(() => {
    if (status === 'loading') return;
    if (!session) {
      router.push('/auth/login');
    }
  }, [session, status, router]);

  // Get profile data
  const {
    profileData,
    isLoading,
    error,
    accessPointsData,
  } = useProfileData();

  // Form management - validation is now handled in useProfileForm
  const {
    form,
    geoLocation,
    transitPointIds,
    handleLocationChange,
    handleTransitPointsChange,
  } = useProfileForm({
    profileData,
    session,
  });

  // Submit handling
  const { handleFormSubmit, isLoading: isSubmitting } = useProfileSubmit({
    session,
    geoLocation,
    transitPointIds,
    form,
    successMessage: t('profileUpdated'),
    onSuccess: () => {
      setIsEditing(false);
    },
  });

  // Loading state
  if (status === 'loading' || isLoading) {
    return (
      <Center style={{ height: '100vh' }}>
        <Loader size="lg" />
      </Center>
    );
  }

  // Error state
  if (error) {
    return (
      <Container size="md" mt="xl">
        <Alert icon={<IconAlertCircle size="1rem" />} title={tCommon('error')} color="red">
          {t('profileUpdateFailed')}
        </Alert>
      </Container>
    );
  }

  // No data state
  if (!session || !profileData?.data?.user) {
    return (
      <Container size="md" mt="xl">
        <Alert icon={<IconAlertCircle size="1rem" />} title="No Data" color="orange">
          No profile data found. Please contact support.
        </Alert>
      </Container>
    );
  }

  const { user } = profileData.data;
  const userType = session.user?.user_type || user?.userType || '';

  return (
    <Container size="xl" pt={10}>
      <ProfileFormContainer
        form={form}
        isEditing={isEditing}
        user={user}
        userType={userType}
        onSubmit={handleFormSubmit}
        geoLocation={geoLocation}
        transitPointIds={transitPointIds}
        accessPointsData={accessPointsData}
        onLocationChange={handleLocationChange}
        onTransitPointsChange={handleTransitPointsChange}
        onEdit={() => setIsEditing(true)}
        onCancel={() => {
          setIsEditing(false);
          form.reset(); // Reset form to original values on cancel
        }}
        isLoading={isSubmitting}
      />
    </Container>
  );
}
