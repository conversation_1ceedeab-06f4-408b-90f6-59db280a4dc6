/* eslint-disable sonarjs/no-duplicate-string */
export const API_ENDPOINT = {
  // Auth endpoints
  auth: {
    login: '/auth/login',
    register: '/auth/register',
    logout: '/auth/logout',
    forgotPassword: '/auth/forgot-password',
    resetPassword: '/auth/reset-password',
    verifyEmail: '/auth/verify-email',
    verifyResetToken: '/auth/verify-reset-token',
    changePassword: '/auth/change-password',
    sendVerificationOtp: '/auth/send-verification-otp',
    resendVerificationOtp: '/auth/resend-verification-otp',
  },

  users: {
    profile: '/users/profile',
    updateProfile: '/users/profile',
  },

  uploads: {
    photo: '/uploads/photo',
  },

  accessPoints: {
    base: '/access-points',
    list: '/access-points',
    create: '/access-points',
    update: '/access-points',
    byId: (id: string) => `/access-points/${id}`,
  },

  shipments: {
    base: '/shipments',
    byId: (id: string) => `/shipments/${id}`,
    cancel: (id: string) => `/shipments/${id}/cancel`,
    my: '/shipments/my',
    pending: '/shipments/pending',
    scan: '/shipments/scan',
    deliver: '/shipments/deliver',
  },

  qrLabels: {
    generateForShipment: '/qr-labels/generate-for-shipment',
    generatePDF: '/qr-labels/generate-pdf',
    list: '/qr-labels',
    byId: (id: string) => `/qr-labels/${id}`,
  },
  dashboard: {
    base: '/dashboard',
  },

  notifications: {
    base: '/notifications',
    list: '/notifications',
    unreadCount: '/notifications/unread-count',
    markAsRead: (id: string) => `/notifications/${id}/read`,
    markAllAsRead: '/notifications/mark-all-read',
    preferences: '/notifications/preferences',
    filterOptions: '/notifications/filter-options',
  },
};
