import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import {
  Container,
  Title,
  Paper,
  Stack,
  Loader,
  Center,
  NumberInput,
  Button,
  Group,
  ThemeIcon,
  Divider,
} from '@mantine/core';
import {
  IconFileTypePdf,
  IconQrcode,
} from '@tabler/icons-react';
import { useGeneratePDFMutation } from '../src/requests/qr-labels/calls';
import { notifications } from '@mantine/notifications';
import { saveAs } from 'file-saver';
import useTranslation from 'next-translate/useTranslation';

export default function QRGeneratorPage() {
  const { mutate: generatePDF, isPending } = useGeneratePDFMutation();
  const [count, setCount] = useState<number>(1);

  const { data: session, status } = useSession();
  const router = useRouter();
  const { t } = useTranslation('qr');

  useEffect(() => {
    if (status === 'loading') return; // Still loading

    if (!session) {
      router.push('/auth/login');
      return;
    }

    // Check if user is an access operator
    if (session.user?.user_type !== 'ACCESS_OPERATOR') {
      router.push('/'); // Redirect to home if not an access operator
    }
  }, [session, status, router]);

  if (status === 'loading') {
    return (
      <Center style={{ height: '100vh' }}>
        <Loader />
      </Center>
    );
  }

  if (!session || session.user?.user_type !== 'ACCESS_OPERATOR') {
    return null; // Will redirect
  }

  const handleGenerate = () => {
    if (!count || count < 1) {
      notifications.show({
        title: t('invalidCount'),
        message: t('invalidCountMessage'),
        color: 'orange',
      });
      return;
    }

    generatePDF({ data: { count } }, {
      onSuccess: (res) => {
        const { blob } = res.data;
        if (blob) {
          saveAs(blob, `qr_labels_${Date.now()}.pdf`);
        }
      },
    });
  };

  return (
    <Container size="sm" py="xl">
      <Paper
        shadow="sm"
        p="xl"
        radius="lg"
        withBorder
        style={{
          maxWidth: 500,
          margin: '0 auto',
        }}
      >
        <Stack gap="lg">
          {/* Header */}
          <Group gap="sm">
            <ThemeIcon
              size="lg"
              radius="md"
              variant="light"
              color="blue"
            >
              <IconQrcode size="1.2rem" />
            </ThemeIcon>
            <Title order={3} c="var(--mantine-color-text)">
              {t('title')}
            </Title>
          </Group>

          <Divider />

          {/* Number Input */}
          <NumberInput
            label={t('numberOfCodes')}
            description={t('numberOfCodesDescription')}
            min={1}
            max={50}
            value={count}
            onChange={(v) => setCount(Number(v))}
            size="md"
            radius="md"
            styles={{
              label: {
                fontSize: '1rem',
                fontWeight: 500,
                marginBottom: 8,
                color: 'var(--mantine-color-text)',
              },
              description: {
                color: 'var(--mantine-color-dimmed)',
              },
            }}
          />

          {/* Generate Button */}
          <Button
            size="lg"
            radius="md"
            variant="gradient"
            gradient={{ from: 'blue', to: 'purple', deg: 45 }}
            leftSection={<IconFileTypePdf size="1.1rem" />}
            loading={isPending}
            onClick={handleGenerate}
            fullWidth
            style={{
              height: 50,
              fontSize: '1rem',
              fontWeight: 600,
            }}
          >
            {t('generateAndDownload')}
          </Button>
        </Stack>
      </Paper>
    </Container>
  );
}
