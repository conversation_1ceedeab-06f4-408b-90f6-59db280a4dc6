{"pendingShipments": "Pending Shipments", "incomingShipments": "Incoming Shipments", "myShipments": "My Shipments", "availableShipments": "Available Shipments", "inTransitShipments": "In Transit Shipments", "allShipments": "All Shipments", "allShipmentsDescription": "All shipments assigned to you as a car operator", "createNewShipment": "Create New Shipment", "createShipment": "Create Shipment", "backToShipments": "Back to Shipments", "refresh": "Refresh", "search": "Search", "searchShipments": "Search shipments...", "filterByStatus": "Filter by status", "itemsPerPage": "Items per page", "perPageCount": "{{count}} per page", "clearSearch": "Clear Search", "clearFilters": "Clear Filters", "filteredResults": "Filtered results", "tryAgain": "Try Again", "cancel": "Cancel", "save": "Save", "submit": "Submit", "loading": "Loading", "initializing": "Initializing...", "invalidDate": "Invalid date", "loadingShipments": "Loading shipments...", "loadingPendingShipments": "Loading pending shipments...", "loadingIncomingShipments": "Loading incoming shipments...", "loadingMyShipments": "Loading my shipments...", "loadingAvailableShipments": "Loading available shipments...", "loadingInTransitShipments": "Loading in transit shipments...", "errorLoadingShipments": "Error loading shipments", "failedToLoadShipments": "Failed to load shipments. Please try again.", "failedToLoadPendingShipments": "Failed to load pending shipments. Please try again.", "failedToLoadIncomingShipments": "Failed to load incoming shipments. Please try again.", "failedToLoadMyShipments": "Failed to load my shipments. Please try again.", "failedToLoadAvailableShipments": "Failed to load available shipments. Please try again.", "failedToLoadInTransitShipments": "Failed to load in transit shipments. Please try again.", "noShipments": "No Shipments", "noPendingShipments": "No Pending Shipments", "noIncomingShipments": "No Incoming Shipments", "noMyShipments": "No My Shipments", "noAvailableShipments": "No Available Shipments", "noInTransitShipments": "No In Transit Shipments", "noShipmentsFound": "No shipments found", "noShipmentsYet": "No shipments yet", "noShipmentsMatchFilters": "No shipments match your filters", "createFirstShipment": "Create your first shipment to get started", "adjustFilters": "Try adjusting your search or filter criteria", "noShipmentsMatch": "No shipments match your search criteria. Try adjusting your search terms.", "noPendingShipmentsMessage": "There are currently no pending shipments at your access point.", "noIncomingShipmentsMessage": "There are currently no incoming shipments to your access point.", "noMyShipmentsMessage": "You have no assigned shipments at the moment.", "noAvailableShipmentsMessage": "There are currently no shipments available for pickup.", "noInTransitShipmentsMessage": "You have no shipments currently in transit.", "searchPlaceholder": "Search by tracking code, customer name, or description...", "searchPendingPlaceholder": "Search pending shipments...", "searchIncomingPlaceholder": "Search incoming shipments...", "searchMyPlaceholder": "Search my shipments...", "searchAvailablePlaceholder": "Search available shipments...", "searchInTransitPlaceholder": "Search in transit shipments...", "showing": "Showing", "of": "of", "shipments": "shipments", "pendingShipmentsLower": "pending shipments", "incomingShipmentsLower": "incoming shipments", "myShipmentsLower": "my shipments", "availableShipmentsLower": "available shipments", "inTransitShipmentsLower": "in transit shipments", "matching": "matching", "trackingCode": "Tracking Code", "customer": "Customer", "destination": "Destination", "status": "Status", "createdAt": "Created", "updatedAt": "Updated", "actions": "Actions", "view": "View", "assign": "Assign", "assignPackage": "Assign Package", "pickup": "Pickup", "deliver": "Deliver", "scanArrival": "<PERSON><PERSON>", "markAsDelivered": "<PERSON> as Delivered", "cancelShipment": "Cancel Shipment", "shipmentDetails": "Shipment Details", "shipmentInformation": "Shipment Information", "customerInformation": "Customer Information", "routeInformation": "Route Information", "trackingInformation": "Tracking Information", "description": "Description", "noDescriptionProvided": "No description provided", "weight": "Weight", "dimensions": "Dimensions", "sizeSmall": "Small", "sizeMedium": "Medium", "sizeLarge": "Large", "sizeExtraLarge": "Extra Large", "shipmentId": "Shipment ID", "weightAndSize": "Weight & Size", "lastUpdated": "Last Updated", "value": "Value", "notes": "Notes", "originAccessPoint": "Origin Access Point", "destinationAccessPoint": "Destination Access Point", "assignedCarOperator": "Assigned Car Operator", "estimatedDelivery": "Estimated Delivery", "actualDelivery": "Actual Delivery", "shipmentHistory": "Shipment History", "statusHistory": "Status History", "created": "Created", "assigned": "Assigned", "pickedUp": "Picked Up", "inTransit": "In Transit", "arrived": "Arrived at Destination", "delivered": "Delivered", "cancelled": "Cancelled", "expired": "Expired", "pending": "Pending", "readyForPickup": "Ready for Pickup", "timeRemainingHours": "{{hours}}h {{minutes}}m remaining", "timeRemainingMinutes": "{{minutes}}m remaining", "awaitingPickup": "Awaiting <PERSON><PERSON>", "createNewShipmentTitle": "Create New Shipment", "createNewShipmentSubtitle": "Fill in the details below to create a new shipment. Make sure all information is accurate before submitting.", "reminderText": "⏰ Remember: You have 24 hours after creation to visit the origin AO and scan your package.", "shipmentCreatedSuccessMessage": "Your shipment has been created. Remember: You have 24 hours to visit the origin AO and scan your package!", "destinationMustBeDifferent": "Destination must be different from origin", "pleaseCorrectErrors": "Please correct the errors", "formValidationMessage": "There are some issues with your submission. Please review the form and try again.", "loadingAccessPoints": "Loading access points...", "error": "Error", "failedToLoadAccessPoints": "Failed to load access points. Please try again.", "noActiveAccessPoints": "No active access points available. Please contact support.", "weightKg": "Weight (kg)", "enterWeight": "Enter weight", "selectPackageSize": "Select package size", "describeContents": "Describe the contents of your shipment", "receiverName": "Receiver Name", "enterReceiverName": "Enter receiver's full name", "receiverPhone": "Receiver Phone", "shipmentCreatedSuccess": "Shipment created successfully!", "shipmentUpdatedSuccess": "Shipment updated successfully!", "shipmentCancelledSuccess": "Shipment cancelled successfully!", "shipmentAssignedSuccess": "Shipment assigned successfully!", "shipmentPickedUpSuccess": "Shipment picked up successfully!", "shipmentDeliveredSuccess": "Shipment delivered successfully!", "failedToCancelShipment": "Failed to Cancel Shipment", "failedToAssignShipment": "Failed to assign shipment", "failedToPickupShipment": "Failed to pickup shipment", "failedToDeliverShipment": "Failed to deliver shipment", "confirmCancelShipment": "Are you sure you want to cancel this shipment?", "confirmAssignShipment": "Are you sure you want to assign this shipment?", "confirmPickupShipment": "Are you sure you want to pickup this shipment?", "confirmDeliverShipment": "Are you sure you want to mark this shipment as delivered?", "cancelWarning": "This action cannot be undone. The shipment will be marked as cancelled and cannot be processed further.", "receiver": "Receiver", "keepShipment": "Keep Shipment", "cancelShipmentWarning": "This action cannot be undone.", "assignShipmentWarning": "This will assign the shipment to you.", "pickupShipmentWarning": "Make sure you have physically collected the package.", "deliverShipmentWarning": "Make sure the package has been delivered to the recipient.", "origin": "Origin", "noAccessPoints": "No Access Points", "noAccessPointsMessage": "There are no available access points to display on the map.", "originSet": "Origin Set", "setOrigin": "Set Origin", "destinationSet": "Destination Set", "setDestination": "Set Destination", "selectBothAccessPoints": "Please select both origin and destination access points on the map above.", "selectDestinationAccessPoint": "Great! Now please select a destination access point.", "selectOriginAccessPoint": "Great! Now please select an origin access point.", "accessPointsSelectedSuccess": "Perfect! You have selected both access points. You can now proceed to fill in the shipment details below.", "shipmentNumber": "Shipment #", "packageInformation": "Package Information", "receiverInformation": "Receiver Information", "accessPointsAndRoute": "Access Points & Route", "shipmentRoute": "Shipment Route", "pickupQRCode": "Pickup QR Code", "trackingCodeTitle": "Tracking Code", "timeline": "Timeline", "shipmentCreated": "Shipment Created", "shipmentCreatedDescription": "Your shipment has been created and is waiting for pickup", "readyForPickupTitle": "Ready for Pickup", "readyForPickupDescription": "Shipment is at origin and ready for pickup by carrier", "inTransitTitle": "In Transit", "inTransitDescription": "Shipment has been picked up and is on its way", "arrivedAtDestination": "Arrived at Destination", "arrivedAtDestinationDescription": "Shipment has arrived at the destination access point", "deliveredTitle": "Delivered", "deliveredDescription": "Shipment has been successfully delivered to the receiver", "cancelledTitle": "Cancelled", "cancelledDescription": "Shipment was cancelled: {{reason}}", "noReasonProvided": "No reason provided", "actionRequired24Hour": "Action Required: 24-Hour Time Limit", "visit24HourMessage": "You have 24 hours to visit the origin AO and scan your package.", "shipmentExpired": "Shipment Expired", "shipmentExpiresSoon": "Shipment Expires Soon", "expiredMessage": "This shipment expired on {{date}}. Please contact support for assistance.", "expiresInMessage": "This shipment expires in {{timeRemaining}}. Please take action soon.", "showQRCodeForPickup": "Show this QR code for package pickup", "useToTrackShipment": "Use this to track your shipment", "size": "Size", "expires": "Expires", "name": "Name", "phone": "Phone", "packageDropoffLocation": "Package Drop-off Location", "packagePickupLocation": "Package Pickup Location", "contactInformation": "Contact Information", "operatingHours": "Operating Hours", "operatingHoursTime": "8:00 AM - 6:00 PM", "availableForDropoff": "Available for drop-off", "availableForPickup": "Available for pickup", "active": "ACTIVE", "from": "FROM", "to": "TO", "importantInstructions": "Important Instructions:", "dropoffInstruction": "Drop-off: Take your package to {{location}} during operating hours (8:00 AM - 6:00 PM).", "pickupInstruction": "Pickup: The recipient can collect the package from {{location}} once it arrives.", "contactInstruction": "Contact: Both locations can be contacted for updates about your shipment.", "routeInformationTitle": "Route Information:", "greenMarkerInfo": "• Green marker (O) shows the origin access point where you drop off your package", "redMarkerInfo": "• Red marker (D) shows the destination access point where the receiver picks up", "important": "Important", "dropoffLocationAlert": "Please ensure you drop off your package at the Origin location and the recipient can collect it from the Destination location during operating hours.", "dropoffLocation": "Drop-off location", "pickupLocation": "Pickup location", "statusPending": "Pending", "statusAwaitingPickup": "Awaiting <PERSON><PERSON>", "statusInTransit": "In Transit", "statusArrivedAtDestination": "Arrived At Destination", "statusDelivered": "Delivered", "statusCancelled": "Cancelled", "sizeSmallLabel": "Small", "sizeMediumLabel": "Medium", "sizeLargeLabel": "Large", "sizeExtraLargeLabel": "Extra Large", "notAssigned": "Not assigned", "kg": " kg", "success": "Success", "shipmentCancelledSuccessfully": "Shipment cancelled successfully", "myAssignedShipments": "My Assigned Shipments", "viewAndManageAssigned": "View and manage shipments you have assigned", "searchShipmentsPlaceholder": "Search shipments...", "allStatuses": "All Statuses", "perPage10": "10 per page", "perPage25": "25 per page", "perPage50": "50 per page", "loadingAssignedShipments": "Loading assigned shipments...", "noAssignedShipmentsYet": "No assigned shipments yet", "showingResults": "Showing {{count}} of {{total}} assigned shipments", "withCurrentFilters": "with current filters", "totalAssigned": "Total Assigned", "statusReadyForPickup": "Ready For Pickup", "statusExpired": "Expired", "assignedOn": "Assigned on", "packageDetails": "Package Details", "statusTimeline": "Status Timeline", "currentStatus": "Current Status", "backToShipmentsList": "Back to Shipments List", "processPickup": "Process Pickup", "assignPackageToShipment": "Assign Package to Shipment", "shipmentQR": "Shipment QR", "photo": "Photo", "scanShipmentQRCode": "Scan shipment QR code", "takePackagePhoto": "Take package photo", "addNotes": "Add notes", "processComplete": "Process Complete", "packageSuccessfullyProcessed": "The package has been successfully processed", "close": "Close", "deliverShipment": "Deliver Shipment", "scanPackageQR": "Scan Package QR", "scanPickupQR": "<PERSON><PERSON> Pickup QR", "receiverInfo": "Receiver Info", "photoAndNotes": "Photo & Notes", "pickupComplete": "Pickup Complete", "scanQRCodeOnPackage": "Scan the QR code on the package", "scanAOQRCodeAttached": "Scan the AO QR code attached to the package.", "scanPickupQRFromReceiver": "Scan the pickup QR code from the receiver's phone or label.", "enterCodeManually": "Enter code manually", "scanWithCamera": "<PERSON><PERSON> with camera", "packageQR": "Package QR", "enterPackageQRCode": "Enter Package QR Code", "next": "Next", "pickupQR": "Pickup QR", "enterPickupQRCode": "Enter pickup QR code", "receiverPhoneNumber": "Receiver Phone Number", "enterReceiverPhoneNumber": "Enter receiver's phone number", "uploadPhotoProofDelivery": "Upload a photo as proof of delivery.", "notesOptional": "Notes (optional)", "anyAdditionalNotes": "Any additional notes", "confirmDelivery": "Confirm Delivery", "allFieldsRequired": "All fields are required.", "uploadFailed": "Upload Failed", "failedUploadPhotoTryAgain": "Failed to upload photo. Please try again.", "deliveryFailed": "Delivery Failed", "pickupCompleteMessage": "The package has been successfully delivered to the receiver.", "failedCompleteDeliveryTryAgain": "Failed to complete delivery. Please try again.", "back": "Back", "nextStep": "Next Step", "completeProcess": "Complete Process", "scanShipmentQRCodeDescription": "Scan Shipment QR Code:", "generateNewQRCode": "Generate New QR Code", "generatedQRCode": "Generated QR Code:", "scanThisQRCode": "Scan this QR code to assign the shipment", "cameraScan": "Camera Scan", "manualEntry": "Manual Entry", "enterShipmentQRCode": "Enter Shipment QR Code", "enterShipmentQRCodePlaceholder": "Enter the shipment QR code (e.g., AO_x7k9m2p1)", "enterShipmentQRCodeDescription": "Enter the shipment QR code", "pointCameraAtQR": "Point your camera at the shipment QR code", "takePhotoDocumentation": "Take a photo of the package for documentation", "takePhoto": "Take Photo", "uploadingPhoto": "Uploading photo...", "packagePhoto": "Package Photo", "photoUploadedSuccessfully": "Photo uploaded successfully!", "notesLabel": "Notes", "addNotesAboutPackage": "Add any notes about the package...", "pleaseTakePhoto": "Please take a photo", "pleaseScanQRCode": "Please scan or enter a shipment QR code", "takePhotoPickupDocumentation": "Take a photo of yourself picking up the package for documentation", "takePhotoDeliveryProof": "Take a photo as proof of delivery to the receiver", "takePickupPhoto": "Take Pickup Photo", "takeDeliveryPhoto": "Take Delivery Photo", "pickupPhoto": "Pickup Photo", "deliveryPhoto": "Delivery Photo", "addNotesAboutPickup": "Add any notes about the pickup...", "addNotesAboutDelivery": "Add any notes about the delivery...", "allShipmentsTitle": "All Shipments", "inTransitShipmentsTitle": "In-Transit Shipments", "inTransitShipmentsDescription": "Shipments currently in transit that can be assigned to destination AO", "total": "Total", "inTransitCount": "In Transit", "noShipmentsAvailableForPickup": "No shipments available for pickup", "shipmentsReadyForPickupWillAppearHere": "Shipments ready for pickup will appear here", "noShipmentsInTransit": "No shipments in transit", "availableShipmentsLowercase": "available shipments", "inTransitShipmentsLowercase": "in-transit shipments", "inTransitSince": "In Transit Since", "loadingMap": "Loading map...", "shipmentHash": "Shipment #", "filterByStatusPlaceholder": "Filter by status", "itemsPerPagePlaceholder": "Items per page", "viewButton": "View", "pickUpButton": "Pick Up", "assignButton": "Assign", "destinationAO": "Destination AO", "pickUpPackage": "Pick Up Package", "carOperatorPickup": "Car Operator Pickup", "readyForPickupSince": "Ready for pickup since", "assignToDestinationAO": "Assign to Destination AO", "deliverToReceiver": "Deliver to Receiver", "scanPackageQRCode": "Scan package QR code", "scanPackageQRCodeTitle": "Scan Package QR Code:", "scanPackageQRImportant": "Scan the QR code attached to the package. The QR code must start with \"AO_\" and belong to this shipment.", "scanPackageQRCodeDescription": "Point your camera at the package QR code", "enterPackageQRCodePlaceholder": "Enter the package QR code (e.g., AO_x7k9m2p1)", "enterPackageQRCodeDescription": "Enter the QR code from the package", "packageQRScanned": "Package QR scanned", "pleaseScanEnterPackageQR": "Please scan or enter the package QR code", "invalidQRFormat": "Invalid QR code format. Package QR must start with \"AO_\"", "deliveryComplete": "Delivery Complete!", "packagePickedUpSuccessMessage": "The package has been successfully picked up and is now in transit.", "packageDeliveredSuccessMessage": "The package has been successfully delivered to the receiver.", "stopScanning": "Stop Scanning", "startScanning": "Start Scanning", "markPackageArrivedDestinationAO": "<PERSON> as Arrived at Destination AO", "scanCode": "Scan Code", "scanAOCodeOnPackage": "Scan the AO code on the package", "markPackageArrivedForShipment": "Mark package as arrived for shipment", "onlyAOCodesCanBeUsed": "Only codes generated by an Access Operator can be used. The code must start with \"AO_\" and belong to this shipment.", "scanCodeAttachedFromOriginAO": "Scan the code attached to the package (from the origin AO)", "pointCameraAtCodeOnPackage": "Point your camera at the code attached to the package", "enterCode": "Enter Code", "enterCodePlaceholder": "Enter the code value (e.g., AO_x7k9m2p1)", "enterCodeFromPackage": "Enter the code from the package", "codeScannedSuccessfully": "Code scanned successfully!", "nextTakePhoto": "Next: Take Photo", "photoOfPackageAtDestination": "Photo of package at destination", "takePhotoWithCodeVisible": "Take a photo of the package with the code visible", "continueToAssignment": "Continue to Assignment", "completeArrivalAssignment": "Complete arrival assignment", "reviewCompleteArrivalAssignment": "Review and complete the arrival assignment", "addNotesPackageCondition": "Add any notes about the package condition...", "markAsArrived": "<PERSON> as Arrived", "packageMarkedAsArrived": "Package Marked as Arrived!", "arrivalCompletionMessage": "The code has been scanned and photo uploaded. The shipment status has been updated to ARRIVED_AT_DESTINATION. The receiver will be notified for pickup.", "markPackageAsArrived": "<PERSON> as Arrived", "shipmentCode": "Shipment Code", "scanShipmentCode": "Scan shipment code", "scanCodeAttachedToPackage": "Scan the code attached to the package. The code must start with \"AO_\" and belong to this shipment.", "pointCameraAtShipmentCode": "Point your camera at the shipment code", "enterShipmentCode": "Enter Shipment Code", "enterShipmentCodePlaceholder": "Enter the shipment code (e.g., AO_x7k9m2p1)", "enterShipmentCodeDescription": "Enter the shipment code", "addNotesAboutPackageArrival": "Add any notes about the package arrival...", "arrivalConfirmed": "Arrival Confirmed", "packageMarkedArrivedAtDestination": "The package has been successfully marked as arrived at the destination.", "takePhotoForDocumentation": "Take a photo of the package for documentation", "completePickup": "Complete Pickup", "completeDelivery": "Complete Delivery", "takePhotoProofDelivery": "Take a photo as proof of delivery to the receiver", "packageSuccessfullyPickedUp": "Package Successfully Picked Up!", "shipmentStatusUpdatedInTransit": "The shipment status has been updated to IN_TRANSIT. You can now transport the package to the destination.", "pleaseScanEnterPickupCode": "Please scan or enter the pickup QR code", "pickupCodeScanned": "Pickup QR scanned", "scanPickupCode": "Scan Pickup QR Code", "scanPickupCodeFromReceiver": "Scan the pickup QR code from the receiver's phone or printed label", "pointCameraAtReceiverPickupCode": "Point your camera at the receiver's pickup QR code", "enterPickupCode": "Enter Pickup QR Code", "enterPickupCodePlaceholder": "Enter the pickup QR code (e.g., PICKUP123456)", "enterPickupCodeFromReceiver": "Enter the pickup QR code from the receiver"}