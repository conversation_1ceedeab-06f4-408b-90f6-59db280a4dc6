/* Global CSS to force LTR behavior for PinInput components */

/* Force LTR direction for PinInput containers with maximum specificity */
.ltr-pin-input-container {
  direction: ltr !important;
  unicode-bidi: embed !important;
  writing-mode: horizontal-tb !important;
}

/* Force LTR direction for all PinInput inputs with maximum specificity */
.ltr-pin-input-container input,
.ltr-pin-input-container input[data-pin-input],
.ltr-pin-input-container .mantine-PinInput-input,
.ltr-pin-input-container input[type="text"] {
  direction: ltr !important;
  text-align: center !important;
  unicode-bidi: embed !important;
  font-family: monospace, sans-serif !important;
  font-size: 1.1rem !important;
  font-weight: 600 !important;
  writing-mode: horizontal-tb !important;
}

/* Override RTL styles with maximum specificity */
[dir="rtl"] .ltr-pin-input-container,
[dir="rtl"] .ltr-pin-input-container input,
[dir="rtl"] .ltr-pin-input-container input[data-pin-input],
[dir="rtl"] .ltr-pin-input-container .mantine-PinInput-input,
[dir="rtl"] .ltr-pin-input-container input[type="text"],
html[lang="ar"] .ltr-pin-input-container,
html[lang="ar"] .ltr-pin-input-container input,
html[lang="ar"] .ltr-pin-input-container input[data-pin-input],
html[lang="ar"] .ltr-pin-input-container .mantine-PinInput-input,
html[lang="ar"] .ltr-pin-input-container input[type="text"],
body[dir="rtl"] .ltr-pin-input-container,
body[dir="rtl"] .ltr-pin-input-container input,
body[dir="rtl"] .ltr-pin-input-container input[data-pin-input],
body[dir="rtl"] .ltr-pin-input-container .mantine-PinInput-input,
body[dir="rtl"] .ltr-pin-input-container input[type="text"] {
  direction: ltr !important;
  text-align: center !important;
  unicode-bidi: embed !important;
  writing-mode: horizontal-tb !important;
}
