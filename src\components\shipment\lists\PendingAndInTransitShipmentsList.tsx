/* eslint-disable sonarjs/no-duplicate-string */
import { useState } from 'react';
import {
  Stack,
  Box,
  Paper,
} from '@mantine/core';
import { useSession } from 'next-auth/react';
import useTranslation from 'next-translate/useTranslation';
import { useIsClient } from '../../../hooks/useIsClient';
import { Shipment } from '../../../requests/shipment';
import AssignShipmentModal from '../assign/AssignShipmentModal';
import { ShipmentArrivalModal } from '../modals';
import PendingShipmentCard from '../cards/PendingShipmentCard';

interface ShipmentsListProps {
  shipments: Shipment[];
  // eslint-disable-next-line react/require-default-props
  onRefresh?: () => void;
}

export default function PendingAndInTransitShipmentsList({
  shipments,
  onRefresh,
}: ShipmentsListProps) {
  const { t } = useTranslation('shipments');
  const isClient = useIsClient();
  const [assignModalOpened, setAssignModalOpened] = useState(false);
  const [arrivalModalOpened, setArrivalModalOpened] = useState(false);
  const [shipmentToAssign, setShipmentToAssign] = useState<Shipment | null>(null);
  const [shipmentForArrival, setShipmentForArrival] = useState<Shipment | null>(null);
  const { data: sessionData } = useSession();

  // Add client-side rendering guard to prevent hydration issues
  if (!isClient) {
    return (
      <Stack gap="md">
        {Array.from({ length: 3 }).map((_, index) => (
          // eslint-disable-next-line react/no-array-index-key
          <Paper key={index} p="md" withBorder>
            <Box h={120} bg="gray.1" style={{ borderRadius: 'var(--mantine-radius-sm)' }} />
          </Paper>
        ))}
      </Stack>
    );
  }

  const handleAssignShipment = (shipment: Shipment) => {
    const userId = sessionData?.user?.id;
    if (!userId) return;

    // Determine which modal to open based on the action needed
    if (shipment.originAoId === userId && shipment.status === 'PENDING') {
      // Origin AO drop-off - use AssignShipmentModal
      setShipmentToAssign(shipment);
      setAssignModalOpened(true);
    } else if (shipment.destAoId === userId && shipment.status === 'IN_TRANSIT') {
      // Destination AO arrival - use ShipmentArrivalModal
      setShipmentForArrival(shipment);
      setArrivalModalOpened(true);
      // eslint-disable-next-line sonarjs/no-duplicated-branches
    } else {
      // Default case - use AssignShipmentModal
      setShipmentToAssign(shipment);
      setAssignModalOpened(true);
    }
  };

  const handleAssignSuccess = () => {
    setAssignModalOpened(false);
    setShipmentToAssign(null);
    if (onRefresh) {
      onRefresh();
    }
  };

  const handleArrivalSuccess = () => {
    setArrivalModalOpened(false);
    setShipmentForArrival(null);
    if (onRefresh) {
      onRefresh();
    }
  };

  return (
    <>
      <AssignShipmentModal
        opened={assignModalOpened}
        onClose={() => setAssignModalOpened(false)}
        shipment={shipmentToAssign}
        onSuccess={handleAssignSuccess}
      />

      <ShipmentArrivalModal
        opened={arrivalModalOpened}
        onClose={() => setArrivalModalOpened(false)}
        shipment={shipmentForArrival}
        onSuccess={handleArrivalSuccess}
      />

      <Stack gap="md">
        {shipments.map((shipment) => {
          const actionConfig = shipment.status === 'IN_TRANSIT'
            ? { label: t('scanArrival'), color: 'green' as const }
            : { label: t('assignPackage'), color: 'blue' as const };

          return (
            <PendingShipmentCard
              key={shipment.id}
              shipment={shipment}
              onAction={handleAssignShipment}
              actionConfig={actionConfig}
            />
          );
        })}
      </Stack>
    </>
  );
}
