import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import { Center, Loader } from '@mantine/core';
import { useIsClient } from '../../../src/hooks/useIsClient';

/**
 * Index page for AO shipments - redirects to pending shipments
 * This ensures the menu structure works properly
 */
export default function AOShipmentsIndexPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const isClient = useIsClient();

  useEffect(() => {
    if (isClient && status === 'authenticated') {
      if (session?.user?.user_type === 'ACCESS_OPERATOR') {
        // Redirect AO users to pending shipments
        router.replace('/access-operator/shipments/pending');
      } else {
        // Redirect non-AO users to home
        router.replace('/');
      }
    } else if (isClient && status === 'unauthenticated') {
      // Redirect unauthenticated users to login
      router.replace('/auth/login');
    }
  }, [isClient, status, session, router]);

  // Show loading while redirecting
  return (
    <Center h="100vh">
      <Loader size="lg" />
    </Center>
  );
}
