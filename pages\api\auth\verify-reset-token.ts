import { NextApiRequest, NextApiResponse } from 'next';
import { BACKEND_API } from '../../../src/lib/axios';
import { API_ENDPOINT, HTTP_CODE } from '../../../src/data';
import { verifyResetTokenApiResponseSchema } from '../../../src/requests/auth/response-transform';

async function handleGet(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Extract token and email from query parameters
    const { token, email } = req.query;

    // Validate required parameters
    if (!token || !email) {
      return res.status(HTTP_CODE.BAD_REQUEST).json({
        success: false,
        message: 'Token and email are required',
        error: {
          type: 'VALIDATION_ERROR',
          details: 'Missing token or email parameters'
        }
      });
    }

    // Call backend API to verify the reset token
    console.log('Calling backend API:', `${process.env.BACKEND_API_URL || 'http://localhost:8000'}/api${API_ENDPOINT.auth.verifyResetToken}`);
    console.log('Params:', { token, email });
    
    const response = await BACKEND_API.get(
      API_ENDPOINT.auth.verifyResetToken,
      {
        params: { token, email },
      },
    );

    const validatedResponse = verifyResetTokenApiResponseSchema.parse(response.data);

    // Return the response as JSON
    return res.status(HTTP_CODE.SUCCESS).json(validatedResponse);
  } catch (e: any) {
    console.error('Error in verify-reset-token API:', e.message);
    console.error('Error details:', e.response?.data || e);
    
    // Handle backend API errors
    if (e.response) {
      return res.status(e.response.status).json(e.response.data);
    }
    
    // Handle connection errors (backend not running)
    if (e.code === 'ECONNREFUSED' || e.code === 'ENOTFOUND') {
      return res.status(HTTP_CODE.SERVICE_UNAVAILABLE).json({
        success: false,
        message: 'Backend service is not available. Please try again later.',
        error: {
          type: 'SERVICE_ERROR',
          details: 'Cannot connect to backend service'
        }
      });
    }
    
    // Handle other errors
    return res.status(HTTP_CODE.BAD_REQUEST).json({
      success: false,
      message: 'Invalid or expired reset token',
      error: {
        type: 'VALIDATION_ERROR',
        details: 'Invalid or expired reset token'
      }
    });
  }
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'GET') {
    return handleGet(req, res);
  }

  res.setHeader('Allow', ['GET']);
  return res.status(HTTP_CODE.METHOD_NOT_ALLOWED).json({
    success: false,
    message: `Method ${req.method} not allowed`,
  });
}
