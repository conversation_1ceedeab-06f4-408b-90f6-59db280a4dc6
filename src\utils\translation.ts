/* eslint-disable no-return-await */
import { getCookie } from 'cookies-next';
import getT from 'next-translate/getT';
import { defaultLocale, LocaleCookie } from '../data';

/**
 * Get the current locale from cookie or fallback to default
 * Safe for both client and server side usage
 */
export const getCurrentLocale = (): string => {
  try {
    if (typeof window !== 'undefined') {
      // Client side - safe to access cookies
      const locale = getCookie(LocaleCookie);
      return (locale as string) || defaultLocale;
    }
    // Server side - return default locale
    return defaultLocale;
  } catch (error) {
    // Fallback in case of any cookie access issues
    return defaultLocale;
  }
};

/**
 * Get translation function for a specific namespace
 */
export const getTranslation = async (namespace: string = 'common') => {
  const locale = getCurrentLocale();
  return await getT(locale, namespace);
};

/**
 * Get translation function for common namespace
 */
export const getCommonTranslation = async () => await getTranslation('common');

/**
 * Get translation function for error namespace
 */
export const getErrorTranslation = async () => await getTranslation('error');

/**
 * Translate a key with fallback
 */
export const translateWithFallback = async (
  key: string,
  fallback: string,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  params?: Record<string, any>,
  namespace: string = 'common',
): Promise<string> => {
  try {
    const t = await getTranslation(namespace);
    const translated = t(key, params);
    return translated === key ? fallback : translated;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.warn(`Translation failed for key: ${key}`, error);
    return fallback;
  }
};

/**
 * Translate error message with fallback
 */
export const translateError = async (
  key: string,
  fallback: string,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  params?: Record<string, any>,
): Promise<string> => translateWithFallback(key, fallback, params, 'error');

/**
 * Get direction based on current locale
 */
export const getDirection = (): 'ltr' | 'rtl' => {
  const locale = getCurrentLocale();
  return locale === 'ar' ? 'rtl' : 'ltr';
};

/**
 * Check if current locale is RTL
 */
export const isRTL = (): boolean => getDirection() === 'rtl';

/**
 * Format date according to locale
 */
export const formatDateForLocale = (date: Date | string): string => {
  const locale = getCurrentLocale();
  const dateObj = typeof date === 'string' ? new Date(date) : date;

  if (locale === 'ar') {
    return dateObj.toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  }

  return dateObj.toLocaleDateString('en-US', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  });
};

/**
 * Get locale-specific number formatting
 */
export const formatNumberForLocale = (number: number): string => {
  const locale = getCurrentLocale();
  return number.toLocaleString(locale === 'ar' ? 'ar-SA' : 'en-US');
};
