import { API_ENDPOINT } from '../../data';
import { CLIENT_API } from '../../lib/axios';
import { handleApiError } from '../../utils/handle-backend-error';
import { DashboardApiResponse } from './type';
import { QUERY_KEYS } from '../cache-invalidation';

export interface GetDashboardQueryProps {
  enabled?: boolean;
}

// Use centralized query key for dashboard
const queryKeys = QUERY_KEYS.dashboard;

/**
 * Fetch dashboard data for the currently authenticated user.
 * It relies on the Next.js API route `/api/dashboard`, which in turn
 * forwards the authenticated request to the backend.
 */
export const getDashboardRequest = (): Promise<DashboardApiResponse> => CLIENT_API.get(API_ENDPOINT.dashboard.base)
  .then((res) => res?.data as DashboardApiResponse)
  .catch((e) => {
    handleApiError(e);
    throw e?.response?.data;
  });

// React-Query friendly query factory
export const getDashboardQuery = (props?: GetDashboardQueryProps) => ({
  queryKey: [queryKeys],
  queryFn: () => getDashboardRequest(),
  refetchOnWindowFocus: false,
  enabled: props?.enabled,
});
