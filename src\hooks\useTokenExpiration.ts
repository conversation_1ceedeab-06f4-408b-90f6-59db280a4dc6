import { useSession, signOut } from 'next-auth/react';
import { useEffect } from 'react';
import { useRouter } from 'next/router';

/**
 * Hook to handle token expiration
 * Automatically redirects to login when token expires
 */
export const useTokenExpiration = () => {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    // Only check if we have a session and it's not loading
    if (status === 'authenticated' && session?.error === 'TokenExpired') {
      // Token has expired, sign out and redirect to login
      signOut({
        redirect: false,
        callbackUrl: '/auth/login',
      }).then(() => {
        router.push('/auth/login');
      });
    }
  }, [session, status, router]);

  return {
    isTokenExpired: session?.error === 'TokenExpired',
    isAuthenticated: status === 'authenticated' && !session?.error,
  };
};

export default useTokenExpiration;
