// Re-export the existing myShipmentsApiResponseSchema from shipment folder
// This maintains consistency and avoids duplication
// Import for local use
import { myShipmentsApiResponseSchema } from '../shipment/response-transformer';

export {
  myShipmentsApiResponseSchema,
  shipmentApiResponseSchema,
  shipmentBackendSchema,
  shipmentSchema,
} from '../shipment/response-transformer';

// Export schemas collection
export const myResponseSchemas = {
  myShipmentsApiResponse: myShipmentsApiResponseSchema,
};

export default myResponseSchemas;
