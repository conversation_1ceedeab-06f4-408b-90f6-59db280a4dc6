import { z } from 'zod';
import {
  getAccessPointApiResponseSchema,
  getAccessPointsApiResponseSchema,
  accessPointApiResponseSchema,
} from './response-transformer';
import {
  createAccessPointApiRequestSchema,
  updateAccessPointApiRequestSchema,
} from './request-transformer';
import { Pagination } from '../../types';

// Filter types for access point queries
export interface AccessPointFilter {
  search?: string;
  approved?: boolean | null;
  status?: string;
  hasLocation?: boolean | null;
  createdAtGte?: string | null;
  createdAtLte?: string | null;
  updatedAtGte?: string | null;
  updatedAtLte?: string | null;
}

// Query props interfaces following example pattern
export interface GetAccessPointQueryProps {
  id: string;
  enabled?: boolean;
  refetchOnWindowFocus?: boolean;
}

export interface GetAccessPointsQueryProps {
  pagination?: Pagination;
  filters?: AccessPointFilter;
  sort?: string;
  enabled?: boolean;
}

// Define AccessPoint type based on the transformer function return type
export type AccessPoint = ReturnType<typeof accessPointApiResponseSchema>;

// Request types
export type CreateAccessPointApiRequest = z.infer<typeof createAccessPointApiRequestSchema>;
export type UpdateAccessPointApiRequest = z.infer<typeof updateAccessPointApiRequestSchema>;

// Response types
export type GetAccessPointApiResponse = z.infer<typeof getAccessPointApiResponseSchema>;
export type GetAccessPointsApiResponse = z.infer<typeof getAccessPointsApiResponseSchema>;

// Legacy types for backward compatibility
export type AccessOperator = AccessPoint;
export type CreateAccessOperatorRequest = CreateAccessPointApiRequest;
export type UpdateAccessOperatorRequest = UpdateAccessPointApiRequest;

export interface CreateAccessPointQueryProps {
  data: CreateAccessPointApiRequest;
  enabled?: boolean;
}

export interface UpdateAccessPointQueryProps {
  data: UpdateAccessPointApiRequest;
  enabled?: boolean;
}
