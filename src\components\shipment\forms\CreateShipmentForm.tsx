/* eslint-disable react/require-default-props */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable sonarjs/cognitive-complexity */
/* eslint-disable complexity */
import { useState } from 'react';
import {
  Stack,
  Paper,
  Alert,
  Loader,
  Text,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { IconAlertCircle } from '@tabler/icons-react';
import { useQuery } from '@tanstack/react-query';
import useTranslation from 'next-translate/useTranslation';
import { AccessPoint, getAccessPointsQuery, SHIPMENT_SIZE } from '../../../requests';
import { useCreateShipmentMutation } from '../../../requests/hooks/enhanced-mutations';
import { createShipmentApiRequestSchema, CreateShipmentApiRequest } from '../../../requests/shipment';
import { AccessPointSelection, ShipmentForm } from '../components';

interface CreateShipmentFormProps {
  onSuccess?: (() => void) | undefined;
  onCancel?: (() => void) | undefined;
}

interface FormValues {
  originAoId: string;
  destAoId: string;
  weight: number;
  size: string;
  description: string;
  receiverName: string;
  receiverPhone: string;
}

export default function CreateShipmentForm({ onSuccess, onCancel }: CreateShipmentFormProps) {
  const { t } = useTranslation('shipments');
  const [selectedOrigin, setSelectedOrigin] = useState<AccessPoint | null>(null);
  const [selectedDestination, setSelectedDestination] = useState<AccessPoint | null>(null);

  const {
    data: accessPointsData,
    isLoading: isLoadingAccessPoints,
    error: accessPointsError,
  } = useQuery({
    ...getAccessPointsQuery({
      pagination: { page: 0, pageSize: 100 },
      enabled: true,
    }),
    refetchOnWindowFocus: false,
  });
  // Handle both transformed and raw response formats
  const accessPoints = accessPointsData?.data?.accessOperators || [];

  // Form setup
  const form = useForm<FormValues>({
    initialValues: {
      originAoId: '',
      destAoId: '',
      weight: 1,
      size: 'MEDIUM',
      description: '',
      receiverName: '',
      receiverPhone: '',
    },
    // Integrate Zod validation from the shipment request layer
    validate: (values) => {
      const parsed = createShipmentApiRequestSchema.safeParse(values);

      if (parsed.success) {
        return {} as Record<keyof FormValues, string | null>;
      }

      const errors: Partial<Record<keyof FormValues, string | null>> = {};
      parsed.error.errors.forEach((issue) => {
        const field = issue.path[0] as keyof FormValues;
        errors[field] = issue.message;
      });

      return errors as Record<keyof FormValues, string | null>;
    },
    validateInputOnBlur: true,
    validateInputOnChange: false,
  });

  // Handlers for map selections
  const handleOriginSelect = (accessPoint: AccessPoint) => {
    setSelectedOrigin(accessPoint);
    form.setFieldValue('originAoId', accessPoint.id);
    form.clearFieldError('originAoId');
  };

  const handleDestinationSelect = (accessPoint: AccessPoint) => {
    setSelectedDestination(accessPoint);
    form.setFieldValue('destAoId', accessPoint.id);
    form.clearFieldError('destAoId');
  };

  // Create shipment mutation with automatic cache invalidation
  const createMutation = useCreateShipmentMutation({
    successMessage: t('shipmentCreatedSuccessMessage'),
    onSuccess: () => {
      form.reset();
      setSelectedOrigin(null);
      setSelectedDestination(null);
      onSuccess?.();
    },
  });

  const handleSubmit = (values: FormValues) => {
    if (values.originAoId === values.destAoId) {
      form.setFieldError('destAoId', t('destinationMustBeDifferent'));
      return;
    }

    const requestData: CreateShipmentApiRequest = {
      originAoId: values.originAoId,
      destAoId: values.destAoId,
      weight: values.weight,
      size: values.size as typeof SHIPMENT_SIZE[number],
      description: values.description,
      receiverName: values.receiverName,
      receiverPhone: values.receiverPhone,
    };

    // Validate the data; if invalid, surface errors on the form & notification
    const parsed = createShipmentApiRequestSchema.safeParse(requestData);

    if (!parsed.success) {
      parsed.error.errors.forEach((err) => {
        const fieldName = err.path[0] as keyof FormValues;
        if (fieldName) {
          form.setFieldError(fieldName, err.message);
        }
      });

      return;
    }

    createMutation.mutate({ data: parsed.data });
  };

  // Enhanced form submission handler that prevents default behavior
  const handleFormSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    event.stopPropagation();

    // Trigger Mantine form validation
    const validation = form.validate();

    if (!validation.hasErrors) {
      handleSubmit(form.values);
    } else {
      notifications.show({
        title: t('pleaseCorrectErrors'),
        message: t('formValidationMessage'),
        color: 'orange',
        icon: <IconAlertCircle size="1rem" />,
      });
    }
  };

  if (isLoadingAccessPoints) {
    return (
      <Paper p="lg" withBorder>
        <Stack align="center" gap="md">
          <Loader size="lg" />
          <Text c="dimmed">{t('loadingAccessPoints')}</Text>
        </Stack>
      </Paper>
    );
  }

  if (accessPointsError || accessPoints.length === 0) {
    return (
      <Paper p="lg" withBorder>
        <Alert icon={<IconAlertCircle size="1rem" />} color="red" title={t('error')}>
          {accessPointsError
            ? t('failedToLoadAccessPoints')
            : t('noActiveAccessPoints')}
        </Alert>
      </Paper>
    );
  }

  return (
    <Paper p="lg" withBorder>
      <Stack gap="lg">
        <AccessPointSelection
          accessPoints={accessPoints}
          selectedOrigin={selectedOrigin}
          selectedDestination={selectedDestination}
          onOriginSelect={handleOriginSelect}
          onDestinationSelect={handleDestinationSelect}
          isLoading={isLoadingAccessPoints}
        />
        <ShipmentForm
          form={form}
          onSubmit={handleFormSubmit}
          onCancel={onCancel}
          isLoading={createMutation.isPending}
        />
      </Stack>
    </Paper>
  );
}
