/* eslint-disable camelcase */
import { NextApiRequest, NextApiResponse } from 'next';
import { getJwt } from '../../../src/utils';
import { BACKEND_API } from '../../../src/lib/axios';
import { HTTP_CODE } from '../../../src/data';
import createApiError from '../../../src/utils/create-api-error';
import { scanShipmentApiResponseSchema } from '../../../src/requests/shipment/response-transformer';
import { shipmentRequestSchemas } from '../../../src/requests/shipment';

/**
 * @description Handle POST request to scan shipment QR code
 */
async function handleScanShipment(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  try {
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');

    // Verify authentication
    const { token } = await getJwt(req);
    if (!token) {
      return res.status(HTTP_CODE.UNAUTHORIZED).json({
        success: false,
        message: 'Authentication required',
      });
    }
    const {
      qrValue, qr_value,
      photoUrl, photo_url,
      action,
      notes,
      shipmentId, shipment_id,
    } = req.body;

    const qrValueFinal = qrValue || qr_value;
    const photoUrlFinal = photoUrl || photo_url;
    const shipmentIdFinal = shipmentId || shipment_id;

    if (!qrValueFinal || !photoUrlFinal || !action || !shipmentIdFinal) {
      return res.status(HTTP_CODE.BAD_REQUEST).json({
        success: false,
        message: 'QR value, photo URL, action, and shipment ID are required',
      });
    }

    // Validate using Zod schemas and transform for backend
    const validatedData = shipmentRequestSchemas.scanShipment.parse({
      qrValue: qrValueFinal,
      photoUrl: photoUrlFinal,
      action,
      shipmentId: shipmentIdFinal,
      notes,
    });

    const scanData = shipmentRequestSchemas.scanShipmentBackend.parse(validatedData);

    // Send to backend API with no-cache headers
    const response = await BACKEND_API.post('/shipments/scan', scanData, {
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        Pragma: 'no-cache',
        Expires: '0',
        'If-None-Match': '',
      },
    });

    const validatedResponse = scanShipmentApiResponseSchema.parse(response.data);
    return res.status(HTTP_CODE.SUCCESS).json(validatedResponse);
  } catch (error: unknown) {
    // eslint-disable-next-line no-console
    console.log('error', JSON.stringify(error));
    const apiError = createApiError({ error });
    return res.status(apiError.code).json(apiError);
  }
}

/**
 * @description API endpoint to scan shipment QR codes
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'POST') {
    return handleScanShipment(req, res);
  }

  res.setHeader('Allow', ['POST']);
  return res.status(HTTP_CODE.METHOD_NOT_ALLOWED).json({
    success: false,
    message: `Method ${req.method} not allowed`,
    code: HTTP_CODE.METHOD_NOT_ALLOWED,
  });
}
