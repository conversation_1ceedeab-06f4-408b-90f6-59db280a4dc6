import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import useTranslation from 'next-translate/useTranslation';
import {
  Button,
  Paper,
  Title,
  Text,
  Container,
  Group,
  Anchor,
  Stack,
  LoadingOverlay,
} from '@mantine/core';
import CustomLTRPinInput from '../../src/components/common/CustomLTRPinInput';
import { useForm, zodResolver } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { emailSchema, VerifyFormValues, verifySchema } from '../../src/requests';
import { resendVerificationOtpRequest, verifyEmailOtpRequest } from '../../src/requests/auth/calls';

export default function VerifyEmailPage() {
  const router = useRouter();
  const { email: emailFromQuery } = router.query;

  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [userEmail, setUserEmail] = useState<string>('');
  const [showOtpSentMessage, setShowOtpSentMessage] = useState(false);
  const { t } = useTranslation('auth');
  const { t: tCommon } = useTranslation('common');

  useEffect(() => {
    if (router.isReady && typeof emailFromQuery === 'string') {
      try {
        emailSchema.parse({ email: emailFromQuery });
        setUserEmail(emailFromQuery);
      } catch (e) {
        notifications.show({
          title: t('invalidEmail'),
          message: tCommon('somethingWentWrong'),
          color: 'red',
        });
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [router.isReady, emailFromQuery]);

  const form = useForm<VerifyFormValues>({
    validate: zodResolver(verifySchema),
    initialValues: {
      otp: '',
    },
  });

  const handleVerifySubmit = async (values: VerifyFormValues) => {
    setIsLoading(true);
    try {
      const response = await verifyEmailOtpRequest({ data: { otp: values.otp } });

      if (response.success) {
        notifications.show({
          title: t('emailVerified'),
          message: t('emailVerifiedSuccessfully'),
          color: 'green',
        });
        router.push('/auth/login');
      }
    } catch (error: unknown) {
      const errorMessage = (error as { message?: string })?.message || t('verificationFailed');
      notifications.show({
        title: t('verificationFailed'),
        message: errorMessage,
        color: 'red',
      });
    }
    setIsLoading(false);
  };

  const handleResendOtp = async () => {
    if (!userEmail) {
      notifications.show({
        title: t('missingEmail'),
        message: tCommon('somethingWentWrong'),
        color: 'orange',
      });
      return;
    }
    setIsResending(true);
    try {
      const response = await resendVerificationOtpRequest({ data: { email: userEmail } });
      if (response.success) {
        notifications.show({
          title: t('otpResent'),
          message: t('verificationOtpResentSuccessfully'),
          color: 'blue',
        });
        setShowOtpSentMessage(true);
      } else {
        notifications.show({
          title: t('failedToResendOtp'),
          message: response.message || t('failedToResendOtp'),
          color: 'red',
        });
      }
    } catch (error: unknown) {
      const errorMessage = (error as { message?: string })?.message || t('failedToResendOtp');
      notifications.show({
        title: t('failedToResendOtp'),
        message: errorMessage,
        color: 'red',
      });
    }
    setIsResending(false);
  };

  return (
    <Container size={420} my={40}>
      <Title ta="center">
        {t('verifyYourEmail')}
      </Title>
      <Text c="dimmed" size="sm" ta="center" mt={5}>
        {t('enterSixDigitCode')}
      </Text>

      <Paper withBorder shadow="md" p={30} mt={30} radius="md">
        <LoadingOverlay visible={isLoading || isResending} />
        {userEmail && (
        <Text ta="center" mb="md">
          {t('verifyingEmail')}
          <strong>{userEmail}</strong>
        </Text>
        )}

        <form onSubmit={form.onSubmit(handleVerifySubmit)}>
          <Stack align="center">
            <Text>{t('enterOtp')}</Text>
            <CustomLTRPinInput
              length={6}
              {...form.getInputProps('otp')}
            />

            <Button type="submit" fullWidth mt="xl" loading={isLoading}>
              {t('verifyEmail')}
            </Button>
            <Button variant="subtle" onClick={handleResendOtp} loading={isResending} disabled={!userEmail}>
              {t('resendOtp')}
            </Button>
            {showOtpSentMessage && <Text c="blue" size="sm" mt="xs">{t('otpSentMessage')}</Text>}
          </Stack>
        </form>
        <Group justify="center" mt="lg">
          <Link href="/auth/login" passHref legacyBehavior>
            <Anchor component="a" c="dimmed" size="sm">
              {t('backToSignIn')}
            </Anchor>
          </Link>
        </Group>
      </Paper>
    </Container>
  );
}
