import { z } from 'zod';

// Common validation patterns
const VALIDATION = {
  WEIGHT: {
    MIN: 0.1,
    MAX: 1000,
    MESSAGE: 'Weight must be between 0.1 and 1000 kg',
  },
  DESCRIPTION: {
    MIN_LENGTH: 5,
    MAX_LENGTH: 500,
    MESSAGE: 'Description must be between 5 and 500 characters',
  },
  RECEIVER_NAME: {
    MIN_LENGTH: 3,
    MAX_LENGTH: 100,
    MESSAGE: 'Receiver name must be between 3 and 100 characters',
  },
  RECEIVER_PHONE: {
    // eslint-disable-next-line no-useless-escape
    PATTERN: /^[\+]?[\d\s\-\(\)]{7,20}$/,
    MESSAGE: 'Phone number must be a valid format (7-20 characters, may include +, spaces, dashes, parentheses)',
  },
  REASON: {
    MAX_LENGTH: 500,
    MESSAGE: 'Reason must be at most 500 characters',
  },
  NOTES: {
    MAX_LENGTH: 1000,
    MESSAGE: 'Notes must be at most 1000 characters',
  },
  QR_VALUE: {
    MIN_LENGTH: 5,
    MESSAGE: 'QR value must be at least 5 characters',
  },
  PHOTO_URL: {
    PATTERN: /^\/uploads\/photos\/.+\.(jpg|jpeg|png)$/i,
    MESSAGE: 'Photo URL must be a valid upload path ending with jpg, jpeg, or png',
  },
  COORDINATES: {
    LATITUDE: {
      MIN: -90,
      MAX: 90,
      MESSAGE: 'Latitude must be between -90 and 90',
    },
    LONGITUDE: {
      MIN: -180,
      MAX: 180,
      MESSAGE: 'Longitude must be between -180 and 180',
    },
  },
};

// Shipment size enum
const SHIPMENT_SIZE = ['SMALL', 'MEDIUM', 'LARGE', 'EXTRA_LARGE'] as const;
const SCAN_ACTION = ['DROPOFF', 'PICKUP', 'ARRIVAL'] as const;

// Create shipment schema - what the frontend sends (camelCase)
export const createShipmentApiRequestSchema = z.object({
  originAoId: z.string().uuid('Invalid origin access operator ID'),
  destAoId: z.string().uuid('Invalid destination access operator ID'),
  weight: z.number()
    .min(VALIDATION.WEIGHT.MIN, VALIDATION.WEIGHT.MESSAGE)
    .max(VALIDATION.WEIGHT.MAX, VALIDATION.WEIGHT.MESSAGE),
  size: z.enum(SHIPMENT_SIZE, { message: 'Invalid shipment size' }),
  description: z.string()
    .min(VALIDATION.DESCRIPTION.MIN_LENGTH, VALIDATION.DESCRIPTION.MESSAGE)
    .max(VALIDATION.DESCRIPTION.MAX_LENGTH, VALIDATION.DESCRIPTION.MESSAGE),
  receiverName: z.string()
    .min(VALIDATION.RECEIVER_NAME.MIN_LENGTH, VALIDATION.RECEIVER_NAME.MESSAGE)
    .max(VALIDATION.RECEIVER_NAME.MAX_LENGTH, VALIDATION.RECEIVER_NAME.MESSAGE),
  receiverPhone: z.string()
    .regex(VALIDATION.RECEIVER_PHONE.PATTERN, VALIDATION.RECEIVER_PHONE.MESSAGE),
});

// Backend create shipment schema - transforms frontend camelCase to backend snake_case
export const createShipmentBackendRequestSchema = createShipmentApiRequestSchema.transform((data) => ({
  origin_ao_id: data.originAoId,
  dest_ao_id: data.destAoId,
  weight: data.weight,
  size: data.size,
  description: data.description,
  receiver_name: data.receiverName,
  receiver_phone: data.receiverPhone,
}));

// Cancel shipment schema
export const cancelShipmentApiRequestSchema = z.object({
  reason: z.string()
    .max(VALIDATION.REASON.MAX_LENGTH, VALIDATION.REASON.MESSAGE)
    .optional(),
});

// Scan shipment schema (frontend camelCase)
export const scanShipmentApiRequestSchema = z.object({
  shipmentId: z.string().uuid('Invalid shipment ID'),
  qrValue: z.string()
    .min(VALIDATION.QR_VALUE.MIN_LENGTH, VALIDATION.QR_VALUE.MESSAGE),
  photoUrl: z.string(),
  action: z.enum(SCAN_ACTION, { message: 'Invalid scan action' }),
  notes: z.string()
    .max(VALIDATION.NOTES.MAX_LENGTH, VALIDATION.NOTES.MESSAGE)
    .optional(),
});

// Backend scan shipment schema - transforms frontend camelCase to backend snake_case
export const scanShipmentBackendRequestSchema = scanShipmentApiRequestSchema.transform((data) => ({
  shipment_id: data.shipmentId,
  qr_value: data.qrValue,
  photo_url: data.photoUrl,
  action: data.action,
  notes: data.notes,
}));

// Deliver shipment schema (frontend camelCase)
export const deliverShipmentApiRequestSchema = z.object({
  shipmentQr: z.string()
    .min(VALIDATION.QR_VALUE.MIN_LENGTH, VALIDATION.QR_VALUE.MESSAGE),
  pickupQr: z.string()
    .min(VALIDATION.QR_VALUE.MIN_LENGTH, VALIDATION.QR_VALUE.MESSAGE),
  photoUrl: z.string(),
  notes: z.string()
    .max(VALIDATION.NOTES.MAX_LENGTH, VALIDATION.NOTES.MESSAGE)
    .optional(),
});

// Backend deliver shipment schema - transforms frontend camelCase to backend snake_case
export const deliverShipmentBackendRequestSchema = deliverShipmentApiRequestSchema.transform((data) => ({
  shipment_qr: data.shipmentQr,
  pickup_qr: data.pickupQr,
  photo_url: data.photoUrl,
  notes: data.notes,
}));

// Legacy exports for backward compatibility
export const createShipmentSchema = createShipmentApiRequestSchema;
export const cancelShipmentSchema = cancelShipmentApiRequestSchema;
export const scanShipmentSchema = scanShipmentApiRequestSchema;
export const deliverShipmentSchema = deliverShipmentApiRequestSchema;

// Request schemas collection
export const shipmentRequestSchemas = {
  createShipment: createShipmentApiRequestSchema,
  createShipmentBackend: createShipmentBackendRequestSchema,
  cancelShipment: cancelShipmentApiRequestSchema,
  scanShipment: scanShipmentApiRequestSchema,
  scanShipmentBackend: scanShipmentBackendRequestSchema,
  deliverShipment: deliverShipmentApiRequestSchema,
  deliverShipmentBackend: deliverShipmentBackendRequestSchema,
};

export default shipmentRequestSchemas;
