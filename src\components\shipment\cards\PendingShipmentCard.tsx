/* eslint-disable sonarjs/cognitive-complexity */
/* eslint-disable no-nested-ternary */
/* eslint-disable complexity */
/* eslint-disable react/require-default-props */
import {
  Card,
  Text,
  Badge,
  Group,
  Stack,
  Button,
  Box,
  ThemeIcon,
  useMantineTheme,
  useMantineColorScheme,
} from '@mantine/core';
import {
  IconPackage,
  IconMapPin,
  IconClock,
  IconWeight,
  IconUser,
  IconTruck,
  IconCheck,
  IconAlertTriangle,
} from '@tabler/icons-react';
import { useRouter } from 'next/router';
import useTranslation from 'next-translate/useTranslation';
import { useIsClient } from '../../../hooks/useIsClient';
import { Shipment } from '../../../requests/shipment';
import { getExpiryInfo, shouldHighlightExpiry } from '../../../utils/shipmentExpiry';

interface ActionConfig {
  label: string;
  color: 'blue' | 'green' | 'orange' | 'red';
}

interface PendingShipmentCardProps {
  shipment: Shipment;
  onAction: (shipment: Shipment) => void;
  actionConfig: ActionConfig;
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'PENDING':
      return 'orange';
    case 'AWAITING_PICKUP':
      return 'blue';
    case 'IN_TRANSIT':
      return 'cyan';
    case 'ARRIVED_AT_DESTINATION':
      return 'grape';
    case 'DELIVERED':
      return 'green';
    case 'CANCELLED':
      return 'red';
    default:
      return 'gray';
  }
};

const getStatusLabel = (status: string, t: (key: string) => string) => {
  switch (status) {
    case 'PENDING':
      return t('statusPending');
    case 'AWAITING_PICKUP':
      return t('statusAwaitingPickup');
    case 'IN_TRANSIT':
      return t('statusInTransit');
    case 'ARRIVED_AT_DESTINATION':
      return t('statusArrivedAtDestination');
    case 'DELIVERED':
      return t('statusDelivered');
    case 'CANCELLED':
      return t('statusCancelled');
    default:
      return status;
  }
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'PENDING':
      return IconClock;
    case 'AWAITING_PICKUP':
      return IconPackage;
    case 'IN_TRANSIT':
      return IconTruck;
    case 'ARRIVED_AT_DESTINATION':
      return IconMapPin;
    case 'DELIVERED':
      return IconCheck;
    default:
      return IconPackage;
  }
};

const getSizeColor = (size: string) => {
  switch (size) {
    case 'SMALL':
      return 'green';
    case 'MEDIUM':
      return 'blue';
    case 'LARGE':
      return 'orange';
    case 'EXTRA_LARGE':
      return 'red';
    default:
      return 'gray';
  }
};

const getSizeLabel = (size: string, t: (key: string) => string) => {
  switch (size) {
    case 'SMALL':
      return t('sizeSmall');
    case 'MEDIUM':
      return t('sizeMedium');
    case 'LARGE':
      return t('sizeLarge');
    case 'EXTRA_LARGE':
      return t('sizeExtraLarge');
    default:
      return size;
  }
};

export default function PendingShipmentCard({
  shipment,
  onAction,
  actionConfig,
}: PendingShipmentCardProps) {
  const isClient = useIsClient();
  const router = useRouter();
  const { t } = useTranslation('shipments');
  const theme = useMantineTheme();
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === 'dark';
  const StatusIcon = getStatusIcon(shipment.status);
  const isRTL = router.locale === 'ar';

  const formatDate = (dateString: string) => {
    if (!isClient) return 'Loading...';
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
      });
    } catch {
      return 'Invalid date';
    }
  };

  const getCardBorderColor = () => {
    if (shipment.expiresAt && getExpiryInfo(shipment.expiresAt).isExpired) return theme.colors.red[isDark ? 5 : 4];
    if (shipment.expiresAt && shouldHighlightExpiry(shipment.expiresAt)) return theme.colors.orange[isDark ? 5 : 4];
    return undefined;
  };

  return (
    <Card
      withBorder
      p="md"
      radius="md"
      style={{
        borderColor: getCardBorderColor(),
        borderWidth: getCardBorderColor() ? '2px' : '1px',
        transition: 'all 0.2s ease',
        direction: isRTL ? 'rtl' : 'ltr',
      }}
      shadow="sm"
    >
      <Stack gap="sm">
        {/* Header */}
        <Group justify="space-between" align="center" style={{ direction: isRTL ? 'rtl' : 'ltr' }}>
          <Group gap="xs">
            <ThemeIcon
              size="lg"
              variant="light"
              color={getStatusColor(shipment.status)}
              radius="md"
            >
              <StatusIcon size="1.1rem" />
            </ThemeIcon>
            <Box>
              <Text fw={600} size="sm" c={isDark ? 'gray.1' : 'dark.7'} style={{ direction: isRTL ? 'rtl' : 'ltr' }}>
                #
                {shipment.id.slice(-8).toUpperCase()}
              </Text>
              <Text size="xs" c="dimmed" style={{ direction: isRTL ? 'rtl' : 'ltr' }}>
                {formatDate(shipment.createdAt)}
              </Text>
            </Box>
          </Group>

          <Badge
            color={getStatusColor(shipment.status)}
            variant="light"
            size="sm"
            radius="md"
          >
            {getStatusLabel(shipment.status, t)}
          </Badge>
        </Group>

        {/* Description */}
        <Text
          size="sm"
          fw={500}
          c={isDark ? 'gray.2' : 'dark.6'}
          lineClamp={2}
          title={shipment.description}
          style={{ direction: isRTL ? 'rtl' : 'ltr', textAlign: isRTL ? 'right' : 'left' }}
        >
          {shipment.description}
        </Text>

        {/* Package Info */}
        <Stack gap="xs">
          <Group gap="md" justify="space-between" style={{ direction: isRTL ? 'rtl' : 'ltr' }}>
            <Group gap="xs">
              <Group gap={4}>
                <IconWeight size="0.9rem" color={theme.colors.gray[5]} />
                <Text size="sm" c="dimmed" style={{ direction: isRTL ? 'rtl' : 'ltr' }}>
                  <Text component="span" size="xs" c="dimmed">
                    {t('weight')}
                    :
                  </Text>
                  {shipment.weight}
                  {t('kg')}
                </Text>
              </Group>

              <Group gap={4}>
                <Text size="xs" c="dimmed" style={{ direction: isRTL ? 'rtl' : 'ltr' }}>
                  {t('size')}
                  :
                  {' '}
                  <Badge
                    size="sm"
                    variant="light"
                    color={getSizeColor(shipment.size)}
                    radius="md"
                  >
                    {getSizeLabel(shipment.size, t)}
                  </Badge>
                </Text>
              </Group>
            </Group>

            <Group gap="xs" style={{ direction: isRTL ? 'rtl' : 'ltr' }}>
              <IconUser size="0.9rem" color={theme.colors.gray[5]} />
              <Text size="sm" c="dimmed" lineClamp={1} style={{ direction: isRTL ? 'rtl' : 'ltr' }}>
                <Text component="span" size="xs" c="dimmed">
                  {t('receiverName')}
                  :
                  {' '}
                  {' '}
                </Text>
                {shipment.receiverName}
              </Text>
            </Group>
          </Group>
        </Stack>

        {/* Expiry Warning */}
        {shipment.expiresAt && shouldHighlightExpiry(shipment.expiresAt) && (
          <Group gap="xs" align="center" style={{ direction: isRTL ? 'rtl' : 'ltr' }}>
            <IconAlertTriangle
              size="1rem"
              color={getExpiryInfo(shipment.expiresAt).isExpired ? theme.colors.red[6] : theme.colors.orange[6]}
            />
            <Text
              size="sm"
              fw={600}
              c={getExpiryInfo(shipment.expiresAt).isExpired ? 'red' : 'orange'}
              style={{ direction: isRTL ? 'rtl' : 'ltr' }}
            >
              {getExpiryInfo(shipment.expiresAt).timeRemaining}
            </Text>
            {getExpiryInfo(shipment.expiresAt).isExpired && (
              <Badge size="sm" color="red" variant="filled">{t('expired')}</Badge>
            )}
          </Group>
        )}

        {/* Action Button */}
        <Button
          variant="filled"
          color={actionConfig.color}
          size="sm"
          onClick={() => onAction(shipment)}
          fullWidth
        >
          {actionConfig.label}
        </Button>
      </Stack>
    </Card>
  );
}
