import type { NextApiRequest, NextApiResponse } from 'next';
import { getJwt } from '../../../src/utils';
import { BACKEND_API } from '../../../src/lib/axios';
import { API_ENDPOINT, HTTP_CODE, apiMethods } from '../../../src/data';
import { shipmentRequestSchemas } from '../../../src/requests/shipment';
import { deliverShipmentApiResponseSchema } from '../../../src/requests/shipment/response-transformer';
import { AxiosError } from 'axios';

function normalizeDeliverBody(body: Record<string, unknown>) {
  if (body.shipmentQr && body.pickupQr && body.photoUrl) return body;
  return {
    shipmentQr: body.shipmentQr || body.shipment_qr,
    pickupQr: body.pickupQr || body.pickup_qr,
    photoUrl: body.photoUrl || body.photo_url,
    notes: body.notes,
    geoLatitude: body.geoLatitude || body.geo_latitude,
    geoLongitude: body.geoLongitude || body.geo_longitude,
  };
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== apiMethods.POST) {
    res.setHeader('Allow', [apiMethods.POST]);
    return res.status(HTTP_CODE.METHOD_NOT_ALLOWED).json({
      success: false,
      message: `Method ${req.method} not allowed`,
      code: HTTP_CODE.METHOD_NOT_ALLOWED,
    });
  }

  try {
    const { token } = await getJwt(req);
    if (!token) {
      return res.status(HTTP_CODE.UNAUTHORIZED).json({
        success: false,
        message: 'Authentication required',
      });
    }
    const normalizedBody = normalizeDeliverBody(req.body);
    const validatedData = shipmentRequestSchemas.deliverShipment.parse(normalizedBody);
    const backendData = shipmentRequestSchemas.deliverShipmentBackend.parse(validatedData);
    const response = await BACKEND_API.post(API_ENDPOINT.shipments.deliver, backendData, {
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        Pragma: 'no-cache',
        Expires: '0',
      },
    });
    const validatedResponse = deliverShipmentApiResponseSchema.parse(response.data);
    return res.status(HTTP_CODE.SUCCESS).json(validatedResponse);
  } catch (error: unknown) {
    if (error instanceof AxiosError && error.response) {
      const { status, data } = error.response;
      if (status === 403) {
        return res.status(status).json({
          message: data?.error?.message || data?.message || 'Backend error',
          errors: data?.error?.details || [],
          backend: true,
        });
      }
      return res.status(status).json({
        code: status,
        message: data?.error?.message || data?.message || 'Backend error',
        errors: data?.error?.details || [],
        backend: true,
      });
    }
    return res.status(500).json({
      code: 500,
      message: 'Internal server error',
      errors: [],
    });
  }
}
