import { z } from 'zod';
import {
  updateProfileApiRequestSchema,
  updateProfileBackendRequestSchema,
} from './request-transformer';
import {
  userProfileSchema,
  userBackendSchema,
  getProfileApiResponseSchema,
  updateProfileApiResponseSchema,
  listProfilesApiResponseSchema,
  userProfileApiResponseSchema,
} from './response-transformer';
import { Pagination } from '../../types';

// Filter types for profile queries
export interface ProfileFilter {
  search?: string;
  userType?: string;
  status?: string;
  approved?: boolean | null;
  emailVerified?: boolean | null;
  createdAtGte?: string | null;
  createdAtLte?: string | null;
  updatedAtGte?: string | null;
  updatedAtLte?: string | null;
  hasLocation?: boolean | null;
  hasAccessPoints?: boolean | null;
}

// Query props interfaces following example pattern
export interface GetProfileQueryProps {
  enabled?: boolean;
  refetchOnWindowFocus?: boolean;
}

export interface GetProfilesQueryProps {
  populate?: {
    accessPoints?: boolean;
    // Add other populate options as needed
  };
  pagination?: Pagination;
  filters?: ProfileFilter;
  sort?: string;
  enabled?: boolean;
}

// Infer types from Zod schemas
export type UserProfile = z.infer<typeof userProfileSchema>;
export type UserBackend = z.infer<typeof userBackendSchema>;

// Request types
export type UpdateProfileApiRequest = z.infer<typeof updateProfileApiRequestSchema>;
export type UpdateProfileBackendRequest = z.infer<typeof updateProfileBackendRequestSchema>;

export interface UpdateProfileQueryProps {
  data: UpdateProfileApiRequest;
  enabled?: boolean;
}

// Response types
export type GetProfileApiResponse = z.infer<typeof getProfileApiResponseSchema>;
export type UpdateProfileApiResponse = z.infer<typeof updateProfileApiResponseSchema>;
export type ListProfilesApiResponse = z.infer<typeof listProfilesApiResponseSchema>;
export type UserProfileApiResponse = ReturnType<typeof userProfileApiResponseSchema>;

// Legacy types for backward compatibility
export type UpdateProfileRequest = UpdateProfileApiRequest;
