/* eslint-disable complexity */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable max-lines */
/* eslint-disable react/require-default-props */
import {
  <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>,
} from 'react-leaflet';
import { LatLngExpression, Icon } from 'leaflet';
import {
  Paper, Text, Button, Group, Stack, Alert,
  Box,
  Badge,
  useMantineColorScheme,
  useMantineTheme,
} from '@mantine/core';
import {
  IconMapPin, IconAlertCircle,
  IconCurrentLocation,
  IconFlag,
} from '@tabler/icons-react';
import useTranslation from 'next-translate/useTranslation';

const originIconSvg = `
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="green" width="36" height="36">
    <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
  </svg>
`;

const destinationIconSvg = `
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="red" width="36" height="36">
    <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
  </svg>
`;

const defaultIconSvg = `
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#3b82f6" width="24" height="24">
    <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
  </svg>
`;

// Custom icons for different marker types
const originIcon = new Icon({
  iconUrl: `data:image/svg+xml;base64,${btoa(originIconSvg)}`,
  iconSize: [36, 36],
  iconAnchor: [18, 36],
  popupAnchor: [0, -36],
});

const destinationIcon = new Icon({
  iconUrl: `data:image/svg+xml;base64,${btoa(destinationIconSvg)}`,
  iconSize: [36, 36],
  iconAnchor: [18, 36],
  popupAnchor: [0, -36],
});

const defaultIcon = new Icon({
  iconUrl: `data:image/svg+xml;base64,${btoa(defaultIconSvg)}`,
  iconSize: [24, 24],
  iconAnchor: [12, 24],
  popupAnchor: [0, -24],
});

interface AccessPoint {
  id: string;
  name: string;
  businessName?: string;
  address: string;
  geoLatitude: number;
  geoLongitude: number;
  phone?: string;
  email?: string;
}

interface CustomerAccessPointMapProps {
  accessPoints: AccessPoint[];
  selectedOriginId?: string | null;
  selectedDestinationId?: string | null;
  onOriginSelect: (accessPoint: AccessPoint) => void;
  onDestinationSelect: (accessPoint: AccessPoint) => void;
  isLoading: boolean;
}

export default function CustomerAccessPointMap({
  accessPoints,
  selectedOriginId,
  selectedDestinationId,
  onOriginSelect,
  onDestinationSelect,
  isLoading,
}: CustomerAccessPointMapProps) {
  const { t } = useTranslation('shipments');
  const mapCenter: LatLngExpression = [34.8021, 38.9968]; // Default to Syria
  const theme = useMantineTheme();
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === 'dark';

  if (isLoading) {
    return (
      <Paper style={{
        height: 500, display: 'flex', alignItems: 'center', justifyContent: 'center',
      }}
      >
        <Stack align="center">
          <IconMapPin size="2rem" />
          <Text>{t('loadingAccessPoints')}</Text>
        </Stack>
      </Paper>
    );
  }

  if (accessPoints.length === 0) {
    return (
      <Alert icon={<IconAlertCircle size="1rem" />} color="yellow" title={t('noAccessPoints')}>
        {t('noAccessPointsMessage')}
      </Alert>
    );
  }

  return (
    <Paper withBorder style={{ height: 500, overflow: 'hidden' }}>
      <MapContainer {...{ center: mapCenter, zoom: 6, style: { height: '100%', width: '100%' } } as any}>
        <TileLayer
          {...{
            url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
          } as any}
        />

        {accessPoints.map((ap) => {
          const isOrigin = ap.id === selectedOriginId;
          const isDestination = ap.id === selectedDestinationId;
          let icon = defaultIcon;
          if (isOrigin) {
            icon = originIcon;
          }
          if (isDestination) {
            icon = destinationIcon;
          }

          return (
            <Marker
              key={ap.id}
              position={[ap.geoLatitude, ap.geoLongitude]}
              {...{ icon } as any}
            >
              <Popup
                {...{
                  className: isDark ? 'dark-popup' : 'light-popup',
                } as any}
              >
                <Box
                  style={{
                    minWidth: 380,
                    background: isDark ? theme.colors.dark[6] : theme.white,
                    border: `1px solid ${isDark ? theme.colors.dark[4] : theme.colors.gray[2]}`,
                    borderRadius: theme.radius.md,
                    padding: theme.spacing.sm,
                  }}
                >
                  <Stack gap="sm">
                    {/* Header */}
                    <Box>
                      <Group gap="xs" mb="xs">
                        {isOrigin && (
                        <Badge color="green" variant="filled" size="xs" radius="sm">
                          {t('origin')}
                        </Badge>
                        )}
                        {isDestination && (
                        <Badge color="red" variant="filled" size="xs" radius="sm">
                          {t('destination')}
                        </Badge>
                        )}
                      </Group>
                      <Text fw={600} size="sm" c={isDark ? 'gray.1' : 'dark.8'} mb={4}>
                        {ap.businessName || ap.name}
                      </Text>
                      <Text size="xs" c="dimmed" lineClamp={2}>
                        {ap.address}
                      </Text>
                    </Box>

                    {/* Contact Info */}
                    {(ap.phone || ap.email) && (
                    <Box
                      p="xs"
                      style={{
                        background: isDark ? theme.colors.dark[7] : theme.colors.gray[0],
                        borderRadius: theme.radius.sm,
                        border: `1px solid ${isDark ? theme.colors.dark[5] : theme.colors.gray[2]}`,
                      }}
                    >
                      {ap.phone && (
                        <Text size="xs" c="dimmed">
                          📞
                          {' '}
                          {ap.phone}
                        </Text>
                      )}
                      {ap.email && (
                        <Text size="xs" c="dimmed">
                          ✉️
                          {' '}
                          {ap.email}
                        </Text>
                      )}
                    </Box>
                    )}

                    {/* Action Buttons */}
                    <Group gap="xs" grow>
                      <Button
                        size="xs"
                        variant={isOrigin ? 'filled' : 'light'}
                        color="green"
                        onClick={() => onOriginSelect(ap)}
                        disabled={isOrigin}
                        leftSection={<IconCurrentLocation size="0.8rem" />}
                        radius="md"
                        style={{
                          transition: 'all 0.2s ease',
                        }}
                      >
                        {isOrigin ? t('originSet') : t('setOrigin')}
                      </Button>
                      <Button
                        size="xs"
                        variant={isDestination ? 'filled' : 'light'}
                        color="red"
                        onClick={() => onDestinationSelect(ap)}
                        disabled={isDestination}
                        leftSection={<IconFlag size="0.8rem" />}
                        radius="md"
                        style={{
                          transition: 'all 0.2s ease',
                        }}
                      >
                        {isDestination ? t('destinationSet') : t('setDestination')}
                      </Button>
                    </Group>
                  </Stack>
                </Box>
              </Popup>
            </Marker>
          );
        })}
      </MapContainer>
    </Paper>
  );
}
