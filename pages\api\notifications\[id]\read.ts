import { NextApiRequest, NextApiResponse } from 'next';
import { getJwt } from '../../../../src/utils';
import { BACKEND_API } from '../../../../src/lib/axios';
import { HTTP_CODE } from '../../../../src/data';
import { validateMarkAsReadResponse } from '../../../../src/requests/notifications';
import createApiError from '../../../../src/utils/create-api-error';

const apiMethods = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
} as const;

async function handleMarkAsRead(req: NextApiRequest, res: NextApiResponse, token: string) {
  try {
    const { id } = req.query;

    if (!id || typeof id !== 'string') {
      return res.status(HTTP_CODE.BAD_REQUEST).json(
        createApiError('Notification ID is required', 'VALIDATION_ERROR'),
      );
    }

    const response = await BACKEND_API.put(`/notifications/${id}/read`, {}, {
      headers: {
        Authorization: token,
      },
    });

    // Validate backend response
    const validatedResponse = validateMarkAsReadResponse(response.data);

    return res.status(HTTP_CODE.SUCCESS).json(validatedResponse);
  } catch (error: unknown) {
    if (error && typeof error === 'object' && 'response' in error) {
      const apiError = error as { response?: { status?: number; data?: unknown } };
      return res.status(apiError.response?.status || HTTP_CODE.INTERNAL_SERVER_ERROR).json(
        apiError.response?.data || createApiError('Backend API error', 'BACKEND_ERROR'),
      );
    }

    return res.status(HTTP_CODE.INTERNAL_SERVER_ERROR).json(
      createApiError('Failed to mark notification as read', 'INTERNAL_ERROR'),
    );
  }
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { method } = req;

  try {
    const { token } = await getJwt(req);
    if (!token) {
      return res.status(HTTP_CODE.UNAUTHORIZED).json(
        createApiError('Authentication required', 'UNAUTHORIZED'),
      );
    }

    if (method === apiMethods.PUT) {
      return await handleMarkAsRead(req, res, token as string);
    }

    return res.status(HTTP_CODE.METHOD_NOT_ALLOWED).json(
      createApiError(`Method ${method} not allowed`, 'METHOD_NOT_ALLOWED'),
    );
  } catch (error) {
    return res.status(HTTP_CODE.INTERNAL_SERVER_ERROR).json(
      createApiError('Internal server error', 'INTERNAL_ERROR'),
    );
  }
}
