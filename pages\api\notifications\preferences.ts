import { NextApiRequest, NextApiResponse } from 'next';
import { ZodError } from 'zod';
import { getJwt } from '../../../src/utils';
import { BACKEND_API } from '../../../src/lib/axios';
import { HTTP_CODE } from '../../../src/data';
import {
  updatePreferencesRequestSchema,
  validateGetPreferencesResponse,
  validateUpdatePreferencesResponse,
} from '../../../src/requests/notifications';
import createApiError from '../../../src/utils/create-api-error';

const apiMethods = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
} as const;

async function handleGetPreferences(_req: NextApiRequest, res: NextApiResponse, token: string) {
  try {
    const response = await BACKEND_API.get('/notifications/preferences', {
      headers: {
        Authorization: token,
      },
    });

    const validatedResponse = validateGetPreferencesResponse(response.data);

    return res.status(HTTP_CODE.SUCCESS).json(validatedResponse);
  } catch (error: unknown) {
    if (error && typeof error === 'object' && 'response' in error) {
      const apiError = error as { response?: { status?: number; data?: unknown } };
      return res.status(apiError.response?.status || HTTP_CODE.INTERNAL_SERVER_ERROR).json(
        apiError.response?.data || createApiError('Backend API error', 'BACKEND_ERROR'),
      );
    }

    return res.status(HTTP_CODE.INTERNAL_SERVER_ERROR).json(
      createApiError('Failed to fetch notification preferences', 'INTERNAL_ERROR'),
    );
  }
}

async function handleUpdatePreferences(req: NextApiRequest, res: NextApiResponse, token: string) {
  try {
    const validatedData = updatePreferencesRequestSchema.parse(req.body);

    const response = await BACKEND_API.put('/notifications/preferences', validatedData, {
      headers: {
        Authorization: token,
      },
    });

    const validatedResponse = validateUpdatePreferencesResponse(response.data);

    return res.status(HTTP_CODE.SUCCESS).json(validatedResponse);
  } catch (error: unknown) {
    if (error instanceof ZodError) {
      return res.status(HTTP_CODE.BAD_REQUEST).json(
        createApiError('Invalid request data', 'VALIDATION_ERROR', error.errors),
      );
    }

    if (error && typeof error === 'object' && 'response' in error) {
      const apiError = error as { response?: { status?: number; data?: unknown } };
      return res.status(apiError.response?.status || HTTP_CODE.INTERNAL_SERVER_ERROR).json(
        apiError.response?.data || createApiError('Backend API error', 'BACKEND_ERROR'),
      );
    }

    return res.status(HTTP_CODE.INTERNAL_SERVER_ERROR).json(
      createApiError('Failed to update notification preferences', 'INTERNAL_ERROR'),
    );
  }
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { method } = req;

  try {
    const { token } = await getJwt(req);
    if (!token) {
      return res.status(HTTP_CODE.UNAUTHORIZED).json(
        createApiError('Authentication required', 'UNAUTHORIZED'),
      );
    }

    switch (method) {
      case apiMethods.GET:
        return await handleGetPreferences(req, res, token as string);

      case apiMethods.PUT:
        return await handleUpdatePreferences(req, res, token as string);

      case apiMethods.POST:
      default:
        return res.status(HTTP_CODE.METHOD_NOT_ALLOWED).json(
          createApiError(`Method ${method} not allowed`, 'METHOD_NOT_ALLOWED'),
        );
    }
  } catch (error) {
    if (error instanceof ZodError) {
      return res.status(HTTP_CODE.BAD_REQUEST).json(
        createApiError('Invalid request data', 'VALIDATION_ERROR', error.errors),
      );
    }

    return res.status(HTTP_CODE.INTERNAL_SERVER_ERROR).json(
      createApiError('Internal server error', 'INTERNAL_ERROR'),
    );
  }
}
