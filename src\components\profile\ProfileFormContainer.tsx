import { Stack, Divider } from '@mantine/core';
import { UseFormReturnType } from '@mantine/form';
import ProfileHeader from './ProfileHeader';
import BasicInformationSection from './BasicInformationSection';
import AccessOperatorSection from './AccessOperatorSection';
import CarOperatorSection from './CarOperatorSection';
import ProfileActionButtons from './ProfileActionButtons';
import type { GeoLocation, ProfileFormValues } from './types';

interface ProfileFormContainerProps {
  form: UseFormReturnType<ProfileFormValues>;
  isEditing: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  user: any;
  userType: string;
  geoLocation: GeoLocation;
  transitPointIds: string[];
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  accessPointsData: any[];
  onLocationChange: (lat: number, lng: number) => void;
  onTransitPointsChange: (pointIds: string[]) => void;
  onEdit: () => void;
  onCancel: () => void;
  isLoading: boolean;
  onSubmit: (event: React.FormEvent) => void;
}

export default function ProfileFormContainer({
  form,
  isEditing,
  user,
  userType,
  onSubmit,
  geoLocation,
  transitPointIds,
  accessPointsData,
  onLocationChange,
  onTransitPointsChange,
  onEdit,
  onCancel,
  isLoading,
}: ProfileFormContainerProps) {
  return (
    <>
      <ProfileHeader user={user} userType={userType} />

      <form
        onSubmit={onSubmit}
        noValidate // Disable browser validation to use custom validation
      >
        <Stack gap="md">
          {/* Basic Information */}
          <BasicInformationSection
            form={form}
            isEditing={isEditing}
            user={user}
          />

          {/* Access Operator specific fields */}
          {userType === 'ACCESS_OPERATOR' && (
            <>
              <Divider />
              <AccessOperatorSection
                form={form}
                isEditing={isEditing}
                geoLocation={geoLocation}
                onLocationChange={onLocationChange}
              />
            </>
          )}

          {/* Car Operator specific fields */}
          {userType === 'CAR_OPERATOR' && (
            <>
              <Divider />
              <CarOperatorSection
                form={form}
                isEditing={isEditing}
                accessPointsData={accessPointsData}
                transitPointIds={transitPointIds}
                onTransitPointsChange={onTransitPointsChange}
              />
            </>
          )}

          {/* Action buttons */}
          <ProfileActionButtons
            isEditing={isEditing}
            isLoading={isLoading}
            onEdit={onEdit}
            onCancel={onCancel}
            onReset={form.reset}
          />
        </Stack>
      </form>
    </>
  );
}
