import { API_ENDPOINT, HTTP_CODE } from '../../data';
import { CLIENT_API } from '../../lib/axios';
import { handleApiError } from '../../utils/handle-backend-error';
import { QueryClient } from '@tanstack/react-query';
import { CacheInvalidationManager, QUERY_KEYS } from '../cache-invalidation';
import {
  GetAccessPointQueryProps,
  GetAccessPointsQueryProps,
  CreateAccessPointQueryProps,
  UpdateAccessPointQueryProps,
} from './types';

// Use centralized query keys
const queryKeys = QUERY_KEYS.accessPoints;

/**
 * @description This function calls to get single access point data by ID.
 * @param props
 * @returns access point data
 */
const getAccessPointRequest = (props: GetAccessPointQueryProps) => {
  const { id } = props;
  return CLIENT_API.get(API_ENDPOINT.accessPoints.byId(id))
    .then((res) => res?.data)
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

/**
 * @description This function calls to get multiple access points.
 * @param props
 * @returns list of access points with pagination
 */
const getAccessPointsRequest = (props: GetAccessPointsQueryProps) => {
  const {
    pagination, filters, sort,
  } = props;

  // Build query parameters following the same pattern as profile requests
  const queryParams: Record<string, string | undefined> = {
    page: pagination?.page?.toString(),
    limit: pagination?.pageSize?.toString(),

    search: filters?.search || undefined,

    sortBy: typeof sort === 'string' ? sort.split(':')[0] : undefined,
    sortOrder: typeof sort === 'string' ? sort.split(':')[1] || 'asc' : undefined,

    'filter[approved]': filters?.approved?.toString(),
    'filter[status]': filters?.status || undefined,
    'filter[createdAtGte]': filters?.createdAtGte || undefined,
    'filter[createdAtLte]': filters?.createdAtLte || undefined,
    'filter[updatedAtGte]': filters?.updatedAtGte || undefined,
    'filter[updatedAtLte]': filters?.updatedAtLte || undefined,
    'filter[hasLocation]': filters?.hasLocation?.toString(),
  };

  // Remove undefined values
  const cleanParams = Object.entries(queryParams).reduce((acc, [key, value]) => {
    if (value !== undefined) {
      acc[key] = value;
    }
    return acc;
  }, {} as Record<string, string>);

  return CLIENT_API.get(API_ENDPOINT.accessPoints.list, {
    params: cleanParams,
  })
    .then((res) => res?.data)
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

/**
 * @description This function calls to create a new access point.
 * @param props
 * @returns created access point data
 */
const createAccessPointRequest = (props: CreateAccessPointQueryProps) => {
  const { data } = props;
  return CLIENT_API.post(API_ENDPOINT.accessPoints.create, data)
    .then((res) => res?.data)
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

/**
 * @description This function calls to update an access point.
 * @param props
 * @returns updated access point data
 */
const updateAccessPointRequest = (props: UpdateAccessPointQueryProps) => {
  const { data } = props;
  return CLIENT_API.put(API_ENDPOINT.accessPoints.update, data)
    .then((res) => res?.data)
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

// Get single access point query function
export const getAccessPointQuery = (props: GetAccessPointQueryProps) => ({
  queryKey: [queryKeys.single, props.id],
  queryFn: () => getAccessPointRequest(props),
  refetchOnWindowFocus: props?.refetchOnWindowFocus ?? false,
  enabled: props?.enabled && !!props.id,
  retry: (failureCount: number, error: { code: HTTP_CODE }) => error?.code !== HTTP_CODE.UNAUTHORIZED,
});

// Get multiple access points query function
export const getAccessPointsQuery = (props?: GetAccessPointsQueryProps) => ({
  queryKey: [
    queryKeys.list,
    props?.filters,
    props?.sort,
    props?.pagination,
  ],
  queryFn: () => getAccessPointsRequest(props || {}),
  refetchOnWindowFocus: false,
  enabled: props?.enabled,
  retry: (failureCount: number, error: { code: HTTP_CODE }) => error?.code !== HTTP_CODE.UNAUTHORIZED,
});

// Create access point query function (for React Query useMutation)
export const createAccessPointQuery = (props: CreateAccessPointQueryProps) => ({
  queryKey: [queryKeys.create],
  queryFn: () => createAccessPointRequest(props),
  refetchOnWindowFocus: false,
  enabled: props?.enabled,
});

// Update access point query function (for React Query useMutation)
export const updateAccessPointQuery = (props: UpdateAccessPointQueryProps) => ({
  queryKey: [queryKeys.update],
  queryFn: () => updateAccessPointRequest(props),
  refetchOnWindowFocus: false,
  enabled: props?.enabled,
});

// Create access point mutation function
export const createAccessPointMutation = () => ({
  mutationKey: [queryKeys.create],
  mutationFn: (props: CreateAccessPointQueryProps) => createAccessPointRequest(props),
});

// Update access point mutation function
export const updateAccessPointMutation = () => ({
  mutationKey: [queryKeys.update],
  mutationFn: (props: UpdateAccessPointQueryProps) => updateAccessPointRequest(props),
});

/**
 * Enhanced mutation functions with built-in cache invalidation
 */

// Create access point mutation with auto-invalidation
export const createAccessPointMutationWithInvalidation = (queryClient: QueryClient) => {
  const cacheManager = new CacheInvalidationManager(queryClient);
  return {
    ...createAccessPointMutation(),
    onSuccess: async () => {
      await cacheManager.invalidateAfterAccessPointMutation();
    },
  };
};

// Update access point mutation with auto-invalidation
export const updateAccessPointMutationWithInvalidation = (queryClient: QueryClient) => {
  const cacheManager = new CacheInvalidationManager(queryClient);
  return {
    ...updateAccessPointMutation(),
    onSuccess: async () => {
      await cacheManager.invalidateAfterAccessPointMutation();
    },
  };
};

// Aliases for backward compatibility
export const getAccessOperatorQuery = getAccessPointQuery;
export const getAccessOperatorsQuery = getAccessPointsQuery;
export const createAccessOperatorQuery = createAccessPointQuery;
export const updateAccessOperatorQuery = updateAccessPointQuery;
export const createAccessOperatorMutation = createAccessPointMutation;
export const updateAccessOperatorMutation = updateAccessPointMutation;
