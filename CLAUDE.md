# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Essential Commands
```bash
yarn dev          # Start development server on port 8001
yarn build        # Build production version
yarn start        # Start production server
yarn lint         # Run ESLint
yarn type-check   # Run TypeScript type checking (strict mode)
yarn test         # Run Jest tests
```

### Development Port
The application runs on port 8001 in development mode (configured in package.json).

## Architecture Overview

### Tech Stack
- **Framework**: Next.js 15.2.3 with React 19
- **UI Library**: Mantine 8.0.2 (complete UI component system)
- **Authentication**: NextAuth.js 4.22.1
- **State Management**: Zustand for client state, TanStack Query for server state
- **Maps**: React Leaflet for map components
- **Internationalization**: next-translate with Arabic (default) and English support
- **Styling**: PostCSS with <PERSON><PERSON>'s preset
- **Testing**: Jest with Testing Library
- **Package Manager**: Yarn 4.9.1

### Application Structure

This is a logistics/shipment management system called NAQALAT with three main user roles:

#### User Roles
1. **Customer**: Creates and tracks shipments
2. **Access Operator (AO)**: Manages shipments at access points
3. **Car Operator (CO)**: Handles shipment transportation

#### Key Features
- Multi-role dashboard system
- Real-time shipment tracking with QR codes
- Interactive maps for access points and routes
- Comprehensive notification system
- Bilingual support (Arabic RTL/English LTR)
- Mobile-responsive design

### Directory Structure

```
src/
├── components/           # Reusable UI components
│   ├── common/          # Shared components (DataTable, QRScanner, etc.)
│   ├── layouts/         # Page layouts
│   ├── maps/           # Map-related components (Leaflet)
│   ├── notifications/   # Notification system
│   ├── profile/        # User profile components
│   ├── shipment/       # Shipment management components
│   └── dashboard/      # Role-specific dashboard components
├── hooks/              # Custom React hooks
├── requests/           # API layer with TanStack Query
│   ├── auth/          # Authentication requests
│   ├── shipment/      # Shipment CRUD operations
│   ├── notifications/ # Notification management
│   └── profile/       # User profile operations
├── utils/             # Utility functions
├── data/              # Constants, routes, API endpoints
└── types/             # TypeScript type definitions

pages/                  # Next.js pages (file-based routing)
├── api/               # API routes
├── auth/              # Authentication pages
├── dashboard/         # Role-specific dashboards
├── customer/          # Customer-specific pages
├── access-operator/   # AO-specific pages
└── car-operator/      # CO-specific pages

locales/               # Translation files
├── ar/               # Arabic translations
└── en/               # English translations
```

### Request Architecture

The application uses a structured API layer:
- **requests/**: Organized by domain (auth, shipment, notifications, profile)
- **TanStack Query**: For server state management with caching
- **Axios instances**: Separate for client API routes and backend API
- **Type-safe**: Full TypeScript coverage with request/response schemas

### Internationalization

- **Default locale**: Arabic (RTL)
- **Supported locales**: Arabic (`ar`), English (`en`)
- **Translation files**: Located in `locales/{locale}/{namespace}.json`
- **Fonts**: Cairo for Arabic, Roboto Condensed for English
- **Direction**: Automatic RTL/LTR switching based on locale

### Authentication & Authorization

- **NextAuth.js**: Session management
- **Role-based routing**: Different dashboards per user role
- **Protected routes**: Defined in `src/data/routes.ts`
- **Session provider**: Wraps entire application

### State Management

- **Server state**: TanStack Query with 5-minute stale time
- **Client state**: Zustand stores
- **Form state**: Mantine Form for complex forms
- **Notifications**: Context-based notification system

### Map Integration

- **Library**: React Leaflet
- **Features**: Access point mapping, route visualization, shipment tracking
- **Icons**: Custom Leaflet icon configuration
- **Z-index management**: Maps positioned below UI elements

## Development Guidelines

### Code Organization
- Components are organized by feature/domain
- Each major feature has its own directory with exports via index.ts
- Hooks are centralized in `src/hooks/`
- Utils are domain-agnostic helper functions

### API Integration
- Use existing TanStack Query hooks from `src/requests/`
- Follow the domain-based organization pattern
- Add new endpoints to `src/data/api-endpoints.ts`

### Styling
- Use Mantine components as primary UI system
- Global styles are in `pages/_app.tsx`
- Responsive design with mobile-first approach
- Theme switching support (light/dark)

### Type Safety
- Run `yarn type-check` before commits
- All API responses should have corresponding TypeScript types
- Use strict TypeScript configuration

### Testing
- Jest configuration with jsdom environment
- Testing Library for component testing
- Test files should be placed in `__tests__` directories

### Maintenance Mode
The application supports maintenance mode via `NEXT_PUBLIC_MAINTENANCE_MODE` environment variable.