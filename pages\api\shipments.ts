import { NextApiRequest, NextApiResponse } from 'next';
import { ZodError } from 'zod';
import { getJwt } from '../../src/utils';
import { BACKEND_API } from '../../src/lib/axios';
import { HTTP_CODE } from '../../src/data';
import {
  returnShipmentParams,
  shipmentRequestSchemas,
} from '../../src/requests/shipment';
import {
  listShipmentsApiResponseSchema,
  createShipmentApiResponseSchema,
} from '../../src/requests/shipment/response-transformer';
import createApiError from '../../src/utils/create-api-error';

const apiMethods = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
} as const;

async function handleGet(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Set cache control headers to prevent caching
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');

    // Verify authentication
    const { token } = await getJwt(req);
    if (!token) {
      return res.status(HTTP_CODE.UNAUTHORIZED).json({
        success: false,
        message: 'Authentication required',
      });
    }

    // Get query parameters with validation
    const params = returnShipmentParams(req);

    // Make request to backend
    const response = await BACKEND_API.get('/shipments', {
      headers: {
        Authorization: token,
      },
      params,
    });

    // Validate and transform response using schema
    const validatedResponse = listShipmentsApiResponseSchema.parse(response.data);
    return res.status(HTTP_CODE.SUCCESS).json(validatedResponse);
  } catch (error: unknown) {
    const apiError = createApiError({ error });
    return res.status(apiError.code).json(apiError);
  }
}

async function handlePost(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Set cache control headers to prevent caching
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');

    // Verify authentication
    const { token } = await getJwt(req);
    if (!token) {
      return res.status(HTTP_CODE.UNAUTHORIZED).json({
        success: false,
        message: 'Authentication required',
      });
    }

    // Validate and transform request body for backend
    const validatedData = shipmentRequestSchemas.createShipmentBackend.parse(req.body);

    // Make request to backend
    const response = await BACKEND_API.post('/shipments', validatedData, {
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });

    // Validate and transform response using schema
    const validatedResponse = createShipmentApiResponseSchema.parse(response.data);
    return res.status(HTTP_CODE.CREATED).json(validatedResponse);
  } catch (error: unknown) {
    if (error instanceof ZodError) {
      return res.status(HTTP_CODE.BAD_REQUEST).json({
        success: false,
        message: 'Validation failed',
        errors: error.errors,
      });
    }

    const apiError = createApiError({ error });
    return res.status(apiError.code).json(apiError);
  }
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    switch (req.method) {
      case apiMethods.GET:
        return await handleGet(req, res);
      case apiMethods.POST:
        return await handlePost(req, res);
      default:
        return res.status(HTTP_CODE.METHOD_NOT_ALLOWED).json({
          success: false,
          message: `Method ${req.method} not allowed`,
        });
    }
  } catch (error: unknown) {
    const apiError = createApiError({ error });
    return res.status(apiError.code).json(apiError);
  }
}
