import { NextApiRequest, NextApiResponse } from 'next';
import { ZodError } from 'zod';
import { getJwt } from '../../../../src/utils';
import { BACKEND_API } from '../../../../src/lib/axios';
import { API_ENDPOINT, HTTP_CODE } from '../../../../src/data';
import { shipmentRequestSchemas } from '../../../../src/requests/shipment';
import createApiError from '../../../../src/utils/create-api-error';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    if (req.method !== 'POST') {
      return res.status(HTTP_CODE.METHOD_NOT_ALLOWED).json({
        success: false,
        message: `Method ${req.method} not allowed`,
      });
    }

    const { token } = await getJwt(req);
    if (!token) {
      return res.status(HTTP_CODE.UNAUTHORIZED).json({
        success: false,
        message: 'Authentication required',
      });
    }

    // Get shipment ID from URL
    const { id } = req.query;
    if (!id || typeof id !== 'string') {
      return res.status(HTTP_CODE.BAD_REQUEST).json({
        success: false,
        message: 'Shipment ID is required',
      });
    }

    const validatedData = shipmentRequestSchemas.cancelShipment.parse(req.body);

    const response = await BACKEND_API.post(API_ENDPOINT.shipments.cancel(id), validatedData, {
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });

    return res.status(HTTP_CODE.SUCCESS).json(response.data);
  } catch (error: unknown) {
    if (error instanceof ZodError) {
      return res.status(HTTP_CODE.BAD_REQUEST).json({
        success: false,
        message: 'Validation failed',
        errors: error.errors,
      });
    }

    const apiError = createApiError({ error });
    return res.status(apiError.code).json(apiError);
  }
}
