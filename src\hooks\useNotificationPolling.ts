import { useEffect, useRef, useCallback } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useSession } from 'next-auth/react';
import { notifications } from '@mantine/notifications';
import { QUERY_KEYS } from '../requests/cache-invalidation';
import { getUnreadCountQuery } from '../requests/notifications';

type Timeout = ReturnType<typeof setTimeout>;

interface UseNotificationPollingOptions {
  /**
   * Polling interval in milliseconds
   * @default 30000 (30 seconds)
   */
  interval?: number;

  /**
   * Whether to enable polling
   * @default true
   */
  enabled?: boolean;

  /**
   * Whether to show toast notifications for new notifications
   * @default true
   */
  showToasts?: boolean;

  /**
   * Callback when new notifications are detected
   */
  onNewNotifications?: (newCount: number, previousCount: number) => void;
}

export function useNotificationPolling(options: UseNotificationPollingOptions = {}) {
  const {
    interval = 30000,
    enabled = true,
    showToasts = true,
    onNewNotifications,
  } = options;

  const { data: session } = useSession();
  const queryClient = useQueryClient();
  const previousCountRef = useRef<number>(0);
  const intervalRef = useRef<Timeout | null>(null);
  const isDocumentVisibleRef = useRef(true);

  const {
    data: unreadCount = 0,
    refetch,
    isLoading,
  } = useQuery({
    ...getUnreadCountQuery({ enabled: enabled && !!session }),
    refetchInterval: false,
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
  });

  const handleNewNotifications = useCallback((newCount: number, previousCount: number) => {
    const newNotificationsCount = newCount - previousCount;

    if (newNotificationsCount > 0) {
      // Show toast notification
      if (showToasts) {
        notifications.show({
          title: 'New Notification',
          message: newNotificationsCount === 1
            ? 'You have 1 new notification'
            : `You have ${newNotificationsCount} new notifications`,
          color: 'blue',
          autoClose: 5000,
        });
      }

      onNewNotifications?.(newCount, previousCount);

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.notifications.list],
      });
    }
  }, [showToasts, onNewNotifications, queryClient]);

  const poll = useCallback(async () => {
    if (!enabled || !session || !isDocumentVisibleRef.current) {
      return;
    }

    try {
      const result = await refetch();
      const newCount = result.data || 0;
      const previousCount = previousCountRef.current;

      if (newCount !== previousCount) {
        handleNewNotifications(newCount, previousCount);
        previousCountRef.current = newCount;
      }
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.error('Notification polling error:', error);
      }
    }
  }, [enabled, session, refetch, handleNewNotifications]);

  useEffect(() => {
    const handleVisibilityChange = (): void => {
      isDocumentVisibleRef.current = !document.hidden;

      if (!document.hidden && enabled && session) {
        poll().catch(() => {
          // Handle errors silently
        });
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [poll, enabled, session]);

  useEffect(() => {
    if (!enabled || !session) {
      return undefined;
    }

    if (unreadCount !== undefined) {
      previousCountRef.current = unreadCount;
    }

    intervalRef.current = setInterval(() => {
      poll().catch(() => {
        // Handle errors silently
      });
    }, interval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [enabled, session, poll, interval, unreadCount]);

  useEffect(() => {
    if (!session) {
      previousCountRef.current = 0;
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }
  }, [session]);

  return {
    unreadCount,
    isLoading,
    refetch: poll,
  };
}

export default useNotificationPolling;
