import { useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import { useIsClient } from '../../../src/hooks/useIsClient';

/**
 * Index page for CO shipments - redirects to transported shipments
 * This ensures the menu structure works properly
 */
export default function COShipmentsIndexPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const isClient = useIsClient();

  useEffect(() => {
    if (isClient && status === 'authenticated') {
      if (session?.user?.user_type === 'CAR_OPERATOR') {
        // Redirect CO users to transported shipments
        router.replace('/car-operator/shipments/my');
      } else {
        // Redirect non-CO users to home
        router.replace('/');
      }
    } else if (isClient && status === 'unauthenticated') {
      // Redirect unauthenticated users to login
      router.replace('/auth/login');
    }
  }, [isClient, status, session, router]);

  // Show nothing while redirecting
  return null;
}
